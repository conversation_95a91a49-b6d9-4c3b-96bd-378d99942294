import { AllDataNodes, OneOfDataNodes, OneOfElementPropertyKeys } from './nodes';

export enum ElementChangeType {
    /** Adds to snapshot history */
    Instant = 'instant',
    /** Skips adding to snapshot history */
    Skip = 'skip',
    /** Always add to snapshot history, even if element has not changed */
    Force = 'force',
    /** Debounce value change */
    Burst = 'burst'
}

export type Changes = Partial<AllDataNodes>;

export interface IElementChange {
    element: OneOfDataNodes | undefined;
    changes: Changes;
}

export interface IFilterOptions {
    /**
     * Speficic element to filter for. Any other elements
     * will stop emission.
     */
    explicitElement: OneOfDataNodes;

    /**
     * Specific properties to filter for. Any other properties
     * will stop emission.
     */
    explicitProperties: OneOfElementPropertyKeys[];
}
