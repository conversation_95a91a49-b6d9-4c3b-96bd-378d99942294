export default {
    "openapi": "3.0.4",
    "info": {
        "title": "DesignApi.Api",
        "version": "1.0"
    },
    "paths": "redacted",
    "components": {
        "schemas": {
            "ActionDto": {
                "required": [
                    "disabled",
                    "id",
                    "operations",
                    "triggers"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "triggers": {
                        "type": "array",
                        "items": {
                            "enum": [
                                "click",
                                "mousedown",
                                "mouseup",
                                "mouseenter",
                                "mouseleave",
                                "touchstart",
                                "touchend",
                                "touchcancel"
                            ],
                            "type": "string"
                        }
                    },
                    "disabled": {
                        "type": "boolean"
                    },
                    "templateId": {
                        "enum": [
                            "reserved-pressed",
                            "reserved-hover"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "operations": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/OperationDto"
                        }
                    },
                    "conditions": {
                        "type": "array",
                        "items": {},
                        "nullable": true
                    },
                    "preventClickThrough": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "AnimationDto": {
                "required": [
                    "hidden",
                    "id",
                    "keyframes",
                    "name",
                    "timingFunction"
                ],
                "type": "object",
                "properties": {
                    "keyframes": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/KeyframeDto"
                        }
                    },
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "type": {
                        "enum": [
                            "in",
                            "out",
                            "repeating",
                            "action",
                            "keyframe"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "timingFunction": {
                        "enum": [
                            "linear",
                            "easeInExpo",
                            "easeOutExpo",
                            "easeInOutExpo",
                            "easeInQuad",
                            "easeOutQuad",
                            "easeInOutQuad",
                            "easeInCubic",
                            "easeOutCubic",
                            "easeInOutCubic",
                            "easeInQuart",
                            "easeOutQuart",
                            "easeInOutQuart",
                            "easeInQuint",
                            "easeOutQuint",
                            "easeInOutQuint",
                            "easeInElastic",
                            "easeOutElastic",
                            "easeInOutElastic",
                            "easeInBack",
                            "easeOutBack",
                            "easeInOutBack",
                            "easeInBounce",
                            "easeOutBounce",
                            "easeInOutBounce"
                        ],
                        "type": "string"
                    },
                    "templateId": {
                        "enum": [
                            "fade-in",
                            "slide-in",
                            "ascend-in",
                            "scale-in",
                            "flip-in",
                            "blur-in",
                            "fade-out",
                            "slide-out",
                            "descend-out",
                            "scale-out",
                            "flip-out",
                            "blur-out"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "settings": {
                        "$ref": "#/components/schemas/SettingDto"
                    },
                    "hidden": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "BorderDto": {
                "required": [
                    "color",
                    "style",
                    "thickness"
                ],
                "type": "object",
                "properties": {
                    "thickness": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "style": {
                        "enum": [
                            "solid",
                            "dotted",
                            "dashed"
                        ],
                        "type": "string"
                    },
                    "color": {
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "BorderOverrideDto": {
                "type": "object",
                "properties": {
                    "thickness": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "style": {
                        "enum": [
                            "solid",
                            "dotted",
                            "dashed"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "color": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "BorderOverrideDtoGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/BorderOverrideDto"
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CreateSizeDto": {
                "required": [
                    "height",
                    "width"
                ],
                "type": "object",
                "properties": {
                    "width": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "height": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CreateSizesDto": {
                "required": [
                    "sizes"
                ],
                "type": "object",
                "properties": {
                    "sizes": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/CreateSizeDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "CreateVersionDto": {
                "required": [
                    "elements",
                    "localizationId",
                    "name",
                    "targetUrl"
                ],
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string"
                    },
                    "localizationId": {
                        "type": "string"
                    },
                    "targetUrl": {
                        "type": "string"
                    },
                    "elements": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ElementTextSegmentDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "CreateVersionsDto": {
                "required": [
                    "versions"
                ],
                "type": "object",
                "properties": {
                    "versions": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/CreateVersionDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "CreativeDto": {
                "required": [
                    "elements",
                    "id",
                    "sizeId",
                    "versionId"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "elements": {
                        "type": "object",
                        "additionalProperties": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/WidgetElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/EllipseElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/GroupNodeOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ImageElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/RectangleElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/TextLikeElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/VideoElementOverrideDto"
                                }
                            ]
                        }
                    },
                    "sizeId": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "versionId": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "targetUrl": {
                        "type": "string",
                        "nullable": true
                    },
                    "checksum": {
                        "type": "string",
                        "nullable": true
                    },
                    "approvalStatus": {
                        "enum": [
                            "In progress",
                            "For review",
                            "Not approved",
                            "Approved"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "creativeWeights": {
                        "$ref": "#/components/schemas/CreativeWeightsDto"
                    }
                },
                "additionalProperties": false
            },
            "CreativeSetDto": {
                "required": [
                    "brandId",
                    "creatives",
                    "defaultVersionId",
                    "elementsPool",
                    "fonts",
                    "id",
                    "lastModified",
                    "name",
                    "sizes",
                    "versions"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "brandId": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "stateId": {
                        "type": "string",
                        "nullable": true
                    },
                    "creatives": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/CreativeDto"
                        }
                    },
                    "sizes": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/SizeDto"
                        }
                    },
                    "versions": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/VersionDto"
                        }
                    },
                    "elementsPool": {
                        "type": "array",
                        "items": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/ElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/EllipseElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/GroupNodeDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ImageElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/RectangleElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/TextLikeElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/VideoElementDto"
                                },
                                {
                                    "$ref": "#/components/schemas/WidgetElementDto"
                                }
                            ]
                        }
                    },
                    "defaultVersionId": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "fonts": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/FontFamilyDto"
                        }
                    },
                    "lastModified": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "CreativeSocialGuideDto": {
                "required": [
                    "guidelines",
                    "network",
                    "overlay",
                    "placement"
                ],
                "type": "object",
                "properties": {
                    "placement": {
                        "enum": [
                            "meta-instagram",
                            "meta-facebook",
                            "meta-messenger",
                            "meta-facebookreels",
                            "meta-instagramreels",
                            "tiktok-default",
                            "tiktok-after-9s",
                            "tiktok-after-9s-with-card",
                            "browse-image",
                            "browse-video",
                            "watch-video",
                            "snapchat-default"
                        ],
                        "type": "string"
                    },
                    "network": {
                        "enum": [
                            "meta",
                            "tiktok",
                            "pinterest",
                            "snapchat"
                        ],
                        "type": "string"
                    },
                    "guidelines": {
                        "type": "boolean"
                    },
                    "overlay": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "CreativeWeightsDto": {
                "required": [
                    "creativeChecksum",
                    "creativeId",
                    "failed",
                    "id"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "creativeId": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "creativeChecksum": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "weights": {
                        "$ref": "#/components/schemas/WeightsDto"
                    },
                    "failed": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyBooleanDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyBooleanOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyColorDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyColorOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyDto": {
                "required": [
                    "$type",
                    "label",
                    "name"
                ],
                "type": "object",
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "label": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "hasDynamicContent": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "CustomPropertyBooleanDto": "#/components/schemas/CustomPropertyBooleanDto",
                        "CustomPropertyColorDto": "#/components/schemas/CustomPropertyColorDto",
                        "CustomPropertyFeedDto": "#/components/schemas/CustomPropertyFeedDto",
                        "CustomPropertyFontDto": "#/components/schemas/CustomPropertyFontDto",
                        "CustomPropertyImageDto": "#/components/schemas/CustomPropertyImageDto",
                        "CustomPropertyNumberDto": "#/components/schemas/CustomPropertyNumberDto",
                        "CustomPropertySelectDto": "#/components/schemas/CustomPropertySelectDto",
                        "CustomPropertyTextDto": "#/components/schemas/CustomPropertyTextDto"
                    }
                }
            },
            "CustomPropertyFeedDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/FeedDto"
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpName": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyFeedOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/FeedDto"
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpName": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyFontDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/WidgetFontStyleDto"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyFontOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/WidgetFontStyleDto"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyImageDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/WidgetImageDto"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyImageOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/WidgetImageDto"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyNumberDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyNumberOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyOverrideDto": {
                "required": [
                    "$type"
                ],
                "type": "object",
                "properties": {
                    "$type": {
                        "type": "string"
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "CustomPropertyBooleanOverrideDto": "#/components/schemas/CustomPropertyBooleanOverrideDto",
                        "CustomPropertyColorOverrideDto": "#/components/schemas/CustomPropertyColorOverrideDto",
                        "CustomPropertyFeedOverrideDto": "#/components/schemas/CustomPropertyFeedOverrideDto",
                        "CustomPropertyFontOverrideDto": "#/components/schemas/CustomPropertyFontOverrideDto",
                        "CustomPropertyImageOverrideDto": "#/components/schemas/CustomPropertyImageOverrideDto",
                        "CustomPropertyNumberOverrideDto": "#/components/schemas/CustomPropertyNumberOverrideDto",
                        "CustomPropertySelectOverrideDto": "#/components/schemas/CustomPropertySelectOverrideDto",
                        "CustomPropertyTextOverrideDto": "#/components/schemas/CustomPropertyTextOverrideDto"
                    }
                }
            },
            "CustomPropertySelectDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/WidgetSelectionOptionDto"
                        },
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertySelectOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/WidgetSelectionOptionDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyTextDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpName": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "CustomPropertyTextOverrideDto": {
                "required": [
                    "value"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/CustomPropertyOverrideDto"
                    }
                ],
                "properties": {
                    "value": {
                        "type": "string"
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpName": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "DecimalGenericStructType": {
                "type": "object",
                "properties": {
                    "value": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "DeleteSizesDto": {
                "required": [
                    "ids"
                ],
                "type": "object",
                "properties": {
                    "ids": {
                        "type": "array",
                        "items": {
                            "type": "integer",
                            "format": "int32"
                        }
                    }
                },
                "additionalProperties": false
            },
            "DeleteVersionsDto": {
                "required": [
                    "ids"
                ],
                "type": "object",
                "properties": {
                    "ids": {
                        "type": "array",
                        "items": {
                            "type": "integer",
                            "format": "int32"
                        }
                    }
                },
                "additionalProperties": false
            },
            "DesignDto": {
                "required": [
                    "fill",
                    "gifExport",
                    "guidelines",
                    "loops",
                    "name",
                    "preloadImage"
                ],
                "type": "object",
                "properties": {
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "fill": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "loops": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "audio": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "guidelines": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GuidelineDto"
                        }
                    },
                    "socialGuide": {
                        "$ref": "#/components/schemas/CreativeSocialGuideDto"
                    },
                    "gifExport": {
                        "$ref": "#/components/schemas/GifExportDto"
                    },
                    "preloadImage": {
                        "$ref": "#/components/schemas/PreloadImageDto"
                    },
                    "startTime": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "stopTime": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "l_DesId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_DocId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "DuplicateSizesDto": {
                "required": [
                    "ids"
                ],
                "type": "object",
                "properties": {
                    "ids": {
                        "type": "array",
                        "items": {
                            "type": "integer",
                            "format": "int32"
                        }
                    }
                },
                "additionalProperties": false
            },
            "DynamicContentDto": {
                "required": [
                    "id",
                    "label",
                    "value"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "label": {
                        "type": "string"
                    },
                    "value": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "ElementDto": {
                "required": [
                    "$type",
                    "actions",
                    "animations",
                    "duration",
                    "filters",
                    "height",
                    "mirrorX",
                    "mirrorY",
                    "opacity",
                    "originX",
                    "originY",
                    "radius",
                    "rotationX",
                    "rotationY",
                    "rotationZ",
                    "scaleX",
                    "scaleY",
                    "time",
                    "width",
                    "x",
                    "y"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/NodeDto"
                    }
                ],
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "time": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "duration": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "parentId": {
                        "type": "string",
                        "nullable": true
                    },
                    "ratio": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "x": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "integer",
                        "format": "int32"
                    },
                    "y": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "integer",
                        "format": "int32"
                    },
                    "width": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32"
                    },
                    "height": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32"
                    },
                    "fill": {
                        "type": "string",
                        "nullable": true
                    },
                    "originX": {
                        "type": "number",
                        "format": "double"
                    },
                    "originY": {
                        "type": "number",
                        "format": "double"
                    },
                    "rotationX": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double"
                    },
                    "rotationY": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double"
                    },
                    "rotationZ": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double"
                    },
                    "radius": {
                        "$ref": "#/components/schemas/RadiusDto"
                    },
                    "opacity": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "mirrorX": {
                        "type": "boolean"
                    },
                    "mirrorY": {
                        "type": "boolean"
                    },
                    "scaleX": {
                        "type": "number",
                        "format": "double"
                    },
                    "scaleY": {
                        "type": "number",
                        "format": "double"
                    },
                    "shadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ShadowDto"
                        },
                        "nullable": true
                    },
                    "border": {
                        "$ref": "#/components/schemas/BorderDto"
                    },
                    "animations": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/AnimationDto"
                        }
                    },
                    "actions": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ActionDto"
                        }
                    },
                    "filters": {
                        "$ref": "#/components/schemas/FilterDto"
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "EllipseElementDto": "#/components/schemas/EllipseElementDto",
                        "ImageElementDto": "#/components/schemas/ImageElementDto",
                        "RectangleElementDto": "#/components/schemas/RectangleElementDto",
                        "TextLikeElementDto": "#/components/schemas/TextLikeElementDto",
                        "VideoElementDto": "#/components/schemas/VideoElementDto",
                        "WidgetElementDto": "#/components/schemas/WidgetElementDto"
                    }
                }
            },
            "ElementOverrideDto": {
                "required": [
                    "$type"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/NodeOverrideDto"
                    }
                ],
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "time": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "duration": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "parentId": {
                        "$ref": "#/components/schemas/StringGenericDataType"
                    },
                    "ratio": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "x": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "y": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "width": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "height": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "fill": {
                        "$ref": "#/components/schemas/StringGenericDataType"
                    },
                    "originX": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "originY": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "rotationX": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "rotationY": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "rotationZ": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "radius": {
                        "$ref": "#/components/schemas/RadiusOverrideDto"
                    },
                    "opacity": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "mirrorX": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "mirrorY": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "scaleX": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "scaleY": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "shadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ShadowDto"
                        },
                        "nullable": true
                    },
                    "border": {
                        "$ref": "#/components/schemas/BorderOverrideDtoGenericDataType"
                    },
                    "animations": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/AnimationDto"
                        },
                        "nullable": true
                    },
                    "actions": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ActionDto"
                        },
                        "nullable": true
                    },
                    "filters": {
                        "$ref": "#/components/schemas/FilterOverrideDto"
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "WidgetElementOverrideDto": "#/components/schemas/WidgetElementOverrideDto",
                        "EllipseElementOverrideDto": "#/components/schemas/EllipseElementOverrideDto",
                        "ImageElementOverrideDto": "#/components/schemas/ImageElementOverrideDto",
                        "RectangleElementOverrideDto": "#/components/schemas/RectangleElementOverrideDto",
                        "TextLikeElementOverrideDto": "#/components/schemas/TextLikeElementOverrideDto",
                        "VideoElementOverrideDto": "#/components/schemas/VideoElementOverrideDto"
                    }
                }
            },
            "ElementTextSegmentDto": {
                "required": [
                    "id",
                    "textSegments"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "textSegments": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextSegmentDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "EllipseElementDto": {
                "required": [
                    "states"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        }
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDto"
                    }
                },
                "additionalProperties": false
            },
            "EllipseElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        },
                        "nullable": true
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDtoGenericDataType"
                    }
                },
                "additionalProperties": false
            },
            "FeedDto": {
                "required": [
                    "fallback",
                    "id",
                    "path",
                    "step",
                    "type"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "path": {
                        "type": "string"
                    },
                    "step": {
                        "$ref": "#/components/schemas/FeedStepDto"
                    },
                    "fallback": {
                        "type": "string"
                    },
                    "type": {
                        "enum": [
                            "text",
                            "number",
                            "image",
                            "video"
                        ],
                        "type": "string"
                    },
                    "legacyFeededReference": {
                        "$ref": "#/components/schemas/LegacyFeededReference"
                    }
                },
                "additionalProperties": false
            },
            "FeedDtoGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/FeedDto"
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "FeedStepDto": {
                "required": [
                    "occurrence",
                    "size",
                    "start"
                ],
                "type": "object",
                "properties": {
                    "occurrence": {
                        "enum": [
                            "none",
                            "loop"
                        ],
                        "type": "string"
                    },
                    "size": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "start": {
                        "type": "integer",
                        "format": "int32"
                    }
                },
                "additionalProperties": false
            },
            "FilterDto": {
                "type": "object",
                "properties": {
                    "blur": {
                        "maximum": 100,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "contrast": {
                        "maximum": 200,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "invert": {
                        "maximum": 100,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "saturate": {
                        "maximum": 200,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "grayscale": {
                        "maximum": 100,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "sepia": {
                        "maximum": 100,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "FilterOverrideDto": {
                "type": "object",
                "properties": {
                    "blur": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "contrast": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "invert": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "saturate": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "grayscale": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    },
                    "sepia": {
                        "$ref": "#/components/schemas/DecimalGenericStructType"
                    }
                },
                "additionalProperties": false
            },
            "FontFamilyDto": {
                "required": [
                    "fontStyles",
                    "name"
                ],
                "type": "object",
                "properties": {
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "fontStyles": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/FontStyleDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "FontStyleDto": {
                "required": [
                    "fontUrl",
                    "id",
                    "name"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "fontUrl": {
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "FrameDto": {
                "required": [
                    "time"
                ],
                "type": "object",
                "properties": {
                    "time": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "duration": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "GenericStateDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/StateDto"
                    }
                ],
                "additionalProperties": false
            },
            "GetAllStudioBackupVersionIdsQueryResponse": {
                "required": [
                    "blobVersionId",
                    "sizeCount",
                    "userName",
                    "versionCount"
                ],
                "type": "object",
                "properties": {
                    "blobVersionId": {
                        "type": "string"
                    },
                    "sizeCount": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "versionCount": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "userName": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "GifExportDto": {
                "required": [
                    "frames",
                    "show"
                ],
                "type": "object",
                "properties": {
                    "frames": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/FrameDto"
                        }
                    },
                    "show": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "GroupNodeDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/NodeDto"
                    }
                ],
                "additionalProperties": false
            },
            "GroupNodeOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/NodeOverrideDto"
                    }
                ],
                "additionalProperties": false
            },
            "GuidelineDto": {
                "required": [
                    "id",
                    "position",
                    "preview",
                    "type"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "position": {
                        "$ref": "#/components/schemas/PositionDto"
                    },
                    "type": {
                        "enum": [
                            "vertical",
                            "horizontal"
                        ],
                        "type": "string"
                    },
                    "preview": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "HttpValidationProblemDetails": {
                "required": [
                    "errors"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ProblemDetails"
                    }
                ],
                "properties": {
                    "errors": {
                        "type": "object",
                        "additionalProperties": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                },
                "additionalProperties": {}
            },
            "ImageAssetDto": {
                "required": [
                    "created",
                    "fileSize",
                    "id",
                    "name",
                    "original",
                    "thumbnail"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "original": {
                        "$ref": "#/components/schemas/ImageDto"
                    },
                    "thumbnail": {
                        "$ref": "#/components/schemas/ImageDto"
                    },
                    "animatedThumbnail": {
                        "$ref": "#/components/schemas/ImageDto"
                    },
                    "fileSize": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "created": {
                        "type": "string",
                        "format": "date-time"
                    },
                    "modified": {
                        "type": "string",
                        "format": "date-time",
                        "nullable": true
                    },
                    "isGenAi": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "ImageAssetDtoGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/ImageAssetDto"
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "ImageDto": {
                "required": [
                    "height",
                    "url",
                    "width"
                ],
                "type": "object",
                "properties": {
                    "url": {
                        "maxLength": 2083,
                        "minLength": 1,
                        "type": "string"
                    },
                    "width": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "height": {
                        "type": "integer",
                        "format": "int32"
                    }
                },
                "additionalProperties": false
            },
            "ImageElementDto": {
                "required": [
                    "imageSettings",
                    "states"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "imageSettings": {
                        "$ref": "#/components/schemas/ImageSettingsDto"
                    },
                    "feed": {
                        "$ref": "#/components/schemas/FeedDto"
                    },
                    "dynamicContent": {
                        "$ref": "#/components/schemas/DynamicContentDto"
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        }
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDto"
                    },
                    "imageAsset": {
                        "$ref": "#/components/schemas/ImageAssetDto"
                    },
                    "l_IEPropId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_DEPropId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "ImageElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "imageSettings": {
                        "$ref": "#/components/schemas/ImageSettingsDto"
                    },
                    "feed": {
                        "$ref": "#/components/schemas/FeedDtoGenericDataType"
                    },
                    "dynamicContent": {
                        "$ref": "#/components/schemas/DynamicContentDto"
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        },
                        "nullable": true
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDtoGenericDataType"
                    },
                    "imageAsset": {
                        "$ref": "#/components/schemas/ImageAssetDtoGenericDataType"
                    }
                },
                "additionalProperties": false
            },
            "ImageSettingsDto": {
                "required": [
                    "sizeMode",
                    "x",
                    "y"
                ],
                "type": "object",
                "properties": {
                    "x": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "y": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "quality": {
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "sizeMode": {
                        "enum": [
                            "fit",
                            "stretch",
                            "crop"
                        ],
                        "type": "string"
                    },
                    "highDpi": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "KeyframeDto": {
                "required": [
                    "duration",
                    "id",
                    "time"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "stateId": {
                        "type": "string",
                        "nullable": true
                    },
                    "time": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "duration": {
                        "maximum": 2000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "timingFunction": {
                        "enum": [
                            "@timingFunction",
                            "linear",
                            "easeInExpo",
                            "easeOutExpo",
                            "easeInOutExpo",
                            "easeInQuad",
                            "easeOutQuad",
                            "easeInOutQuad",
                            "easeInCubic",
                            "easeOutCubic",
                            "easeInOutCubic",
                            "easeInQuart",
                            "easeOutQuart",
                            "easeInOutQuart",
                            "easeInQuint",
                            "easeOutQuint",
                            "easeInOutQuint",
                            "easeInElastic",
                            "easeOutElastic",
                            "easeInOutElastic",
                            "easeInBack",
                            "easeOutBack",
                            "easeInOutBack",
                            "easeInBounce",
                            "easeOutBounce",
                            "easeInOutBounce"
                        ],
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "LegacyFeededReference": {
                "required": [
                    "clientId",
                    "id",
                    "name",
                    "unit",
                    "value"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "clientId": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "unit": {
                        "enum": [
                            "id"
                        ],
                        "type": "string"
                    },
                    "value": {
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "LegacyWidgetProperty": {
                "required": [
                    "id",
                    "name"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "name": {
                        "type": "string"
                    },
                    "label": {
                        "type": "string",
                        "nullable": true
                    },
                    "hasDynamicContent": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "LoadAssetDto": {
                "required": [
                    "name",
                    "parentUrl",
                    "type",
                    "url",
                    "weight"
                ],
                "type": "object",
                "properties": {
                    "weight": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "url": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "parentUrl": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "type": {
                        "enum": [
                            "Application",
                            "Audio",
                            "Font",
                            "Image",
                            "Text",
                            "Unknown",
                            "Video"
                        ],
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "LoadDto": {
                "required": [
                    "assets",
                    "totalWeight"
                ],
                "type": "object",
                "properties": {
                    "totalWeight": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "assets": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/LoadAssetDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "MaskDto": {
                "required": [
                    "isMask"
                ],
                "type": "object",
                "properties": {
                    "isMask": {
                        "type": "boolean"
                    },
                    "elementId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "MaskDtoGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/MaskDto"
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "NodeDto": {
                "required": [
                    "$type",
                    "hidden",
                    "id",
                    "locked",
                    "name"
                ],
                "type": "object",
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "parentNodeId": {
                        "type": "string",
                        "nullable": true
                    },
                    "locked": {
                        "type": "boolean"
                    },
                    "hidden": {
                        "type": "boolean"
                    },
                    "name": {
                        "maxLength": 250,
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "ElementDto": "#/components/schemas/ElementDto",
                        "EllipseElementDto": "#/components/schemas/EllipseElementDto",
                        "GroupNodeDto": "#/components/schemas/GroupNodeDto",
                        "ImageElementDto": "#/components/schemas/ImageElementDto",
                        "RectangleElementDto": "#/components/schemas/RectangleElementDto",
                        "TextLikeElementDto": "#/components/schemas/TextLikeElementDto",
                        "VideoElementDto": "#/components/schemas/VideoElementDto",
                        "WidgetElementDto": "#/components/schemas/WidgetElementDto"
                    }
                }
            },
            "NodeOverrideDto": {
                "required": [
                    "$type"
                ],
                "type": "object",
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "sortIndex": {
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "parentNodeId": {
                        "$ref": "#/components/schemas/StringGenericDataType"
                    },
                    "locked": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "hidden": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false,
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "WidgetElementOverrideDto": "#/components/schemas/WidgetElementOverrideDto",
                        "ElementOverrideDto": "#/components/schemas/ElementOverrideDto",
                        "EllipseElementOverrideDto": "#/components/schemas/EllipseElementOverrideDto",
                        "GroupNodeOverrideDto": "#/components/schemas/GroupNodeOverrideDto",
                        "ImageElementOverrideDto": "#/components/schemas/ImageElementOverrideDto",
                        "RectangleElementOverrideDto": "#/components/schemas/RectangleElementOverrideDto",
                        "TextLikeElementOverrideDto": "#/components/schemas/TextLikeElementOverrideDto",
                        "VideoElementOverrideDto": "#/components/schemas/VideoElementOverrideDto"
                    }
                }
            },
            "OperationAnimationDto": {
                "required": [
                    "duration",
                    "timingFunction"
                ],
                "type": "object",
                "properties": {
                    "timingFunction": {
                        "enum": [
                            "linear",
                            "easeInExpo",
                            "easeOutExpo",
                            "easeInOutExpo",
                            "easeInQuad",
                            "easeOutQuad",
                            "easeInOutQuad",
                            "easeInCubic",
                            "easeOutCubic",
                            "easeInOutCubic",
                            "easeInQuart",
                            "easeOutQuart",
                            "easeInOutQuart",
                            "easeInQuint",
                            "easeOutQuint",
                            "easeInOutQuint",
                            "easeInElastic",
                            "easeOutElastic",
                            "easeInOutElastic",
                            "easeInBack",
                            "easeOutBack",
                            "easeInOutBack",
                            "easeInBounce",
                            "easeOutBounce",
                            "easeInOutBounce"
                        ],
                        "type": "string"
                    },
                    "duration": {
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "OperationDto": {
                "required": [
                    "method"
                ],
                "type": "object",
                "properties": {
                    "method": {
                        "enum": [
                            "SET_STATE",
                            "REMOVE_STATE",
                            "CLEAR_STATES",
                            "OPEN_URL"
                        ],
                        "type": "string"
                    },
                    "value": {
                        "type": "string",
                        "nullable": true
                    },
                    "target": {
                        "type": "string",
                        "nullable": true
                    },
                    "animation": {
                        "$ref": "#/components/schemas/OperationAnimationDto"
                    }
                },
                "additionalProperties": false
            },
            "OptimizationDto": {
                "required": [
                    "enabled"
                ],
                "type": "object",
                "properties": {
                    "enabled": {
                        "type": "boolean"
                    },
                    "quality": {
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "PaddingDto": {
                "required": [
                    "bottom",
                    "left",
                    "right",
                    "top"
                ],
                "type": "object",
                "properties": {
                    "top": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "left": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "right": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "bottom": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "PaddingOverrideDto": {
                "type": "object",
                "properties": {
                    "top": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "left": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "right": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "bottom": {
                        "maximum": 5000,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "PlaybackButtonDto": {
                "required": [
                    "color",
                    "enabled",
                    "size"
                ],
                "type": "object",
                "properties": {
                    "enabled": {
                        "type": "boolean"
                    },
                    "size": {
                        "maximum": 100,
                        "minimum": 0,
                        "type": "integer",
                        "format": "int32"
                    },
                    "color": {
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "PositionDto": {
                "required": [
                    "x",
                    "y"
                ],
                "type": "object",
                "properties": {
                    "x": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "y": {
                        "type": "integer",
                        "format": "int32"
                    }
                },
                "additionalProperties": false
            },
            "PreloadImageDto": {
                "required": [
                    "format",
                    "frames",
                    "quality"
                ],
                "type": "object",
                "properties": {
                    "quality": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "format": {
                        "enum": [
                            "jpg",
                            "gif",
                            "png"
                        ],
                        "type": "string"
                    },
                    "frames": {
                        "type": "array",
                        "items": {
                            "type": "number",
                            "format": "double"
                        }
                    }
                },
                "additionalProperties": false
            },
            "ProblemDetails": {
                "required": [
                    "$type"
                ],
                "type": "object",
                "properties": {
                    "$type": {
                        "type": "string"
                    },
                    "type": {
                        "type": "string",
                        "nullable": true
                    },
                    "title": {
                        "type": "string",
                        "nullable": true
                    },
                    "status": {
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "detail": {
                        "type": "string",
                        "nullable": true
                    },
                    "instance": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": {},
                "discriminator": {
                    "propertyName": "$type",
                    "mapping": {
                        "ProblemDetails": "#/components/schemas/ProblemDetails",
                        "HttpValidationProblemDetails": "#/components/schemas/HttpValidationProblemDetails"
                    }
                }
            },
            "RadiusDto": {
                "required": [
                    "bottomLeft",
                    "bottomRight",
                    "topLeft",
                    "topRight",
                    "type"
                ],
                "type": "object",
                "properties": {
                    "type": {
                        "enum": [
                            "joint",
                            "separate"
                        ],
                        "type": "string"
                    },
                    "topLeft": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "topRight": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "bottomRight": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "bottomLeft": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "RadiusOverrideDto": {
                "type": "object",
                "properties": {
                    "type": {
                        "enum": [
                            "joint",
                            "separate"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "topLeft": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "topRight": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "bottomRight": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "bottomLeft": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "RectangleElementDto": {
                "required": [
                    "states"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        }
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDto"
                    }
                },
                "additionalProperties": false
            },
            "RectangleElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        },
                        "nullable": true
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDtoGenericDataType"
                    }
                },
                "additionalProperties": false
            },
            "SettingDto": {
                "type": "object",
                "properties": {
                    "direction": {
                        "$ref": "#/components/schemas/SettingDtoKind"
                    },
                    "distance": {
                        "$ref": "#/components/schemas/SettingDtoKind"
                    }
                },
                "additionalProperties": false
            },
            "SettingDtoKind": {
                "required": [
                    "name",
                    "value"
                ],
                "type": "object",
                "properties": {
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "value": {
                        "maximum": 9999999,
                        "minimum": -9999999,
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "ShadowDto": {
                "required": [
                    "blur",
                    "color",
                    "offsetX",
                    "offsetY",
                    "spread"
                ],
                "type": "object",
                "properties": {
                    "offsetX": {
                        "maximum": 30000,
                        "minimum": -30000,
                        "type": "number",
                        "format": "double"
                    },
                    "offsetY": {
                        "maximum": 30000,
                        "minimum": -30000,
                        "type": "number",
                        "format": "double"
                    },
                    "color": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "blur": {
                        "maximum": 250,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "spread": {
                        "type": "number",
                        "format": "double"
                    }
                },
                "additionalProperties": false
            },
            "SizeDto": {
                "required": [
                    "elements",
                    "height",
                    "id",
                    "width"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "width": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "height": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "type": "string",
                        "nullable": true
                    },
                    "design": {
                        "$ref": "#/components/schemas/DesignDto"
                    },
                    "elements": {
                        "type": "object",
                        "additionalProperties": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/WidgetElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/EllipseElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/GroupNodeOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ImageElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/RectangleElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/TextLikeElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/VideoElementOverrideDto"
                                }
                            ]
                        }
                    }
                },
                "additionalProperties": false
            },
            "StateDto": {
                "required": [
                    "id"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "name": {
                        "type": "string",
                        "nullable": true
                    },
                    "ratio": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "x": {
                        "type": "string",
                        "nullable": true
                    },
                    "y": {
                        "type": "string",
                        "nullable": true
                    },
                    "width": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "height": {
                        "maximum": 9999999,
                        "minimum": 1,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "fill": {
                        "type": "string",
                        "nullable": true
                    },
                    "originX": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "originY": {
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "rotationX": {
                        "type": "string",
                        "nullable": true
                    },
                    "rotationY": {
                        "type": "string",
                        "nullable": true
                    },
                    "rotationZ": {
                        "type": "string",
                        "nullable": true
                    },
                    "radius": {
                        "$ref": "#/components/schemas/RadiusDto"
                    },
                    "opacity": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "mirrorX": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "mirrorY": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "scaleX": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "scaleY": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "shadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/ShadowDto"
                        },
                        "nullable": true
                    },
                    "border": {
                        "$ref": "#/components/schemas/BorderDto"
                    },
                    "filters": {
                        "$ref": "#/components/schemas/FilterDto"
                    }
                },
                "additionalProperties": false
            },
            "StreamingDto": {
                "required": [
                    "enabled"
                ],
                "type": "object",
                "properties": {
                    "enabled": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            },
            "StringGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "type": "string",
                        "nullable": true
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "TextLikeElementDto": {
                "required": [
                    "characterSpacing",
                    "fontSize",
                    "horizontalAlignment",
                    "isButton",
                    "lineHeight",
                    "maxRows",
                    "padding",
                    "states",
                    "strikethrough",
                    "textColor",
                    "textOverflow",
                    "textSegments",
                    "underline",
                    "uppercase",
                    "verticalAlignment"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "textSegments": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextSegmentDto"
                        }
                    },
                    "dynamicContent": {
                        "$ref": "#/components/schemas/DynamicContentDto"
                    },
                    "font": {
                        "type": "string",
                        "nullable": true
                    },
                    "textColor": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "fontSize": {
                        "maximum": 2500,
                        "minimum": 1,
                        "type": "number",
                        "format": "double"
                    },
                    "lineHeight": {
                        "maximum": 9999,
                        "minimum": 0.1,
                        "type": "number",
                        "format": "double"
                    },
                    "characterSpacing": {
                        "maximum": 9999,
                        "minimum": -1,
                        "type": "number",
                        "format": "double"
                    },
                    "uppercase": {
                        "type": "boolean"
                    },
                    "underline": {
                        "type": "boolean"
                    },
                    "strikethrough": {
                        "type": "boolean"
                    },
                    "maxRows": {
                        "maximum": 9999,
                        "minimum": 0,
                        "type": "integer",
                        "format": "int32"
                    },
                    "padding": {
                        "$ref": "#/components/schemas/PaddingDto"
                    },
                    "textOverflow": {
                        "enum": [
                            "shrink",
                            "truncate",
                            "expand",
                            "scroll"
                        ],
                        "type": "string"
                    },
                    "horizontalAlignment": {
                        "enum": [
                            "left",
                            "center",
                            "right",
                            "justify"
                        ],
                        "type": "string"
                    },
                    "verticalAlignment": {
                        "enum": [
                            "top",
                            "middle",
                            "bottom"
                        ],
                        "type": "string"
                    },
                    "textShadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextShadowDto"
                        },
                        "nullable": true
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextStateDto"
                        }
                    },
                    "isButton": {
                        "type": "boolean"
                    },
                    "l_TEPId": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "TextLikeElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "textSegments": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextSegmentDto"
                        },
                        "nullable": true
                    },
                    "dynamicContent": {
                        "$ref": "#/components/schemas/DynamicContentDto"
                    },
                    "font": {
                        "$ref": "#/components/schemas/StringGenericDataType"
                    },
                    "textColor": {
                        "type": "string",
                        "nullable": true
                    },
                    "fontSize": {
                        "maximum": 2500,
                        "minimum": 1,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "lineHeight": {
                        "maximum": 9999,
                        "minimum": 0.1,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "characterSpacing": {
                        "maximum": 9999,
                        "minimum": -1,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "uppercase": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "underline": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "strikethrough": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "maxRows": {
                        "maximum": 9999,
                        "minimum": 0,
                        "type": "integer",
                        "format": "int32",
                        "nullable": true
                    },
                    "padding": {
                        "$ref": "#/components/schemas/PaddingOverrideDto"
                    },
                    "textOverflow": {
                        "enum": [
                            "shrink",
                            "truncate",
                            "expand",
                            "scroll"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "horizontalAlignment": {
                        "enum": [
                            "left",
                            "center",
                            "right",
                            "justify"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "verticalAlignment": {
                        "enum": [
                            "top",
                            "middle",
                            "bottom"
                        ],
                        "type": "string",
                        "nullable": true
                    },
                    "textShadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextShadowDto"
                        },
                        "nullable": true
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextStateDto"
                        },
                        "nullable": true
                    },
                    "l_VpId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "TextSegmentDto": {
                "required": [
                    "content"
                ],
                "type": "object",
                "properties": {
                    "content": {
                        "maxLength": 2147483647,
                        "minLength": 1,
                        "type": "string"
                    },
                    "styles": {
                        "$ref": "#/components/schemas/TextStyleValueDto"
                    },
                    "variable": {
                        "$ref": "#/components/schemas/VariableDto"
                    },
                    "dynamicContent": {
                        "$ref": "#/components/schemas/DynamicContentDto"
                    },
                    "l_StyleId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "TextShadowDto": {
                "required": [
                    "blur",
                    "color",
                    "offsetX",
                    "offsetY"
                ],
                "type": "object",
                "properties": {
                    "offsetX": {
                        "maximum": 30000,
                        "minimum": -30000,
                        "type": "number",
                        "format": "double"
                    },
                    "offsetY": {
                        "maximum": 30000,
                        "minimum": -30000,
                        "type": "number",
                        "format": "double"
                    },
                    "blur": {
                        "maximum": 250,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "color": {
                        "minLength": 1,
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "TextStateDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/StateDto"
                    }
                ],
                "properties": {
                    "textColor": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "TextStyleValueDto": {
                "type": "object",
                "properties": {
                    "font": {
                        "type": "string",
                        "nullable": true
                    },
                    "textColor": {
                        "type": "string",
                        "nullable": true
                    },
                    "fontSize": {
                        "maximum": 2500,
                        "minimum": 0.01,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "lineHeight": {
                        "maximum": 9999,
                        "minimum": 0.1,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "characterSpacing": {
                        "maximum": 9999,
                        "minimum": -1,
                        "type": "number",
                        "format": "double",
                        "nullable": true
                    },
                    "uppercase": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "underline": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "strikethrough": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "textShadows": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/TextShadowDto"
                        },
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "UpdateCreativeDto": {
                "required": [
                    "approvalStatus",
                    "targetUrl"
                ],
                "type": "object",
                "properties": {
                    "targetUrl": {
                        "type": "string"
                    },
                    "approvalStatus": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "UpdateSizeDto": {
                "required": [
                    "name"
                ],
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "UpdateVersionDto": {
                "required": [
                    "localizationId",
                    "name",
                    "targetUrl",
                    "versionId"
                ],
                "type": "object",
                "properties": {
                    "versionId": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "type": "string"
                    },
                    "localizationId": {
                        "type": "string"
                    },
                    "targetUrl": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "UpdateVersionsDto": {
                "required": [
                    "versions"
                ],
                "type": "object",
                "properties": {
                    "versions": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/UpdateVersionDto"
                        }
                    }
                },
                "additionalProperties": false
            },
            "VariableDto": {
                "required": [
                    "fallback",
                    "id",
                    "path",
                    "step",
                    "type"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "path": {
                        "type": "string"
                    },
                    "step": {
                        "$ref": "#/components/schemas/FeedStepDto"
                    },
                    "fallback": {
                        "type": "string"
                    },
                    "type": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "spanId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "VersionDto": {
                "required": [
                    "elements",
                    "id",
                    "localizationId",
                    "name",
                    "targetUrl"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "localizationId": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "targetUrl": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "elements": {
                        "type": "object",
                        "additionalProperties": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/WidgetElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/EllipseElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/GroupNodeOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/ImageElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/RectangleElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/TextLikeElementOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/VideoElementOverrideDto"
                                }
                            ]
                        }
                    }
                },
                "additionalProperties": false
            },
            "VideoAssetDto": {
                "required": [
                    "created",
                    "durationInMilliseconds",
                    "fileSize",
                    "height",
                    "id",
                    "name",
                    "thumbnail",
                    "url",
                    "width"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "name": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "url": {
                        "maxLength": 2083,
                        "minLength": 1,
                        "type": "string"
                    },
                    "fileSize": {
                        "type": "integer",
                        "format": "int64"
                    },
                    "width": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "height": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "durationInMilliseconds": {
                        "type": "number",
                        "format": "double"
                    },
                    "thumbnail": {
                        "$ref": "#/components/schemas/ImageDto"
                    },
                    "created": {
                        "type": "string",
                        "format": "date-time"
                    },
                    "modified": {
                        "type": "string",
                        "format": "date-time",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "VideoAssetDtoGenericDataType": {
                "type": "object",
                "properties": {
                    "value": {
                        "$ref": "#/components/schemas/VideoAssetDto"
                    },
                    "isRemoved": {
                        "type": "boolean",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "VideoElementDto": {
                "required": [
                    "states",
                    "videoSettings"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "feed": {
                        "$ref": "#/components/schemas/FeedDto"
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        }
                    },
                    "videoSettings": {
                        "$ref": "#/components/schemas/VideoSettingsDto"
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDto"
                    },
                    "videoAsset": {
                        "$ref": "#/components/schemas/VideoAssetDto"
                    },
                    "checksum": {
                        "type": "string",
                        "nullable": true
                    },
                    "l_VEPId": {
                        "type": "string",
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "VideoElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "feed": {
                        "$ref": "#/components/schemas/FeedDtoGenericDataType"
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        },
                        "nullable": true
                    },
                    "videoSettings": {
                        "$ref": "#/components/schemas/VideoSettingsDto"
                    },
                    "masking": {
                        "$ref": "#/components/schemas/MaskDtoGenericDataType"
                    },
                    "videoAsset": {
                        "$ref": "#/components/schemas/VideoAssetDtoGenericDataType"
                    },
                    "checksum": {
                        "$ref": "#/components/schemas/StringGenericDataType"
                    }
                },
                "additionalProperties": false
            },
            "VideoSettingsDto": {
                "required": [
                    "autoplay",
                    "endTime",
                    "loop",
                    "playbackButton",
                    "playbackRate",
                    "restartWithCreative",
                    "sizeMode",
                    "startTime",
                    "stopWithCreative",
                    "volume"
                ],
                "type": "object",
                "properties": {
                    "autoplay": {
                        "type": "boolean"
                    },
                    "loop": {
                        "type": "boolean"
                    },
                    "restartWithCreative": {
                        "type": "boolean"
                    },
                    "stopWithCreative": {
                        "type": "boolean"
                    },
                    "startTime": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "endTime": {
                        "maximum": 9999999,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "playbackRate": {
                        "maximum": 2,
                        "minimum": 0.25,
                        "type": "number",
                        "format": "double"
                    },
                    "sizeMode": {
                        "enum": [
                            "fit",
                            "crop"
                        ],
                        "type": "string"
                    },
                    "playbackButton": {
                        "$ref": "#/components/schemas/PlaybackButtonDto"
                    },
                    "volume": {
                        "maximum": 1,
                        "minimum": 0,
                        "type": "number",
                        "format": "double"
                    },
                    "streaming": {
                        "$ref": "#/components/schemas/StreamingDto"
                    },
                    "optimization": {
                        "$ref": "#/components/schemas/OptimizationDto"
                    }
                },
                "additionalProperties": false
            },
            "WeightsDto": {
                "required": [
                    "totalWeight"
                ],
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "nullable": true
                    },
                    "totalWeight": {
                        "type": "integer",
                        "format": "int32"
                    },
                    "frameworkOverhead": {
                        "$ref": "#/components/schemas/LoadDto"
                    },
                    "initialLoad": {
                        "$ref": "#/components/schemas/LoadDto"
                    },
                    "subLoad": {
                        "$ref": "#/components/schemas/LoadDto"
                    }
                },
                "additionalProperties": false
            },
            "WidgetAssetDto": {
                "required": [
                    "created",
                    "id"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "thumbnail": {
                        "type": "string",
                        "nullable": true
                    },
                    "animatedThumbnail": {
                        "type": "string",
                        "nullable": true
                    },
                    "bannerflowLibraryId": {
                        "type": "string",
                        "nullable": true
                    },
                    "bannerflowLibraryVersionId": {
                        "type": "string",
                        "nullable": true
                    },
                    "exportable": {
                        "type": "boolean",
                        "nullable": true
                    },
                    "modified": {
                        "type": "string",
                        "format": "date-time",
                        "nullable": true
                    },
                    "created": {
                        "type": "string",
                        "format": "date-time"
                    }
                },
                "additionalProperties": false
            },
            "WidgetElementDto": {
                "required": [
                    "customProperties",
                    "states",
                    "type",
                    "widgetContentBlobReference"
                ],
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementDto"
                    }
                ],
                "properties": {
                    "customProperties": {
                        "type": "array",
                        "items": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/CustomPropertyBooleanDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyColorDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyFeedDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyFontDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyImageDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyNumberDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertySelectDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyTextDto"
                                }
                            ]
                        }
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        }
                    },
                    "type": {
                        "enum": [
                            "widget",
                            "bannerflowLibraryWidget",
                            "bannerflowLibraryWidgetInstance"
                        ],
                        "type": "string"
                    },
                    "widgetAsset": {
                        "$ref": "#/components/schemas/WidgetAssetDto"
                    },
                    "widgetContentBlobReference": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "l_WProps": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/LegacyWidgetProperty"
                        },
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "WidgetElementOverrideDto": {
                "type": "object",
                "allOf": [
                    {
                        "$ref": "#/components/schemas/ElementOverrideDto"
                    }
                ],
                "properties": {
                    "customProperties": {
                        "type": "object",
                        "additionalProperties": {
                            "oneOf": [
                                {
                                    "$ref": "#/components/schemas/CustomPropertyBooleanOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyColorOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyFeedOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyFontOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyImageOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyNumberOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertySelectOverrideDto"
                                },
                                {
                                    "$ref": "#/components/schemas/CustomPropertyTextOverrideDto"
                                }
                            ]
                        },
                        "nullable": true
                    },
                    "states": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/GenericStateDto"
                        },
                        "nullable": true
                    }
                },
                "additionalProperties": false
            },
            "WidgetFontStyleDto": {
                "required": [
                    "fontFamilyId",
                    "id",
                    "src",
                    "style",
                    "weight"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "weight": {
                        "type": "number",
                        "format": "double"
                    },
                    "style": {
                        "enum": [
                            "normal",
                            "italic"
                        ],
                        "type": "string"
                    },
                    "src": {
                        "type": "string"
                    },
                    "fontFamilyId": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "WidgetImageDto": {
                "required": [
                    "id",
                    "src"
                ],
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string"
                    },
                    "src": {
                        "type": "string"
                    }
                },
                "additionalProperties": false
            },
            "WidgetSelectionOptionDto": {
                "required": [
                    "selected",
                    "value"
                ],
                "type": "object",
                "properties": {
                    "value": {
                        "minLength": 1,
                        "type": "string"
                    },
                    "selected": {
                        "type": "boolean"
                    }
                },
                "additionalProperties": false
            }
        },
        "securitySchemes": {
            "auth0": {
                "type": "apiKey",
                "description": "Please insert JWT token here prepended by 'auth0'. auth0 [token]",
                "name": "Authorization",
                "in": "header"
            }
        }
    },
    "security": "redacted"
}