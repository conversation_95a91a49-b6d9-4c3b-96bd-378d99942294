/* eslint-disable @typescript-eslint/naming-convention */
export interface paths {
    '/api/creative-sets/{creativeSetId}/creatives/{creativeId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                    creativeId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['UpdateCreativeDto'];
                    'text/json': components['schemas']['UpdateCreativeDto'];
                    'application/*+json': components['schemas']['UpdateCreativeDto'];
                };
            };
            responses: {
                /** @description No Content */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/{creativeSetId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json': components['schemas']['CreativeSetDto'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['CreativeSetDto'];
                    'text/json': components['schemas']['CreativeSetDto'];
                    'application/*+json': components['schemas']['CreativeSetDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['CreativeSetDto'];
                    'text/json': components['schemas']['CreativeSetDto'];
                    'application/*+json': components['schemas']['CreativeSetDto'];
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/validate': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['CreativeSetDto'];
                    'text/json': components['schemas']['CreativeSetDto'];
                    'application/*+json': components['schemas']['CreativeSetDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/{creativeSetId}/sizes': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['CreateSizesDto'];
                    'text/json': components['schemas']['CreateSizesDto'];
                    'application/*+json': components['schemas']['CreateSizesDto'];
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['DeleteSizesDto'];
                    'text/json': components['schemas']['DeleteSizesDto'];
                    'application/*+json': components['schemas']['DeleteSizesDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/{creativeSetId}/sizes/{sizeId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                    sizeId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['UpdateSizeDto'];
                    'text/json': components['schemas']['UpdateSizeDto'];
                    'application/*+json': components['schemas']['UpdateSizeDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/{creativeSetId}/sizes/duplicate': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['DuplicateSizesDto'];
                    'text/json': components['schemas']['DuplicateSizesDto'];
                    'application/*+json': components['schemas']['DuplicateSizesDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/studio-backup/backup-studio-creativeset/{creativesetId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativesetId: number;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/studio-backup/backendtools/backup-studio-creativeset/{creativesetId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativesetId: number;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description No Content */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/studio-backup/versions/{creativeSetId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json': components['schemas']['GetAllStudioBackupVersionIdsQueryResponse'][];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/studio-backup/backup/{creativeSetId}/{versionId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                    versionId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/studio-backup/restore/{creativeSetId}/version/{versionId}': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                    versionId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    '/api/creative-sets/{creativeSetId}/versions': {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['UpdateVersionsDto'];
                    'text/json': components['schemas']['UpdateVersionsDto'];
                    'application/*+json': components['schemas']['UpdateVersionsDto'];
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['CreateVersionsDto'];
                    'text/json': components['schemas']['CreateVersionsDto'];
                    'application/*+json': components['schemas']['CreateVersionsDto'];
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Bad Request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    creativeSetId: number;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    'application/json': components['schemas']['DeleteVersionsDto'];
                    'text/json': components['schemas']['DeleteVersionsDto'];
                    'application/*+json': components['schemas']['DeleteVersionsDto'];
                };
            };
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Forbidden */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Not Found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
                /** @description Internal Server Error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        'application/json':
                            | components['schemas']['ProblemDetails']
                            | components['schemas']['HttpValidationProblemDetails'];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        ActionDto: {
            id: string;
            triggers: (
                | 'click'
                | 'mousedown'
                | 'mouseup'
                | 'mouseenter'
                | 'mouseleave'
                | 'touchstart'
                | 'touchend'
                | 'touchcancel'
            )[];
            disabled: boolean;
            /** @enum {string} */
            templateId?: 'reserved-pressed' | 'reserved-hover';
            operations: components['schemas']['OperationDto'][];
            conditions?: unknown[];
            preventClickThrough?: boolean;
        };
        AnimationDto: {
            keyframes: components['schemas']['KeyframeDto'][];
            id: string;
            name: string;
            /** @enum {string} */
            type?: 'in' | 'out' | 'repeating' | 'action' | 'keyframe';
            /** @enum {string} */
            timingFunction:
                | 'linear'
                | 'easeInExpo'
                | 'easeOutExpo'
                | 'easeInOutExpo'
                | 'easeInQuad'
                | 'easeOutQuad'
                | 'easeInOutQuad'
                | 'easeInCubic'
                | 'easeOutCubic'
                | 'easeInOutCubic'
                | 'easeInQuart'
                | 'easeOutQuart'
                | 'easeInOutQuart'
                | 'easeInQuint'
                | 'easeOutQuint'
                | 'easeInOutQuint'
                | 'easeInElastic'
                | 'easeOutElastic'
                | 'easeInOutElastic'
                | 'easeInBack'
                | 'easeOutBack'
                | 'easeInOutBack'
                | 'easeInBounce'
                | 'easeOutBounce'
                | 'easeInOutBounce';
            /** @enum {string} */
            templateId?:
                | 'fade-in'
                | 'slide-in'
                | 'ascend-in'
                | 'scale-in'
                | 'flip-in'
                | 'blur-in'
                | 'fade-out'
                | 'slide-out'
                | 'descend-out'
                | 'scale-out'
                | 'flip-out'
                | 'blur-out';
            settings?: components['schemas']['SettingDto'];
            hidden: boolean;
        };
        BorderDto: {
            /** Format: double */
            thickness: number;
            /** @enum {string} */
            style: 'solid' | 'dotted' | 'dashed';
            color: string;
        };
        BorderOverrideDto: {
            /** Format: double */
            thickness?: number;
            /** @enum {string} */
            style?: 'solid' | 'dotted' | 'dashed';
            color?: string;
        };
        BorderOverrideDtoGenericDataType: {
            value?: components['schemas']['BorderOverrideDto'];
            isRemoved?: boolean;
        };
        CreateSizeDto: {
            /** Format: int32 */
            width: number;
            /** Format: int32 */
            height: number;
            name?: string;
        };
        CreateSizesDto: {
            sizes: components['schemas']['CreateSizeDto'][];
        };
        CreateVersionDto: {
            name: string;
            localizationId: string;
            targetUrl: string;
            elements: components['schemas']['ElementTextSegmentDto'][];
        };
        CreateVersionsDto: {
            versions: components['schemas']['CreateVersionDto'][];
        };
        CreativeDto: {
            /** Format: int32 */
            id: number;
            elements: {
                [key: string]:
                    | components['schemas']['WidgetElementOverrideDto']
                    | components['schemas']['EllipseElementOverrideDto']
                    | components['schemas']['GroupNodeOverrideDto']
                    | components['schemas']['ImageElementOverrideDto']
                    | components['schemas']['RectangleElementOverrideDto']
                    | components['schemas']['TextLikeElementOverrideDto']
                    | components['schemas']['VideoElementOverrideDto'];
            };
            /** Format: int32 */
            sizeId: number;
            /** Format: int32 */
            versionId: number;
            targetUrl?: string;
            checksum?: string;
            /** @enum {string} */
            approvalStatus?: 'In progress' | 'For review' | 'Not approved' | 'Approved';
            creativeWeights?: components['schemas']['CreativeWeightsDto'];
        };
        CreativeSetDto: {
            /** Format: int32 */
            id: number;
            brandId: string;
            name: string;
            stateId?: string;
            creatives: components['schemas']['CreativeDto'][];
            sizes: components['schemas']['SizeDto'][];
            versions: components['schemas']['VersionDto'][];
            elementsPool: (
                | components['schemas']['EllipseElementDto']
                | components['schemas']['GroupNodeDto']
                | components['schemas']['ImageElementDto']
                | components['schemas']['RectangleElementDto']
                | components['schemas']['TextLikeElementDto']
                | components['schemas']['VideoElementDto']
                | components['schemas']['WidgetElementDto']
            )[];
            /** Format: int32 */
            defaultVersionId: number;
            fonts: components['schemas']['FontFamilyDto'][];
            lastModified: string;
        };
        CreativeSocialGuideDto: {
            /** @enum {string} */
            placement:
                | 'meta-instagram'
                | 'meta-facebook'
                | 'meta-messenger'
                | 'meta-facebookreels'
                | 'meta-instagramreels'
                | 'tiktok-default'
                | 'tiktok-after-9s'
                | 'tiktok-after-9s-with-card'
                | 'browse-image'
                | 'browse-video'
                | 'watch-video'
                | 'snapchat-default';
            /** @enum {string} */
            network: 'meta' | 'tiktok' | 'pinterest' | 'snapchat';
            guidelines: boolean;
            overlay: boolean;
        };
        CreativeWeightsDto: {
            id: string;
            creativeId: string;
            creativeChecksum: string;
            weights?: components['schemas']['WeightsDto'];
            failed: boolean;
        };
        CustomPropertyBooleanDto: {
            value?: boolean;
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyBooleanDto';
        });
        CustomPropertyBooleanOverrideDto: {
            value: boolean;
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyBooleanOverrideDto';
        });
        CustomPropertyColorDto: {
            value?: string;
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyColorDto';
        });
        CustomPropertyColorOverrideDto: {
            value: string;
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyColorOverrideDto';
        });
        CustomPropertyDto: {
            $type: string;
            name: string;
            label: string;
            hasDynamicContent?: boolean;
        };
        CustomPropertyFeedDto: {
            value?: components['schemas']['FeedDto'];
            l_VpId?: string;
            l_VpName?: string;
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyFeedDto';
        });
        CustomPropertyFeedOverrideDto: {
            value: components['schemas']['FeedDto'];
            l_VpId?: string;
            l_VpName?: string;
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyFeedOverrideDto';
        });
        CustomPropertyFontDto: {
            value?: components['schemas']['WidgetFontStyleDto'];
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyFontDto';
        });
        CustomPropertyFontOverrideDto: {
            value: components['schemas']['WidgetFontStyleDto'];
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyFontOverrideDto';
        });
        CustomPropertyImageDto: {
            value?: components['schemas']['WidgetImageDto'];
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyImageDto';
        });
        CustomPropertyImageOverrideDto: {
            value: components['schemas']['WidgetImageDto'];
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyImageOverrideDto';
        });
        CustomPropertyNumberDto: {
            /** Format: double */
            value?: number;
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyNumberDto';
        });
        CustomPropertyNumberOverrideDto: {
            /** Format: double */
            value: number;
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyNumberOverrideDto';
        });
        CustomPropertyOverrideDto: {
            $type: string;
        };
        CustomPropertySelectDto: {
            value?: components['schemas']['WidgetSelectionOptionDto'][];
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertySelectDto';
        });
        CustomPropertySelectOverrideDto: {
            value: components['schemas']['WidgetSelectionOptionDto'][];
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertySelectOverrideDto';
        });
        CustomPropertyTextDto: {
            value?: string;
            l_VpId?: string;
            l_VpName?: string;
        } & (Omit<components['schemas']['CustomPropertyDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyTextDto';
        });
        CustomPropertyTextOverrideDto: {
            value: string;
            l_VpId?: string;
            l_VpName?: string;
        } & (Omit<components['schemas']['CustomPropertyOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'CustomPropertyTextOverrideDto';
        });
        DecimalGenericStructType: {
            /** Format: double */
            value?: number;
            isRemoved?: boolean;
        };
        DeleteSizesDto: {
            ids: number[];
        };
        DeleteVersionsDto: {
            ids: number[];
        };
        DesignDto: {
            name: string;
            fill: string;
            /** Format: int32 */
            loops: number;
            audio?: boolean;
            guidelines: components['schemas']['GuidelineDto'][];
            socialGuide?: components['schemas']['CreativeSocialGuideDto'];
            gifExport: components['schemas']['GifExportDto'];
            preloadImage: components['schemas']['PreloadImageDto'];
            /** Format: double */
            startTime?: number;
            /** Format: double */
            stopTime?: number;
            l_DesId?: string;
            l_DocId?: string;
        };
        DuplicateSizesDto: {
            ids: number[];
        };
        DynamicContentDto: {
            id: string;
            label: string;
            value: string;
        };
        ElementDto: {
            $type: string;
            /** Format: double */
            time: number;
            /** Format: double */
            duration: number;
            parentId?: string;
            /** Format: double */
            ratio?: number;
            /** Format: int32 */
            x: number;
            /** Format: int32 */
            y: number;
            /** Format: int32 */
            width: number;
            /** Format: int32 */
            height: number;
            fill?: string;
            /** Format: double */
            originX: number;
            /** Format: double */
            originY: number;
            /** Format: double */
            rotationX: number;
            /** Format: double */
            rotationY: number;
            /** Format: double */
            rotationZ: number;
            radius: components['schemas']['RadiusDto'];
            /** Format: double */
            opacity: number;
            mirrorX: boolean;
            mirrorY: boolean;
            /** Format: double */
            scaleX: number;
            /** Format: double */
            scaleY: number;
            shadows?: components['schemas']['ShadowDto'][];
            border?: components['schemas']['BorderDto'];
            animations: components['schemas']['AnimationDto'][];
            actions: components['schemas']['ActionDto'][];
            filters: components['schemas']['FilterDto'];
        } & (Omit<WithRequired<components['schemas']['NodeDto'], '$type'>, '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'ElementDto';
        });
        ElementOverrideDto: {
            $type: string;
            /** Format: double */
            time?: number;
            /** Format: double */
            duration?: number;
            parentId?: components['schemas']['StringGenericDataType'];
            ratio?: components['schemas']['DecimalGenericStructType'];
            /** Format: int32 */
            x?: number;
            /** Format: int32 */
            y?: number;
            /** Format: int32 */
            width?: number;
            /** Format: int32 */
            height?: number;
            fill?: components['schemas']['StringGenericDataType'];
            /** Format: double */
            originX?: number;
            /** Format: double */
            originY?: number;
            /** Format: double */
            rotationX?: number;
            /** Format: double */
            rotationY?: number;
            /** Format: double */
            rotationZ?: number;
            radius?: components['schemas']['RadiusOverrideDto'];
            /** Format: double */
            opacity?: number;
            mirrorX?: boolean;
            mirrorY?: boolean;
            /** Format: double */
            scaleX?: number;
            /** Format: double */
            scaleY?: number;
            shadows?: components['schemas']['ShadowDto'][];
            border?: components['schemas']['BorderOverrideDtoGenericDataType'];
            animations?: components['schemas']['AnimationDto'][];
            actions?: components['schemas']['ActionDto'][];
            filters?: components['schemas']['FilterOverrideDto'];
        } & (Omit<WithRequired<components['schemas']['NodeOverrideDto'], '$type'>, '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'ElementOverrideDto';
        });
        ElementTextSegmentDto: {
            id: string;
            textSegments: components['schemas']['TextSegmentDto'][];
        };
        EllipseElementDto: {
            states: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDto'];
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'EllipseElementDto';
        });
        EllipseElementOverrideDto: {
            states?: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDtoGenericDataType'];
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'EllipseElementOverrideDto';
        });
        FeedDto: {
            id: string;
            path: string;
            step: components['schemas']['FeedStepDto'];
            fallback: string;
            /** @enum {string} */
            type: 'text' | 'number' | 'image' | 'video';
            legacyFeededReference?: components['schemas']['LegacyFeededReference'];
        };
        FeedDtoGenericDataType: {
            value?: components['schemas']['FeedDto'];
            isRemoved?: boolean;
        };
        FeedStepDto: {
            /** @enum {string} */
            occurrence: 'none' | 'loop';
            /** Format: int32 */
            size: number;
            /** Format: int32 */
            start: number;
        };
        FilterDto: {
            /** Format: double */
            blur?: number;
            /** Format: double */
            contrast?: number;
            /** Format: double */
            invert?: number;
            /** Format: double */
            saturate?: number;
            /** Format: double */
            grayscale?: number;
            /** Format: double */
            sepia?: number;
        };
        FilterOverrideDto: {
            blur?: components['schemas']['DecimalGenericStructType'];
            contrast?: components['schemas']['DecimalGenericStructType'];
            invert?: components['schemas']['DecimalGenericStructType'];
            saturate?: components['schemas']['DecimalGenericStructType'];
            grayscale?: components['schemas']['DecimalGenericStructType'];
            sepia?: components['schemas']['DecimalGenericStructType'];
        };
        FontFamilyDto: {
            name: string;
            fontStyles: components['schemas']['FontStyleDto'][];
        };
        FontStyleDto: {
            id: string;
            name: string;
            fontUrl: string;
        };
        FrameDto: {
            /** Format: double */
            time: number;
            /** Format: double */
            duration?: number;
        };
        GenericStateDto: components['schemas']['StateDto'];
        GetAllStudioBackupVersionIdsQueryResponse: {
            blobVersionId: string;
            /** Format: int32 */
            sizeCount: number;
            /** Format: int32 */
            versionCount: number;
            userName: string;
        };
        GifExportDto: {
            frames: components['schemas']['FrameDto'][];
            show: boolean;
        };
        GroupNodeDto: Omit<components['schemas']['NodeDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'GroupNodeDto';
        };
        GroupNodeOverrideDto: Omit<components['schemas']['NodeOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'GroupNodeOverrideDto';
        };
        GuidelineDto: {
            id: string;
            position: components['schemas']['PositionDto'];
            /** @enum {string} */
            type: 'vertical' | 'horizontal';
            preview: boolean;
        };
        HttpValidationProblemDetails: ({
            errors: {
                [key: string]: string[];
            };
        } & {
            [key: string]: unknown;
        }) &
            (Omit<components['schemas']['ProblemDetails'], '$type'> & {
                /**
                 * @description discriminator enum property added by openapi-typescript
                 * @enum {string}
                 */
                $type: 'HttpValidationProblemDetails';
            });
        ImageAssetDto: {
            /** Format: int32 */
            id: number;
            name: string;
            original: components['schemas']['ImageDto'];
            thumbnail: components['schemas']['ImageDto'];
            animatedThumbnail?: components['schemas']['ImageDto'];
            /** Format: int32 */
            fileSize: number;
            /** Format: date-time */
            created: string;
            /** Format: date-time */
            modified?: string;
            isGenAi?: boolean;
        };
        ImageAssetDtoGenericDataType: {
            value?: components['schemas']['ImageAssetDto'];
            isRemoved?: boolean;
        };
        ImageDto: {
            url: string;
            /** Format: int32 */
            width: number;
            /** Format: int32 */
            height: number;
        };
        ImageElementDto: {
            imageSettings: components['schemas']['ImageSettingsDto'];
            feed?: components['schemas']['FeedDto'];
            dynamicContent?: components['schemas']['DynamicContentDto'];
            states: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDto'];
            imageAsset?: components['schemas']['ImageAssetDto'];
            l_IEPropId?: string;
            l_DEPropId?: string;
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'ImageElementDto';
        });
        ImageElementOverrideDto: {
            imageSettings?: components['schemas']['ImageSettingsDto'];
            feed?: components['schemas']['FeedDtoGenericDataType'];
            dynamicContent?: components['schemas']['DynamicContentDto'];
            states?: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDtoGenericDataType'];
            imageAsset?: components['schemas']['ImageAssetDtoGenericDataType'];
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'ImageElementOverrideDto';
        });
        ImageSettingsDto: {
            /** Format: double */
            x: number;
            /** Format: double */
            y: number;
            /** Format: int32 */
            quality?: number;
            /** @enum {string} */
            sizeMode: 'fit' | 'stretch' | 'crop';
            highDpi?: boolean;
        };
        KeyframeDto: {
            id: string;
            stateId?: string;
            /** Format: double */
            time: number;
            /** Format: double */
            duration: number;
            /** @enum {string} */
            timingFunction?:
                | '@timingFunction'
                | 'linear'
                | 'easeInExpo'
                | 'easeOutExpo'
                | 'easeInOutExpo'
                | 'easeInQuad'
                | 'easeOutQuad'
                | 'easeInOutQuad'
                | 'easeInCubic'
                | 'easeOutCubic'
                | 'easeInOutCubic'
                | 'easeInQuart'
                | 'easeOutQuart'
                | 'easeInOutQuart'
                | 'easeInQuint'
                | 'easeOutQuint'
                | 'easeInOutQuint'
                | 'easeInElastic'
                | 'easeOutElastic'
                | 'easeInOutElastic'
                | 'easeInBack'
                | 'easeOutBack'
                | 'easeInOutBack'
                | 'easeInBounce'
                | 'easeOutBounce'
                | 'easeInOutBounce';
        };
        LegacyFeededReference: {
            id: string;
            clientId: string;
            name: string;
            /** @enum {string} */
            unit: 'id';
            value: string;
        };
        LegacyWidgetProperty: {
            id: string;
            name: string;
            label?: string;
            hasDynamicContent?: boolean;
        };
        LoadAssetDto: {
            /** Format: int32 */
            weight: number;
            name: string;
            url: string;
            parentUrl: string;
            /** @enum {string} */
            type: 'Application' | 'Audio' | 'Font' | 'Image' | 'Text' | 'Unknown' | 'Video';
        };
        LoadDto: {
            /** Format: int32 */
            totalWeight: number;
            assets: components['schemas']['LoadAssetDto'][];
        };
        MaskDto: {
            isMask: boolean;
            elementId?: string;
        };
        MaskDtoGenericDataType: {
            value?: components['schemas']['MaskDto'];
            isRemoved?: boolean;
        };
        NodeDto: {
            $type: string;
            id: string;
            parentNodeId?: string;
            locked: boolean;
            hidden: boolean;
            name: string;
        };
        NodeOverrideDto: {
            $type: string;
            /** Format: int32 */
            sortIndex?: number;
            parentNodeId?: components['schemas']['StringGenericDataType'];
            locked?: boolean;
            hidden?: boolean;
        };
        OperationAnimationDto: {
            /** @enum {string} */
            timingFunction:
                | 'linear'
                | 'easeInExpo'
                | 'easeOutExpo'
                | 'easeInOutExpo'
                | 'easeInQuad'
                | 'easeOutQuad'
                | 'easeInOutQuad'
                | 'easeInCubic'
                | 'easeOutCubic'
                | 'easeInOutCubic'
                | 'easeInQuart'
                | 'easeOutQuart'
                | 'easeInOutQuart'
                | 'easeInQuint'
                | 'easeOutQuint'
                | 'easeInOutQuint'
                | 'easeInElastic'
                | 'easeOutElastic'
                | 'easeInOutElastic'
                | 'easeInBack'
                | 'easeOutBack'
                | 'easeInOutBack'
                | 'easeInBounce'
                | 'easeOutBounce'
                | 'easeInOutBounce';
            /** Format: double */
            duration: number;
        };
        OperationDto: {
            /** @enum {string} */
            method: 'SET_STATE' | 'REMOVE_STATE' | 'CLEAR_STATES' | 'OPEN_URL';
            value?: string;
            target?: string;
            animation?: components['schemas']['OperationAnimationDto'];
        };
        OptimizationDto: {
            enabled: boolean;
            /** Format: int32 */
            quality?: number;
        };
        PaddingDto: {
            /** Format: double */
            top: number;
            /** Format: double */
            left: number;
            /** Format: double */
            right: number;
            /** Format: double */
            bottom: number;
        };
        PaddingOverrideDto: {
            /** Format: double */
            top?: number;
            /** Format: double */
            left?: number;
            /** Format: double */
            right?: number;
            /** Format: double */
            bottom?: number;
        };
        PlaybackButtonDto: {
            enabled: boolean;
            /** Format: int32 */
            size: number;
            color: string;
        };
        PositionDto: {
            /** Format: int32 */
            x: number;
            /** Format: int32 */
            y: number;
        };
        PreloadImageDto: {
            /** Format: int32 */
            quality: number;
            /** @enum {string} */
            format: 'jpg' | 'gif' | 'png';
            frames: number[];
        };
        ProblemDetails: {
            $type: string;
            type?: string;
            title?: string;
            /** Format: int32 */
            status?: number;
            detail?: string;
            instance?: string;
        } & {
            [key: string]: unknown;
        };
        RadiusDto: {
            /** @enum {string} */
            type: 'joint' | 'separate';
            /** Format: double */
            topLeft: number;
            /** Format: double */
            topRight: number;
            /** Format: double */
            bottomRight: number;
            /** Format: double */
            bottomLeft: number;
        };
        RadiusOverrideDto: {
            /** @enum {string} */
            type?: 'joint' | 'separate';
            /** Format: double */
            topLeft?: number;
            /** Format: double */
            topRight?: number;
            /** Format: double */
            bottomRight?: number;
            /** Format: double */
            bottomLeft?: number;
        };
        RectangleElementDto: {
            states: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDto'];
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'RectangleElementDto';
        });
        RectangleElementOverrideDto: {
            states?: components['schemas']['GenericStateDto'][];
            masking?: components['schemas']['MaskDtoGenericDataType'];
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'RectangleElementOverrideDto';
        });
        SettingDto: {
            direction?: components['schemas']['SettingDtoKind'];
            distance?: components['schemas']['SettingDtoKind'];
        };
        SettingDtoKind: {
            name: string;
            /** Format: double */
            value: number;
        };
        ShadowDto: {
            /** Format: double */
            offsetX: number;
            /** Format: double */
            offsetY: number;
            color: string;
            /** Format: double */
            blur: number;
            /** Format: double */
            spread: number;
        };
        SizeDto: {
            /** Format: int32 */
            id: number;
            /** Format: int32 */
            width: number;
            /** Format: int32 */
            height: number;
            name?: string;
            design?: components['schemas']['DesignDto'];
            elements: {
                [key: string]:
                    | components['schemas']['WidgetElementOverrideDto']
                    | components['schemas']['EllipseElementOverrideDto']
                    | components['schemas']['GroupNodeOverrideDto']
                    | components['schemas']['ImageElementOverrideDto']
                    | components['schemas']['RectangleElementOverrideDto']
                    | components['schemas']['TextLikeElementOverrideDto']
                    | components['schemas']['VideoElementOverrideDto'];
            };
        };
        StateDto: {
            id: string;
            name?: string;
            /** Format: double */
            ratio?: number;
            x?: string;
            y?: string;
            /** Format: int32 */
            width?: number;
            /** Format: int32 */
            height?: number;
            fill?: string;
            /** Format: double */
            originX?: number;
            /** Format: double */
            originY?: number;
            rotationX?: string;
            rotationY?: string;
            rotationZ?: string;
            radius?: components['schemas']['RadiusDto'];
            /** Format: double */
            opacity?: number;
            mirrorX?: boolean;
            mirrorY?: boolean;
            /** Format: double */
            scaleX?: number;
            /** Format: double */
            scaleY?: number;
            shadows?: components['schemas']['ShadowDto'][];
            border?: components['schemas']['BorderDto'];
            filters?: components['schemas']['FilterDto'];
        };
        StreamingDto: {
            enabled: boolean;
        };
        StringGenericDataType: {
            value?: string;
            isRemoved?: boolean;
        };
        TextLikeElementDto: {
            textSegments: components['schemas']['TextSegmentDto'][];
            dynamicContent?: components['schemas']['DynamicContentDto'];
            font?: string;
            textColor: string;
            /** Format: double */
            fontSize: number;
            /** Format: double */
            lineHeight: number;
            /** Format: double */
            characterSpacing: number;
            uppercase: boolean;
            underline: boolean;
            strikethrough: boolean;
            /** Format: int32 */
            maxRows: number;
            padding: components['schemas']['PaddingDto'];
            /** @enum {string} */
            textOverflow: 'shrink' | 'truncate' | 'expand' | 'scroll';
            /** @enum {string} */
            horizontalAlignment: 'left' | 'center' | 'right' | 'justify';
            /** @enum {string} */
            verticalAlignment: 'top' | 'middle' | 'bottom';
            textShadows?: components['schemas']['TextShadowDto'][];
            states: components['schemas']['TextStateDto'][];
            isButton: boolean;
            l_TEPId?: string;
            l_VpId?: string;
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'TextLikeElementDto';
        });
        TextLikeElementOverrideDto: {
            textSegments?: components['schemas']['TextSegmentDto'][];
            dynamicContent?: components['schemas']['DynamicContentDto'];
            font?: components['schemas']['StringGenericDataType'];
            textColor?: string;
            /** Format: double */
            fontSize?: number;
            /** Format: double */
            lineHeight?: number;
            /** Format: double */
            characterSpacing?: number;
            uppercase?: boolean;
            underline?: boolean;
            strikethrough?: boolean;
            /** Format: int32 */
            maxRows?: number;
            padding?: components['schemas']['PaddingOverrideDto'];
            /** @enum {string} */
            textOverflow?: 'shrink' | 'truncate' | 'expand' | 'scroll';
            /** @enum {string} */
            horizontalAlignment?: 'left' | 'center' | 'right' | 'justify';
            /** @enum {string} */
            verticalAlignment?: 'top' | 'middle' | 'bottom';
            textShadows?: components['schemas']['TextShadowDto'][];
            states?: components['schemas']['TextStateDto'][];
            l_VpId?: string;
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'TextLikeElementOverrideDto';
        });
        TextSegmentDto: {
            content: string;
            styles?: components['schemas']['TextStyleValueDto'];
            variable?: components['schemas']['VariableDto'];
            dynamicContent?: components['schemas']['DynamicContentDto'];
            l_StyleId?: string;
        };
        TextShadowDto: {
            /** Format: double */
            offsetX: number;
            /** Format: double */
            offsetY: number;
            /** Format: double */
            blur: number;
            color: string;
        };
        TextStateDto: {
            textColor?: string;
        } & components['schemas']['StateDto'];
        TextStyleValueDto: {
            font?: string;
            textColor?: string;
            /** Format: double */
            fontSize?: number;
            /** Format: double */
            lineHeight?: number;
            /** Format: double */
            characterSpacing?: number;
            uppercase?: boolean;
            underline?: boolean;
            strikethrough?: boolean;
            textShadows?: components['schemas']['TextShadowDto'][];
        };
        UpdateCreativeDto: {
            targetUrl: string;
            approvalStatus: string;
        };
        UpdateSizeDto: {
            name: string;
        };
        UpdateVersionDto: {
            /** Format: int32 */
            versionId: number;
            name: string;
            localizationId: string;
            targetUrl: string;
        };
        UpdateVersionsDto: {
            versions: components['schemas']['UpdateVersionDto'][];
        };
        VariableDto: {
            id: string;
            path: string;
            step: components['schemas']['FeedStepDto'];
            fallback: string;
            type: string;
            spanId?: string;
        };
        VersionDto: {
            /** Format: int32 */
            id: number;
            name: string;
            localizationId: string;
            targetUrl: string;
            elements: {
                [key: string]:
                    | components['schemas']['WidgetElementOverrideDto']
                    | components['schemas']['EllipseElementOverrideDto']
                    | components['schemas']['GroupNodeOverrideDto']
                    | components['schemas']['ImageElementOverrideDto']
                    | components['schemas']['RectangleElementOverrideDto']
                    | components['schemas']['TextLikeElementOverrideDto']
                    | components['schemas']['VideoElementOverrideDto'];
            };
        };
        VideoAssetDto: {
            /** Format: int32 */
            id: number;
            name: string;
            url: string;
            /** Format: int64 */
            fileSize: number;
            /** Format: int32 */
            width: number;
            /** Format: int32 */
            height: number;
            /** Format: double */
            durationInMilliseconds: number;
            thumbnail: components['schemas']['ImageDto'];
            /** Format: date-time */
            created: string;
            /** Format: date-time */
            modified?: string;
        };
        VideoAssetDtoGenericDataType: {
            value?: components['schemas']['VideoAssetDto'];
            isRemoved?: boolean;
        };
        VideoElementDto: {
            feed?: components['schemas']['FeedDto'];
            states: components['schemas']['GenericStateDto'][];
            videoSettings: components['schemas']['VideoSettingsDto'];
            masking?: components['schemas']['MaskDto'];
            videoAsset?: components['schemas']['VideoAssetDto'];
            checksum?: string;
            l_VEPId?: string;
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'VideoElementDto';
        });
        VideoElementOverrideDto: {
            feed?: components['schemas']['FeedDtoGenericDataType'];
            states?: components['schemas']['GenericStateDto'][];
            videoSettings?: components['schemas']['VideoSettingsDto'];
            masking?: components['schemas']['MaskDtoGenericDataType'];
            videoAsset?: components['schemas']['VideoAssetDtoGenericDataType'];
            checksum?: components['schemas']['StringGenericDataType'];
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'VideoElementOverrideDto';
        });
        VideoSettingsDto: {
            autoplay: boolean;
            loop: boolean;
            restartWithCreative: boolean;
            stopWithCreative: boolean;
            /** Format: double */
            startTime: number;
            /** Format: double */
            endTime: number;
            /** Format: double */
            playbackRate: number;
            /** @enum {string} */
            sizeMode: 'fit' | 'crop';
            playbackButton: components['schemas']['PlaybackButtonDto'];
            /** Format: double */
            volume: number;
            streaming?: components['schemas']['StreamingDto'];
            optimization?: components['schemas']['OptimizationDto'];
        };
        WeightsDto: {
            url?: string;
            /** Format: int32 */
            totalWeight: number;
            frameworkOverhead?: components['schemas']['LoadDto'];
            initialLoad?: components['schemas']['LoadDto'];
            subLoad?: components['schemas']['LoadDto'];
        };
        WidgetAssetDto: {
            id: string;
            thumbnail?: string;
            animatedThumbnail?: string;
            bannerflowLibraryId?: string;
            bannerflowLibraryVersionId?: string;
            exportable?: boolean;
            /** Format: date-time */
            modified?: string;
            /** Format: date-time */
            created: string;
        };
        WidgetElementDto: {
            customProperties: (
                | components['schemas']['CustomPropertyBooleanDto']
                | components['schemas']['CustomPropertyColorDto']
                | components['schemas']['CustomPropertyFeedDto']
                | components['schemas']['CustomPropertyFontDto']
                | components['schemas']['CustomPropertyImageDto']
                | components['schemas']['CustomPropertyNumberDto']
                | components['schemas']['CustomPropertySelectDto']
                | components['schemas']['CustomPropertyTextDto']
            )[];
            states: components['schemas']['GenericStateDto'][];
            /** @enum {string} */
            type: 'widget' | 'bannerflowLibraryWidget' | 'bannerflowLibraryWidgetInstance';
            widgetAsset?: components['schemas']['WidgetAssetDto'];
            widgetContentBlobReference: string;
            l_WProps?: components['schemas']['LegacyWidgetProperty'][];
        } & (Omit<components['schemas']['ElementDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'WidgetElementDto';
        });
        WidgetElementOverrideDto: {
            customProperties?: {
                [key: string]:
                    | components['schemas']['CustomPropertyBooleanOverrideDto']
                    | components['schemas']['CustomPropertyColorOverrideDto']
                    | components['schemas']['CustomPropertyFeedOverrideDto']
                    | components['schemas']['CustomPropertyFontOverrideDto']
                    | components['schemas']['CustomPropertyImageOverrideDto']
                    | components['schemas']['CustomPropertyNumberOverrideDto']
                    | components['schemas']['CustomPropertySelectOverrideDto']
                    | components['schemas']['CustomPropertyTextOverrideDto'];
            };
            states?: components['schemas']['GenericStateDto'][];
        } & (Omit<components['schemas']['ElementOverrideDto'], '$type'> & {
            /**
             * @description discriminator enum property added by openapi-typescript
             * @enum {string}
             */
            $type: 'WidgetElementOverrideDto';
        });
        WidgetFontStyleDto: {
            id: string;
            /** Format: double */
            weight: number;
            /** @enum {string} */
            style: 'normal' | 'italic';
            src: string;
            fontFamilyId: string;
        };
        WidgetImageDto: {
            id: string;
            src: string;
        };
        WidgetSelectionOptionDto: {
            value: string;
            selected: boolean;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type ActionDto = components['schemas']['ActionDto'];
export type AnimationDto = components['schemas']['AnimationDto'];
export type BorderDto = components['schemas']['BorderDto'];
export type BorderOverrideDto = components['schemas']['BorderOverrideDto'];
export type BorderOverrideDtoGenericDataType =
    components['schemas']['BorderOverrideDtoGenericDataType'];
export type CreateSizeDto = components['schemas']['CreateSizeDto'];
export type CreateSizesDto = components['schemas']['CreateSizesDto'];
export type CreateVersionDto = components['schemas']['CreateVersionDto'];
export type CreateVersionsDto = components['schemas']['CreateVersionsDto'];
export type CreativeDto = components['schemas']['CreativeDto'];
export type CreativeSetDto = components['schemas']['CreativeSetDto'];
export type CreativeSocialGuideDto = components['schemas']['CreativeSocialGuideDto'];
export type CreativeWeightsDto = components['schemas']['CreativeWeightsDto'];
export type CustomPropertyBooleanDto = components['schemas']['CustomPropertyBooleanDto'];
export type CustomPropertyBooleanOverrideDto =
    components['schemas']['CustomPropertyBooleanOverrideDto'];
export type CustomPropertyColorDto = components['schemas']['CustomPropertyColorDto'];
export type CustomPropertyColorOverrideDto = components['schemas']['CustomPropertyColorOverrideDto'];
export type CustomPropertyDto = components['schemas']['CustomPropertyDto'];
export type CustomPropertyFeedDto = components['schemas']['CustomPropertyFeedDto'];
export type CustomPropertyFeedOverrideDto = components['schemas']['CustomPropertyFeedOverrideDto'];
export type CustomPropertyFontDto = components['schemas']['CustomPropertyFontDto'];
export type CustomPropertyFontOverrideDto = components['schemas']['CustomPropertyFontOverrideDto'];
export type CustomPropertyImageDto = components['schemas']['CustomPropertyImageDto'];
export type CustomPropertyImageOverrideDto = components['schemas']['CustomPropertyImageOverrideDto'];
export type CustomPropertyNumberDto = components['schemas']['CustomPropertyNumberDto'];
export type CustomPropertyNumberOverrideDto = components['schemas']['CustomPropertyNumberOverrideDto'];
export type CustomPropertyOverrideDto = components['schemas']['CustomPropertyOverrideDto'];
export type CustomPropertySelectDto = components['schemas']['CustomPropertySelectDto'];
export type CustomPropertySelectOverrideDto = components['schemas']['CustomPropertySelectOverrideDto'];
export type CustomPropertyTextDto = components['schemas']['CustomPropertyTextDto'];
export type CustomPropertyTextOverrideDto = components['schemas']['CustomPropertyTextOverrideDto'];
export type DecimalGenericStructType = components['schemas']['DecimalGenericStructType'];
export type DeleteSizesDto = components['schemas']['DeleteSizesDto'];
export type DeleteVersionsDto = components['schemas']['DeleteVersionsDto'];
export type DesignDto = components['schemas']['DesignDto'];
export type DuplicateSizesDto = components['schemas']['DuplicateSizesDto'];
export type DynamicContentDto = components['schemas']['DynamicContentDto'];
export type ElementDto = components['schemas']['ElementDto'];
export type ElementOverrideDto = components['schemas']['ElementOverrideDto'];
export type ElementTextSegmentDto = components['schemas']['ElementTextSegmentDto'];
export type EllipseElementDto = components['schemas']['EllipseElementDto'];
export type EllipseElementOverrideDto = components['schemas']['EllipseElementOverrideDto'];
export type FeedDto = components['schemas']['FeedDto'];
export type FeedDtoGenericDataType = components['schemas']['FeedDtoGenericDataType'];
export type FeedStepDto = components['schemas']['FeedStepDto'];
export type FilterDto = components['schemas']['FilterDto'];
export type FilterOverrideDto = components['schemas']['FilterOverrideDto'];
export type FontFamilyDto = components['schemas']['FontFamilyDto'];
export type FontStyleDto = components['schemas']['FontStyleDto'];
export type FrameDto = components['schemas']['FrameDto'];
export type GenericStateDto = components['schemas']['GenericStateDto'];
export type GetAllStudioBackupVersionIdsQueryResponse =
    components['schemas']['GetAllStudioBackupVersionIdsQueryResponse'];
export type GifExportDto = components['schemas']['GifExportDto'];
export type GroupNodeDto = components['schemas']['GroupNodeDto'];
export type GroupNodeOverrideDto = components['schemas']['GroupNodeOverrideDto'];
export type GuidelineDto = components['schemas']['GuidelineDto'];
export type HttpValidationProblemDetails = components['schemas']['HttpValidationProblemDetails'];
export type ImageAssetDto = components['schemas']['ImageAssetDto'];
export type ImageAssetDtoGenericDataType = components['schemas']['ImageAssetDtoGenericDataType'];
export type ImageDto = components['schemas']['ImageDto'];
export type ImageElementDto = components['schemas']['ImageElementDto'];
export type ImageElementOverrideDto = components['schemas']['ImageElementOverrideDto'];
export type ImageSettingsDto = components['schemas']['ImageSettingsDto'];
export type KeyframeDto = components['schemas']['KeyframeDto'];
export type LegacyFeededReference = components['schemas']['LegacyFeededReference'];
export type LegacyWidgetProperty = components['schemas']['LegacyWidgetProperty'];
export type LoadAssetDto = components['schemas']['LoadAssetDto'];
export type LoadDto = components['schemas']['LoadDto'];
export type MaskDto = components['schemas']['MaskDto'];
export type MaskDtoGenericDataType = components['schemas']['MaskDtoGenericDataType'];
export type NodeDto = components['schemas']['NodeDto'];
export type NodeOverrideDto = components['schemas']['NodeOverrideDto'];
export type OperationAnimationDto = components['schemas']['OperationAnimationDto'];
export type OperationDto = components['schemas']['OperationDto'];
export type OptimizationDto = components['schemas']['OptimizationDto'];
export type PaddingDto = components['schemas']['PaddingDto'];
export type PaddingOverrideDto = components['schemas']['PaddingOverrideDto'];
export type PlaybackButtonDto = components['schemas']['PlaybackButtonDto'];
export type PositionDto = components['schemas']['PositionDto'];
export type PreloadImageDto = components['schemas']['PreloadImageDto'];
export type ProblemDetails = components['schemas']['ProblemDetails'];
export type RadiusDto = components['schemas']['RadiusDto'];
export type RadiusOverrideDto = components['schemas']['RadiusOverrideDto'];
export type RectangleElementDto = components['schemas']['RectangleElementDto'];
export type RectangleElementOverrideDto = components['schemas']['RectangleElementOverrideDto'];
export type SettingDto = components['schemas']['SettingDto'];
export type SettingDtoKind = components['schemas']['SettingDtoKind'];
export type ShadowDto = components['schemas']['ShadowDto'];
export type SizeDto = components['schemas']['SizeDto'];
export type StateDto = components['schemas']['StateDto'];
export type StreamingDto = components['schemas']['StreamingDto'];
export type StringGenericDataType = components['schemas']['StringGenericDataType'];
export type TextLikeElementDto = components['schemas']['TextLikeElementDto'];
export type TextLikeElementOverrideDto = components['schemas']['TextLikeElementOverrideDto'];
export type TextSegmentDto = components['schemas']['TextSegmentDto'];
export type TextShadowDto = components['schemas']['TextShadowDto'];
export type TextStateDto = components['schemas']['TextStateDto'];
export type TextStyleValueDto = components['schemas']['TextStyleValueDto'];
export type UpdateCreativeDto = components['schemas']['UpdateCreativeDto'];
export type UpdateSizeDto = components['schemas']['UpdateSizeDto'];
export type UpdateVersionDto = components['schemas']['UpdateVersionDto'];
export type UpdateVersionsDto = components['schemas']['UpdateVersionsDto'];
export type VariableDto = components['schemas']['VariableDto'];
export type VersionDto = components['schemas']['VersionDto'];
export type VideoAssetDto = components['schemas']['VideoAssetDto'];
export type VideoAssetDtoGenericDataType = components['schemas']['VideoAssetDtoGenericDataType'];
export type VideoElementDto = components['schemas']['VideoElementDto'];
export type VideoElementOverrideDto = components['schemas']['VideoElementOverrideDto'];
export type VideoSettingsDto = components['schemas']['VideoSettingsDto'];
export type WeightsDto = components['schemas']['WeightsDto'];
export type WidgetAssetDto = components['schemas']['WidgetAssetDto'];
export type WidgetElementDto = components['schemas']['WidgetElementDto'];
export type WidgetElementOverrideDto = components['schemas']['WidgetElementOverrideDto'];
export type WidgetFontStyleDto = components['schemas']['WidgetFontStyleDto'];
export type WidgetImageDto = components['schemas']['WidgetImageDto'];
export type WidgetSelectionOptionDto = components['schemas']['WidgetSelectionOptionDto'];
export type $defs = Record<string, never>;
type WithRequired<T, K extends keyof T> = T & {
    [P in K]-?: T[P];
};
export type operations = Record<string, never>;
