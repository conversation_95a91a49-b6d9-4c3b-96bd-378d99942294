export interface IAppEnvironment {
    build: IBuildInfo;
    stage: 'local' | 'test' | 'sandbox' | 'production';
    production: boolean;
    sandbox: boolean;
    auth0: {
        clientId: string;
        domain: string;
    };
    newrelic: {
        enabled: boolean;
        accountId: string;
        trustKey: string;
        agentId: string;
        licenseKey: string;
        applicationId: string;
    };
    appInsights: {
        samplingPercentage: number;
        instrumentationKey: string;
        enabled: boolean;
    };
    featureFlags: {
        enabled: boolean;
        url: string;
        clientKey: string;
    };
    signalR: {
        enabled: boolean;
        url: string;
    };
    nsSignalR: {
        enabled: boolean;
        url: string;
    };
    intercomId: string;
    gtmId?: string;
    origins: {
        acg: string;
        analyticsFrontendUrl: string;
        b2: string;
        bannerflowLibrary: string;
        bannerlingo: string;
        bfc: string;
        accountAccessService: string;
        campaignManager: string;
        campaignService: string;
        commentService: string;
        cps: string;
        designApi: string;
        feedStorage: string;
        fontManager: string;
        fontManagerApi: string;
        fontService: string;
        fontStorage: string;
        genAiImage: string;
        imageOptimizer: string;
        listService: string;
        notificationService: string;
        sapi: string;
        socialCampaignManager: string;
        socialCampaignService: string;
        studioBlobStorage: string;
        videoStorage: string;
        unleash: string;
    };
    commentServiceCreativesetToken: string;
    commentServiceCreativeToken: string;
}

export interface IBuildInfo {
    date: string;
    commit: string;
    version: string;
    branch: string;
}
