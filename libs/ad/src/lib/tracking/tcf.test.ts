import { IBannerflowWindow } from '@domain/ad/bannerflow-window';
import { TCData } from '@domain/ad/tcf';
import { Ad } from '../ad';
import { BANNERFLOW_TCF_VENDOR_ID, hasPermission, initTCF_m } from './tcf';

type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};

/**
 * Helper function that only should be used in tests that returns the full
 * object type from a deep partial, a more type safe alternative to
 * ```typescript
 * const data = mockedData as unknown as Type
 * ```
 * if `mockedData` is a deep partial of `Type`.
 */
function getObjectFromDeepPartial<T>(data: DeepPartial<T>): T {
    return data as T;
}

describe('tcf', () => {
    it('should initiate a tfc api message handler if its not in the top window and no tcfapi exists', () => {
        const mockedWindow = {
            top: {}
        } as IBannerflowWindow;
        const ad: DeepPartial<Ad> = { tcDataSubject: { complete_m: jest.fn() } };
        initTCF_m(ad as Ad, mockedWindow);
        expect(mockedWindow.__tcfapi).toBeDefined();
    });

    it('should not initiate a tfc api message handler if its in the top window and no tcfapi exists', () => {
        // top is readonly on window
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const mockedWindow = {} as any;
        mockedWindow.top = mockedWindow;

        const ad: DeepPartial<Ad> = { tcDataSubject: { complete_m: jest.fn() } };
        initTCF_m(ad as Ad, mockedWindow);
        expect(mockedWindow.__tcfapi).toBeUndefined();
    });

    it('should not overwrite tcfapi if it already exists ', () => {
        const tcfapi = {
            scooby: 'shaggy'
        };
        const mockedWindow = {
            __tcfapi: tcfapi,
            top: {}
        } as IBannerflowWindow & { __tcfapi: typeof tcfapi };

        const ad: DeepPartial<Ad> = { tcDataSubject: { complete_m: jest.fn() } };
        initTCF_m(ad as Ad, mockedWindow);
        expect(mockedWindow.__tcfapi).toEqual(tcfapi);
    });

    describe('hasPermission', () => {
        it('should return false if no tcData is supplied', () => {
            expect(hasPermission(undefined)).toEqual(false);
        });

        it('should return false if gdprApplies key does not exists', () => {
            expect(hasPermission({} as TCData)).toEqual(false);
        });

        it('should return true if gdprApplies value is false', () => {
            expect(hasPermission({ cmpStatus: 'loaded', gdprApplies: false } as TCData)).toEqual(true);
        });

        describe('gdpr applies', () => {
            it('should return false if no consent is found', () => {
                expect(hasPermission({ cmpStatus: 'loaded', gdprApplies: true } as TCData)).toEqual(
                    false
                );
            });

            it('should return false if only purpose consent is truthy', () => {
                expect(
                    hasPermission(
                        getObjectFromDeepPartial<TCData>({
                            cmpStatus: 'loaded',
                            gdprApplies: true,
                            purpose: { consents: { ['1']: true } }
                        })
                    )
                ).toEqual(false);
            });

            it('should return false if only vendor consent is truthy', () => {
                expect(
                    hasPermission(
                        getObjectFromDeepPartial<TCData>({
                            cmpStatus: 'loaded',
                            gdprApplies: true,
                            vendor: { consents: { [`${BANNERFLOW_TCF_VENDOR_ID}`]: true } }
                        })
                    )
                ).toEqual(false);
            });

            it('should return true if vendor and purpose consent is truthy', () => {
                expect(
                    hasPermission(
                        getObjectFromDeepPartial<TCData>({
                            cmpStatus: 'loaded',
                            gdprApplies: true,
                            purpose: { consents: { ['1']: true } },
                            vendor: { consents: { [`${BANNERFLOW_TCF_VENDOR_ID}`]: true } }
                        })
                    )
                ).toEqual(true);
            });

            it('should return false if cmp is not loaded', () => {
                expect(
                    hasPermission(
                        getObjectFromDeepPartial<TCData>({
                            cmpStatus: 'loading',
                            gdprApplies: true,
                            purpose: { consents: { ['1']: true } },
                            vendor: { consents: { [`${BANNERFLOW_TCF_VENDOR_ID}`]: true } }
                        })
                    )
                ).toEqual(false);
            });
        });
    });
});
