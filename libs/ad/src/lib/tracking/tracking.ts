import { diInject } from '@di/di';
import { Token } from '@di/di.token';
import { IAd, IAdClickEvent } from '@domain/ad/ad';
import { IAdData } from '@domain/ad/ad-data';
import { INameAndId } from '@domain/ad/ad-data-creative';
import { AdEvents } from '@domain/ad/ad-events';
import { AdEnvironment, IAdConfig } from '@domain/ad/environment';
import { TCData } from '@domain/ad/tcf';
import {
    ICustomEvent,
    ITrackerClickEvent,
    ITrackerErrorEvent,
    ITrackerPlaythroughEvent,
    ITracking,
    ITrackingData,
    ITrackingDataClickEventValue,
    ITrackingDataCustomEventValue,
    ITrackingDataErrorEventValue,
    ITrackingDataEvent,
    ITrackingDataPlaythroughEventValue,
    TrackingDataEventValue,
    TrackingElements,
    TrackingEventType
} from '@domain/ad/tracking-data';
import { loadPixel, send } from '@studio/utils/ad/http';
import { PageVisibilityState, visibilityChange } from '@studio/utils/ad/visibilitychange';
import { isBannerflowEnvironment } from '@studio/utils/environment';
import { concatUrl, parameterToBool } from '@studio/utils/url';
import { hasPermission } from './tcf';
import { isEPrivacyCountry } from './tracking-country-check';

const CUSTOM_EVENT_LENGTH_LIMIT = 100;
export const AD_ERROR_SAMPLING_RATE = 0.001; // 0.1%

export class Tracking implements ITracking {
    /**
     * Unique session id for tracker to map events from the same session
     */
    private readonly _sessionId: string;
    /**
     * Queue of events to send to server
     */
    private _queue: ITrackingDataEvent[] = [];
    /**
     * Track if impression has been sent to only send it once.
     */
    private _impressionQueued?: boolean;
    /**
     * Track if click has been sent to only send it once.
     */
    private _clickQueued?: boolean;
    /**
     * Track if error has already been sent to only send it once.
     * No point in sending more than the first error.
     */
    private _errorSent = false;
    /**
     * If the viewers country is an ePrivacy country.
     * Only needed in some tracking scenarios.
     */
    private _isEPrivacyCountry = true;
    /**
     * If the Ad fulfilled the IAB view-ability.
     */
    private _wasIAB = false;
    /**
     * Ad Interventions reports which will be reported on unload
     */
    private _reports: ReportList = [];

    private _reportingObserver?: ReportingObserver;

    constructor(
        private _ad: IAd,
        private _config: IAdConfig
    ) {
        this._sessionId = this._generateSessionId();

        if (this._config.environment === AdEnvironment.App) {
            return;
        }

        visibilityChange.onChange(this._trackOnUnload);
        this._ad.on(AdEvents.IABViewEnter, this._onIABEnter);

        this._observeAdIntervention();

        this._ad.tcDataSubject.subscribe(tcData => {
            this._checkCountry(tcData);
        });
    }

    /**
     * Deprecated. Use Redirector.getDeeplinkWhitelist() instead.
     */
    get deeplinkWhitelist(): string[] {
        console.warn(`tracking.deeplinkWhitelist is deprecated`);
        return this._ad.redirect.getDeeplinkWhitelist();
    }

    /**
     * Deprecated. Use Ad.click() instead.
     */
    click(data: IAdClickEvent): void {
        console.warn(`tracking.click is deprecated`);
        return this._ad.click(data);
    }

    /**
     * Send error to tracker
     */
    trackError_m(error: string | Error): void {
        // Only track the first error occurring
        if (!this._errorSent) {
            // Apply error sampling
            const paramSampleRate = this._ad.parameters['error-sample-rate'];
            const sampleRate = paramSampleRate ? +paramSampleRate : AD_ERROR_SAMPLING_RATE;
            if (Math.random() < sampleRate) {
                const msg = typeof error !== 'string' ? error.message : error;
                this._sendError(this._getGetErrorEventValue({ error: msg }, sampleRate));
            }
            this._errorSent = true;
        }
    }

    /**
     * Track click event. Also make sure all external click pixels are fired
     * @param clickData
     */
    trackClick_m(clickData: ITrackerClickEvent): void {
        this._triggerUrlParameterPixel();
        this._enqueue(TrackingEventType.Click, clickData);
        this._send();
    }

    /**
     * Track impression event. Also make sure all external click pixels are fired
     */
    trackImpression_m(): void {
        this._enqueue(TrackingEventType.Impression);
        this._send();
    }

    /**
     * Track custom event
     * @param eventData
     */
    trackCustomEvent(eventData: ICustomEvent): void {
        this._enqueue(TrackingEventType.Custom, eventData);
        this._send();
    }

    destroy(): void {
        this._send();
        this._ad.events.off(window, 'beforeunload', this._send);
        this._ad.off(AdEvents.IABViewEnter, this._onIABEnter);
        visibilityChange.destroy_m();
    }

    /**
     * Generate a unique session id.
     * A session should be unique for each ad lifetime and is used to group events (impression, click,...).
     *
     * Since the previous format caused ID collisions, we try to increase entropy by using crypto if available.
     * Previous format: `${new Date().getTime()}_${Math.floor(Math.random() * 1000000)}`
     */
    private _generateSessionId(): string {
        if (window.crypto && typeof window.crypto.randomUUID === 'function') {
            return crypto.randomUUID();
        }
        // fallback for older browsers
        const randomPart = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);
        return `${new Date().getTime()}_${randomPart}`;
    }

    private _checkCountry(tcData?: TCData): void {
        // avoid the check across the bannerflow platform (creative-preview, cm, etc.)
        if (isBannerflowEnvironment(this._config.environment)) {
            return;
        }
        // HTML5 exports are not allowed to perform 3rd party calls
        if (this._ad.isHtml5Export()) {
            return;
        }
        // Get country information if we have no consent and TCF check is not disabled
        const hasToFetchCountry = !this._ad.isTCFCheckDisabled() && !tcData;
        if (!hasToFetchCountry) {
            return;
        }
        console.debug('[Ad] checking ePrivacy');
        isEPrivacyCountry((isEPC: boolean) => {
            console.debug(`[Ad] isEPrivacyCountry: ${isEPC}`);
            this._isEPrivacyCountry = isEPC;
        });
    }

    /**
     * We need to track if the ad ever was in an IAB viewable state.
     */
    private _onIABEnter = (): void => {
        this._wasIAB = true;
    };

    /**
     * Send queued tracking pixels once per view when visibilityChange becomes
     * hidden. This normally happens on page navigation, tab switch, tab close,
     * browser close and window blur. Uses PageLifecycle.js (local fork)
     * that accommodates for various bugs and inconsistencies between browsers.
     * @param  {PageVisibilityState} event
     * @returns void
     */
    private _trackOnUnload = (event: PageVisibilityState): void => {
        if (event === 'hidden') {
            const ac = diInject(Token.ANIMATED_CREATIVE, { optional: true });
            if (ac) {
                const playthroughData = ac.getTrackerPlaythroughData();
                // throttling the extra events for creatives containing a video or widget
                // until the tracker service can handle more events for every ad
                const shouldTrack = playthroughData.elements.some(e =>
                    (<TrackingElements[]>['v', 'vf', 'w', 'wf']).includes(e)
                );
                if (playthroughData && shouldTrack) {
                    this._enqueue(TrackingEventType.Playthrough, playthroughData);
                }
            }

            this._send();

            this._reportInterventions();
            visibilityChange.removeListener_m(this._trackOnUnload);
        }
    };

    /**
     * Add event to queue.
     * @param type
     * @param eventData
     */
    private _enqueue(
        type: TrackingEventType,
        eventData?: ITrackerClickEvent | ICustomEvent | ITrackerErrorEvent | ITrackerPlaythroughEvent
    ): void {
        let eventValue: TrackingDataEventValue;

        switch (type) {
            case TrackingEventType.Impression:
                if (this._impressionQueued) {
                    return;
                }
                this._impressionQueued = true;
                break;
            case TrackingEventType.Click:
                if (this._clickQueued) {
                    return;
                }
                eventValue = this._getClickEventValue(eventData as ITrackerClickEvent);
                this._clickQueued = true;
                break;
            case TrackingEventType.Custom:
                eventValue = this._getCustomEventValue(eventData as ICustomEvent);
                if (!eventValue) {
                    throw new Error(`Can't track custom event value without name`);
                }
                break;

            case TrackingEventType.Playthrough:
                eventValue = this._getPlaythroughEventValue(eventData as ITrackerPlaythroughEvent);
                break;
        }

        this._queue.push({
            t: type,
            d: new Date().getTime(), // Date.now() is causing issues in older browsers
            v: eventValue
        });
    }

    private _getTrackerUrl(trackingData: ITrackingData): string {
        // For a more detailed explanation of this stuff: STUDIO-8569
        const brandId = this._ad.data.brand?.id || '';

        const tcfConsentGiven = hasPermission(this._ad.tcDataSubject.value);
        const tcfCheckDisabled = trackingData.a.tcd === 1;
        const pixelEnabled = this._ad.data.tracking?.pixelEnabled;
        const cookielessTrackerUrl = `/tr/v2/pixel/`;
        const trackerUrl = `/tr/v2/pixel/${brandId}`;

        if (!pixelEnabled) {
            return cookielessTrackerUrl;
        }

        if (tcfConsentGiven || tcfCheckDisabled) {
            return trackerUrl;
        }

        if (!this._isEPrivacyCountry) {
            return trackerUrl;
        }

        return cookielessTrackerUrl;
    }

    /**
     * Send all queued events to tracker. Using `sendBeacon`, if available.
     */
    private _send = (): void => {
        const trackingMeta = this._ad.data.tracking;

        if (trackingMeta && !trackingMeta.disabled && this._queue?.length) {
            const trackingData = this._getTrackingObject(this._queue);
            const trackingDataString = JSON.stringify(trackingData);
            const trackerUrl = this._getTrackerUrl(trackingData);
            const url = concatUrl(this._ad.getOrigin(), trackerUrl);

            // Don't track stuff at bannerflow domain, errors excluded
            if (this._onBannerflowDomain()) {
                // eslint-disable-next-line
                console.log('Events not sent to tracker on bannerflow domains: \n', trackingDataString);
            } else {
                send(url, trackingDataString);
            }

            this._queue = [];
        }
    };

    private _sendError(event: ITrackingDataErrorEventValue): void {
        if (isBannerflowEnvironment(this._config.environment)) {
            return;
        }
        const adId = this._ad.data.id;
        const creativeId = this._ad.selectedCreative.id;
        const errorUrl = concatUrl(this._ad.getOrigin(), `/tr/error/${adId}/${creativeId}`);
        send(errorUrl, JSON.stringify(event));
    }

    private _sendInterventionReports(reports: ReportList): void {
        if (isBannerflowEnvironment(this._config.environment)) {
            return;
        }
        const adId = this._ad.data.id;
        const creativeId = this._ad.selectedCreative.id;
        const reportUrl = concatUrl(this._ad.getOrigin(), `/tr/blocked/${adId}/${creativeId}`);
        send(reportUrl, JSON.stringify(reports));
    }

    private _onBannerflowDomain(): boolean {
        const domain = this._ad.getDomain();
        return /^(https?:)(\/\/)?(.*\.)?bannerflow\.com\/?/.test(domain);
    }

    /**
     * Fire clickpixel passed as a parameter
     */
    private _triggerUrlParameterPixel(): void {
        const parameters = this._ad.parameters;

        // Custom click tracking pixel
        if (parameters.clickpixel) {
            loadPixel(decodeURIComponent(parameters.clickpixel));
        }
    }

    /**
     * Create object with tracking data required by tracker.
     */
    private _getTrackingObject(events: ITrackingDataEvent[]): ITrackingData {
        // To support error tracking be ultra generous on data availability
        const data = this._ad.data || ({} as IAdData);
        const creative = this._ad.selectedCreative || { size: {}, version: {} };
        const params = this._ad.parameters || {};
        const brand = data.brand || {};
        const account = data.account || {};
        const design = creative.design || ({} as INameAndId);
        const size = creative.size || {};
        const version = creative.version || {};
        const localization = version.localization || {};
        const creativeset = creative.creativeset || {};
        const campaign = data.campaign || { id: data.campaignId };
        const tcf = this._ad.tcDataSubject.value;
        const limitIp = parameterToBool(params.limit_ip) ? 1 : 0;

        return {
            e: events,
            u: {
                r: this._ad.getDomain(), //  Referrer url (complete url without querystrings)
                s: this._sessionId
            },
            a: {
                // Advertiser
                vs: creative.adVersion !== undefined ? `${creative.adVersion}` : 'studio',
                a: account.slug, // Account slug
                br: brand.id, // Brand ID
                c: creativeset.id, // Campaign ID
                ad: data.id, // Ad ID
                b: creative.id, // Creative ID (previous banner)
                s: size.id, //  Size ID
                cs: `${size.width}x${size.height}`, // formatted creative size. e.g.: 300x600 - width x height
                t: version.id, //  Version ID (previous translation)
                l: localization.id, // Localization ID
                bf: design.id, //  Design ID (previous BannerFormat)
                cp: campaign.id, // ID of the DSP campaign
                ch: params.did, // Destination (To which network/adserver the creative set is published)
                tcf: tcf?.tcString,
                tcd: this._ad.isTCFCheckDisabled() ? 1 : 0, // TCF check disabled (1/0)
                lip: limitIp, // No IP processing,
                rl: params.reportinglabel // Reporting label for Analytics
            }
        };
    }

    /**
     * Convert custom event to the event value accepted by tracker
     * @param eventData
     */
    private _getCustomEventValue(eventData: ICustomEvent): ITrackingDataCustomEventValue | undefined {
        if (eventData.name) {
            const click = eventData;
            const id = eventData.id;
            const label = eventData.label;
            const val = parseFloat(`${eventData.value}`);
            const eventValue: ITrackingDataCustomEventValue = {
                n: String(click.name).slice(0, CUSTOM_EVENT_LENGTH_LIMIT)
            };

            if (id !== undefined && id !== null) {
                eventValue.id = String(id).slice(0, CUSTOM_EVENT_LENGTH_LIMIT);
            }
            if (label !== undefined && label !== null) {
                eventValue.l = String(label).slice(0, CUSTOM_EVENT_LENGTH_LIMIT);
            }
            if (!isNaN(val)) {
                eventValue.m = Math.round(val * 100) / 100; // Round to two decimals
            }

            return eventValue;
        }
    }

    /**
     * Get data from click. Currently only x and y
     */
    private _getClickEventValue(eventData: ITrackerClickEvent): ITrackingDataClickEventValue {
        return {
            x: eventData.x,
            y: eventData.y,
            t: eventData.time
        };
    }

    /**
     * Get tracker event value for playthrough event
     */
    private _getPlaythroughEventValue(
        event: ITrackerPlaythroughEvent
    ): ITrackingDataPlaythroughEventValue {
        return {
            el: event.elements,
            b: event.isBannerflowHosted,
            lo: event.loop,
            p: parseFloat(event.progress.toFixed(3)),
            iab: Number(this._wasIAB),
            t: parseFloat(event.playTime.toFixed(3))
        };
    }

    /**
     * Get data from error.
     */
    private _getGetErrorEventValue(
        eventData: ITrackerErrorEvent,
        sampleRateUsed: number
    ): ITrackingDataErrorEventValue {
        // Assuming eventData.error is already a string, as prepared in trackError_m
        return {
            error: eventData.error as string,
            sampleRate: sampleRateUsed
        };
    }

    private _observeAdIntervention(): void {
        if (window.ReportingObserver) {
            // Callback that will handle intervention reports
            this._reportingObserver = new window.ReportingObserver(
                reports => {
                    // report interventions inside creative-preview via errors
                    if (isBannerflowEnvironment(this._config.environment)) {
                        const reportBody = reports[0]?.body;
                        const err = new Error(`AdIntervention - ${reportBody?.['message']}`);
                        this._ad.emit(AdEvents.Error, { event: err, data: reportBody });
                        return;
                    }

                    if (reports.length > 0) {
                        this._reports.push(...reports);
                    }
                },
                {
                    types: ['intervention'],
                    buffered: true
                }
            );

            // Start watching for interventions
            this._reportingObserver.observe();
        }
    }

    private _reportInterventions(): void {
        if (!this._reportingObserver) {
            return;
        }
        // https://developer.chrome.com/blog/heavy-ad-interventions
        const pendingReports = this._reportingObserver.takeRecords();
        const allReports: ReportList = [...this._reports, ...pendingReports];
        if (allReports.length > 0) {
            this._sendInterventionReports(allReports);
        }
    }
}
