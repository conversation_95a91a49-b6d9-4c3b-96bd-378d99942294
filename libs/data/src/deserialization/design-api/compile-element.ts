import {
    ElementOverrideDtoKeys,
    OneOfCustomPropertyDtos,
    OneOfElementDtos,
    OneOfElementOverrideDtos
} from '@domain/api/design-api.interop';

import {
    CreativeDto,
    CreativeSetDto,
    FilterDto,
    GroupNodeDto,
    GroupNodeOverrideDto,
    WidgetElementOverrideDto
} from '@domain/api/generated/design-api';
import { cloneDeep } from '@studio/utils/clone';
import { CompileElementOptions, InferredElementOverride, OverrideOptions } from './design-api.types';
import {
    getElementOverrideOfEntity,
    getOverrideOrValue,
    getWidgetCustomPropertiesOverride,
    isCompleteOverrideProperty,
    isDapiGroupNodeDto,
    isDapiGroupNodeOverrideDto,
    isDapiWidgetElementOverrideDto,
    isDapiWidgetNodeDto
} from './helpers';

export function compileDesignApiElements(
    creativeSetDto: CreativeSetDto,
    creative: CreativeDto
): OneOfElementDtos[] {
    const size = creativeSetDto.sizes.find(({ id }) => id === creative.sizeId)!;

    const sortedElementIds = Object.keys(size.elements).sort((a, b) => {
        const sortIndexA = size.elements[a]?.sortIndex ?? 0;
        const sortIndexB = size.elements[b]?.sortIndex ?? 0;
        return sortIndexA - sortIndexB;
    });

    return sortedElementIds.map(elementId =>
        compileDesignApiElement(creativeSetDto, { elementId, creative, size })
    );
}

function getPoolElement<ElementDto extends OneOfElementDtos>(
    creativeSetDto: CreativeSetDto,
    elementId: string
): ElementDto {
    const poolElement = creativeSetDto.elementsPool.find(({ id }) => id === elementId);

    if (!poolElement) {
        throw new Error(`Element with id ${elementId} does not exist in Creativeset.ElementPool.`);
    }

    return cloneDeep(poolElement as ElementDto);
}

function compileDesignApiElement(
    creativeSetDto: CreativeSetDto,
    { elementId, creative, size }: CompileElementOptions
): OneOfElementDtos {
    const versionId = creative.versionId;
    const version = creativeSetDto.versions.find(({ id }) => id === versionId);

    if (!version) {
        throw new Error();
    }

    const poolElement = getPoolElement(creativeSetDto, elementId);
    const override = getElementOverride(poolElement, { size, creative, version });

    return mergePoolElementWithOverride(poolElement, override);
}

export function getElementOverride<Element extends OneOfElementDtos>(
    poolElement: Element,
    { creative, size, version }: OverrideOptions
): InferredElementOverride<Element> {
    const versionElement = getElementOverrideOfEntity(poolElement, version);
    const sizeElement = getElementOverrideOfEntity(poolElement, size);
    const creativeElement = getElementOverrideOfEntity(poolElement, creative);

    const inferredElementOverride = {
        ...versionElement,
        ...sizeElement,
        ...creativeElement
    } as InferredElementOverride<Element>;

    if (isDapiWidgetNodeDto(poolElement)) {
        const customProperties = getWidgetCustomPropertiesOverride(poolElement, {
            versionElement: versionElement as WidgetElementOverrideDto,
            sizeElement: sizeElement as WidgetElementOverrideDto,
            creativeElement: creativeElement as WidgetElementOverrideDto
        });

        return {
            ...(inferredElementOverride as WidgetElementOverrideDto),
            customProperties
        } satisfies WidgetElementOverrideDto as InferredElementOverride<Element>;
    }

    return inferredElementOverride;
}

export function mergePoolElementWithOverride<ElementDto extends OneOfElementDtos>(
    poolElement: ElementDto,
    override: OneOfElementOverrideDtos
): ElementDto {
    return applyFlatOverride(poolElement, override);
}

/**
 * Merges overrides with values from the poolElement
 *
 * Any type -> Overrides if exists, otherwise uses rootElement value
 *
 * Arrays -> Does complete deep overrides
 *
 * Objects -> Does partial shallow overrides (e.g, merges `{ a: 1, b: 2 }` + `{ b: 3, c: 4 }` into `{ a: 1, b: 3, c: 4 })` unless it's a property which is explicitly allowed to completely override, e.g `imageSettings`
 *
 * *Note that this operation mutates provided poolElement.*
 */
function applyFlatOverride<ElementDto extends OneOfElementDtos>(
    poolElement: ElementDto,
    override: OneOfElementOverrideDtos
): ElementDto {
    if (isDapiGroupNodeDto(poolElement)) {
        // Assume override is GroupNodeOverrideDto
        return flattenGroupOverride(poolElement, override as GroupNodeOverrideDto) as ElementDto;
    }

    // Assert that override is not GroupNodeOverrideDto to narrow types
    if (isDapiGroupNodeOverrideDto(override)) {
        throw new Error('Illegal operation on GroupNodeOverrideDto.');
    }

    for (const field in override) {
        const key = field as ElementOverrideDtoKeys;

        switch (key) {
            case '$type':
                continue;

            case 'customProperties': {
                if (!isDapiWidgetNodeDto(poolElement) || !isDapiWidgetElementOverrideDto(override)) {
                    throw new Error('Illegal operation on WidgetElementDto.');
                }

                poolElement.customProperties = poolElement.customProperties.map(customProperty => {
                    const overrideProperty = override.customProperties?.[customProperty.name];

                    const value = overrideProperty?.value ?? customProperty.value;

                    return {
                        ...customProperty,
                        value
                    } as OneOfCustomPropertyDtos;
                });

                break;
            }

            case 'filters': {
                const keys = new Set([
                    ...Object.keys(poolElement.filters || {}),
                    ...Object.keys(override.filters || {})
                ]) as Set<keyof FilterDto>;

                const filters: FilterDto = {};

                for (const filterKey of keys) {
                    filters[filterKey] = getOverrideOrValue(
                        poolElement.filters[filterKey],
                        override.filters?.[filterKey]
                    );
                }

                poolElement.filters = filters;
                break;
            }

            default: {
                const shouldOverride = isCompleteOverrideProperty(key);
                poolElement[key] = getOverrideOrValue(poolElement[key], override[key], shouldOverride);
                break;
            }
        }
    }

    return poolElement;
}

function flattenGroupOverride(element: GroupNodeDto, override: GroupNodeOverrideDto): GroupNodeDto {
    for (const field in override) {
        const key = field as keyof GroupNodeOverrideDto;

        switch (key) {
            case '$type':
                continue;

            case 'parentNodeId':
                element[key] = getOverrideOrValue(element[key], override[key]);
                break;

            default:
                element[key] = override[key] ?? element[key];
        }
    }

    return element;
}
