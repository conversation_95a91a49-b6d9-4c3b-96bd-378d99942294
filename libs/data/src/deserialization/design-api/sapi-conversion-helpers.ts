import {
    createRichTextFromSegments,
    createRichTextFromString,
    isVersionedText
} from '@creative/elements/rich-text/utils';
import { CreativeDataNode } from '@creative/nodes/base-data-node';
import {
    createVersionedTextFromText,
    isImageNode,
    isTextNode,
    isVideoNode,
    isWidgetNode,
    toFlatNodeList
} from '@creative/nodes/helpers';
import { getTextSegmentType } from '@creative/serialization/character-properties-serializer';
import { deserializeDesignDocument } from '@creative/serialization/document-serializer';
import {
    deserializeGuidelines,
    deserializeSocialGuide
} from '@creative/serialization/guidelines-serializer';
import {
    OneOfCustomPropertyDtos,
    OneOfCustomPropertyOverrideDtos,
    OneOfElementDtos
} from '@domain/api/design-api.interop';
import {
    AnimationDto,
    CreativeSetDto,
    CustomPropertyFeedOverrideDto,
    CustomPropertyTextOverrideDto,
    DesignDto,
    DynamicContentDto,
    FeedDto,
    FilterDto,
    GenericStateDto,
    ImageElementDto,
    LegacyFeededReference,
    SettingDto,
    SizeDto,
    TextLikeElementDto,
    TextLikeElementOverrideDto,
    TextStateDto,
    VersionDto,
    VideoElementDto,
    WidgetElementDto,
    WidgetElementOverrideDto
} from '@domain/api/generated/design-api';
import {
    CharacterStyleDto,
    AnimationDto as SapiAnimationDto,
    CreativeDto as SapiCreativeDto,
    DocumentDto as SapiDocumentDto,
    FeedDto as SapiFeedDto,
    FilterDto as SapiFilterDto,
    ImageAssetDataDto as SapiImageAssetDataDto,
    SettingDto as SapiSettingDto,
    StateDto as SapiStateDto,
    TextStateDto as SapiTextStateDto,
    VideoAssetDataDto as SapiVideoAssetDataDto,
    WidgetElementDto as SapiWidgetElementDto
} from '@domain/api/generated/sapi';
import { ImageLibraryAsset } from '@domain/brand/brand-library/image-asset';
import { VideoLibraryAsset } from '@domain/brand/brand-library/video-asset';
import { IWidgetLibraryAsset } from '@domain/brand/brand-library/widget-asset';
import { CreativeSize, IDesign, IElement, IElementProperty } from '@domain/creativeset';
import { ApprovalStatus, ICreative } from '@domain/creativeset/creative/creative';
import { AssetReference } from '@domain/creativeset/element-asset';
import {
    ITextSpan,
    IVersion,
    IVersionedText,
    IVersionProperty,
    IWidgetText
} from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import { IFeed } from '@domain/feed';
import { ICreativeDataNode, OneOfDataNodes } from '@domain/nodes';
import { OneOfNodesDto } from '@domain/serialization';
import { ITextVariable, SpanType } from '@domain/text';
import { IWidgetCustomProperty, WidgetUnits } from '@domain/widget';
import { distinctArrayById } from '@studio/utils/array';
import { createElement, createElementProperty } from '@studio/utils/element.utils';
import { uuidv4 } from '@studio/utils/id';
import { isNumber, omit } from '@studio/utils/utils';
import { compileDesignApiElements, getElementOverride } from './compile-element';
import {
    getKindFromDesignApiElement,
    isCustomFeedPropertyDto,
    isCustomTextPropertyDto,
    isDapiEllipseNodeDto,
    isDapiGroupNodeDto,
    isDapiImageNodeDto,
    isDapiRectangleNodeDto,
    isDapiTextLikeNodeDto,
    isDapiVideoNodeDto,
    isDapiWidgetNodeDto,
    isDapiWidgetWithAssetNodeDto,
    isVersionableCustomPropertyDto
} from './helpers';

export function convertPoolElementToElement(element: OneOfElementDtos): IElement {
    return createElement({
        id: element.id,
        name: element.name,
        type: getKindFromDesignApiElementForCreativesetElement(element),
        properties: getPropertiesFromDapiElementDto(element)
    });
}

export function getConvertedCreativesAndAssets(
    creativeSetDto: CreativeSetDto,
    versions: IVersion[],
    elements: IElement[],
    designs: IDesign[],
    creativeSizes: CreativeSize[]
): {
    creatives: ICreative[];
    imageAssets: ImageLibraryAsset[];
    videoAssets: VideoLibraryAsset[];
    widgetAssets: IWidgetLibraryAsset[];
} {
    const imageAssets: ImageLibraryAsset[] = getImageLibraryAssetFromElementDtos(
        creativeSetDto.elementsPool
    );
    const videoAssets: VideoLibraryAsset[] = getVideoLibraryAssetFromElementDtos(
        creativeSetDto.elementsPool
    );

    // sometimes we can have widget element in design, but widget asset is missing in widgets array.
    // In this case we have decided to skip this items as it was originally (647956, 648039, 648041)
    const widgetAssets = creativeSetDto.elementsPool
        .filter(isDapiWidgetWithAssetNodeDto)
        .map(element => element.widgetAsset);

    const creatives = creativeSetDto.creatives.map((creative): ICreative => {
        const creativeSize = creativeSizes.find(({ id }) => id === `${creative.sizeId}`)!;
        const size = creativeSetDto.sizes.find(({ id }) => id === creative.sizeId)!;
        const version = versions.find(({ id }) => id === `${creative.versionId}`)!;
        const dapiElements = compileDesignApiElements(creativeSetDto, creative);
        const designDto = size.design;

        imageAssets.push(...getImageLibraryAssetFromElementDtos(dapiElements));
        videoAssets.push(...getVideoLibraryAssetFromElementDtos(dapiElements));

        for (const dapiElement of dapiElements) {
            if (isDapiWidgetNodeDto(dapiElement)) {
                const rootWidgetElement = elements.find(({ id }) => id === dapiElement.id);
                for (const customProperty of dapiElement.customProperties) {
                    if (isVersionableCustomPropertyDto(customProperty) && customProperty.l_VpId) {
                        const rootElementProperty = rootWidgetElement?.properties.find(
                            p => p.name === customProperty.name
                        );
                        if (rootElementProperty) {
                            rootElementProperty.versionPropertyId = customProperty.l_VpId;
                        }
                    }
                }
            }
        }

        const SAPIDesignDocumentDto = designDto
            ? createSapiDocument(dapiElements, designDto, size, version)
            : undefined;

        const creativeDataNode = SAPIDesignDocumentDto
            ? deserializeDesignDocument(SAPIDesignDocumentDto)
            : undefined;

        let design: IDesign | undefined;

        if (creativeDataNode && designDto?.name) {
            design = designDto.l_DesId
                ? designs.find(({ id }) => id === designDto.l_DesId)
                : designs.find(({ name }) => name === designDto.name);

            if (design) {
                mergeDesignElementCharacterStyles(creativeDataNode, design);
            } else {
                populateGlobalElementsToDataNodes(creativeDataNode, elements);

                design = {
                    id: designDto.l_DesId ?? '0',
                    elements: elements.filter(element => size.elements[element.id]),
                    name: designDto.name,
                    document: creativeDataNode
                };
                designs.push(design);
            }
        }

        return {
            id: `${creative.id}`,
            checksum: creative.checksum,
            approvalStatus: creative.approvalStatus as ApprovalStatus,
            size: creativeSize,
            design,
            version,
            targetUrl: creative.targetUrl
        };
    });

    return {
        creatives,
        imageAssets: distinctArrayById(imageAssets),
        videoAssets: distinctArrayById(videoAssets),
        widgetAssets: distinctArrayById(widgetAssets)
    };
}

export function convertVersion(
    version: VersionDto,
    elements: IElement[],
    creativesetDto: CreativeSetDto
): IVersion {
    const { id: versionId, name, targetUrl } = version;
    const migratedVersion: IVersion = {
        id: `${versionId}`,
        localization: {
            id: version.localizationId
        },
        name,
        targetUrl: targetUrl ?? '',
        properties: []
    };

    const isDefaultVersion = version.id === creativesetDto.defaultVersionId;

    for (const poolElement of creativesetDto.elementsPool) {
        if (!isDefaultVersion) {
            const versionElement = version.elements?.[poolElement.id];
            if (!versionElement) {
                continue;
            }
        }

        const element = elements.find(({ id }) => id === poolElement.id);

        if (!element) {
            continue;
        }

        if (isDapiTextLikeNodeDto(poolElement)) {
            const versionOverride = getElementOverride(poolElement, { version });
            const versionProperty = getTextLikeVersionProperty(versionOverride, element);
            // Styles are mapped in populateCharacterStylesByTextSegment
            versionProperty.value.styles = [];
            migratedVersion.properties.push(versionProperty);
            const existingElementProperty = element.properties.find(
                p => p.versionPropertyId === versionProperty.id
            );
            if (!existingElementProperty) {
                element.properties.push(
                    createElementProperty({
                        id: poolElement.l_TEPId,
                        name: 'content',
                        versionPropertyId: versionProperty.id,
                        value: versionProperty.id ? undefined : '' // Comparison tool expects empty string due to that being persisted for SAPI, but we don't need to set value if version exists
                    })
                );
            }
        } else if (isDapiWidgetNodeDto(poolElement)) {
            const versionElement = getElementOverride(poolElement, { version });

            if (!versionElement.customProperties) {
                continue;
            }

            const widgetVersionProperties = getWidgetVersionProperties(poolElement, versionElement);
            widgetVersionProperties.forEach(vp => migratedVersion.properties.push(vp));
        }
    }

    return migratedVersion;
}

export function getPropertiesFromDapiElementDto(element: OneOfElementDtos): IElementProperty[] {
    if (isDapiWidgetNodeDto(element)) {
        return getWidgetElementProperties(element);
    }

    if (isDapiVideoNodeDto(element)) {
        return getVideoElementProperties(element);
    }

    if (isDapiImageNodeDto(element)) {
        return getImageElementProperties(element);
    }

    if (isDapiTextLikeNodeDto(element)) {
        return getTextElementDynamicProperties(element);
    }

    return [];
}

export function getWidgetElementProperties(element: WidgetElementDto): IElementProperty[] {
    const properties: IElementProperty[] = [];

    const blobReferenceLegacyProperty = element.l_WProps?.find(
        ({ name }) => name === AssetReference.WidgetContentUrl
    );
    if (blobReferenceLegacyProperty) {
        properties.push(
            createElementProperty({
                clientId: uuidv4(),
                id: blobReferenceLegacyProperty.id,
                unit: 'string',
                name: AssetReference.WidgetContentUrl,
                value: element.widgetContentBlobReference
            })
        );
    }

    const widgetReferenceLegacyProperty = element.l_WProps?.find(
        ({ name }) => name === AssetReference.Widget
    );
    if (widgetReferenceLegacyProperty) {
        const value = element.widgetAsset?.id;
        properties.push(
            createElementProperty({
                clientId: uuidv4(),
                id: widgetReferenceLegacyProperty.id,
                unit: 'string',
                name: AssetReference.Widget,
                value: value
            })
        );
    }

    for (const customProperty of element.customProperties) {
        const customLegacyProperty = element.l_WProps?.find(({ name }) => name === customProperty.name);
        if (customLegacyProperty) {
            // element custom properties label could vary from design document custom properties and element properties are prioritized
            const versionPropertyId = isVersionableCustomPropertyDto(customProperty)
                ? customProperty.l_VpId
                : undefined;
            properties.push(
                createElementProperty({
                    ...customProperty,
                    clientId: uuidv4(),
                    id: customLegacyProperty.id,
                    unit: getUnitFromDesignApiCustomProperty(customProperty),
                    versionPropertyId,
                    value: versionPropertyId ? undefined : customProperty.value,
                    label: customLegacyProperty.label,
                    hasDynamicContent: customLegacyProperty.hasDynamicContent
                })
            );
        }
    }

    return properties;
}

export function getVideoElementProperties(element: VideoElementDto): IElementProperty[] {
    if (element.l_VEPId) {
        return [
            createElementProperty({
                clientId: uuidv4(),
                id: element.l_VEPId,
                unit: 'id',
                name: AssetReference.Video,
                value: element.videoAsset?.id.toString()
            })
        ];
    } else if (element.feed?.legacyFeededReference) {
        return [getLegacyFeedElementProperty(element.feed?.legacyFeededReference)];
    }

    return [];
}

function getLegacyFeedElementProperty(legacyFeededReference: LegacyFeededReference): IElementProperty {
    return createElementProperty({
        clientId: legacyFeededReference.clientId,
        id: legacyFeededReference.id,
        unit: legacyFeededReference.unit,
        name: legacyFeededReference.name,
        value: legacyFeededReference.value
    });
}

function getDynamicElementProperty(dynamicContent: DynamicContentDto): IElementProperty {
    return createElementProperty({
        clientId: uuidv4(),
        id: dynamicContent.id,
        unit: 'object',
        name: 'dynamicContent',
        label: dynamicContent.label,
        value: dynamicContent.value
    });
}

export function getTextElementDynamicProperties(element: TextLikeElementDto): IElementProperty[] {
    if (element.dynamicContent) {
        return [getDynamicElementProperty(element.dynamicContent)];
    }

    return [];
}

export function getImageElementProperties(element: ImageElementDto): IElementProperty[] {
    let elementProperties: IElementProperty[] = [];

    if (element.dynamicContent) {
        elementProperties = [getDynamicElementProperty(element.dynamicContent)];
    }

    if (element.l_IEPropId) {
        return [
            ...elementProperties,
            createElementProperty({
                clientId: uuidv4(),
                id: element.l_IEPropId,
                unit: 'id',
                name: AssetReference.Image,
                value: element.imageAsset?.id.toString()
            })
        ];
    } else if (element.feed?.legacyFeededReference) {
        return [
            ...elementProperties,
            getLegacyFeedElementProperty(element.feed?.legacyFeededReference)
        ];
    }

    return elementProperties;
}

export function getTextLikeVersionProperty(
    textLikeElement: TextLikeElementDto | TextLikeElementOverrideDto,
    element: IElement
): IVersionProperty<IVersionedText> {
    const existingProperty = element?.properties.find(({ name }) => name === 'content');

    const versionPropertyId = existingProperty?.versionPropertyId ?? textLikeElement.l_VpId;

    if (!versionPropertyId) {
        throw new Error('VersionPropertyId not found');
    }

    const versionProperty: IVersionProperty<IVersionedText> = {
        id: versionPropertyId,
        name: 'content',
        value: createVersionedTextFromText(
            createRichTextFromSegments(textLikeElement.textSegments ?? [])
        )
    };

    return versionProperty;
}

export function getWidgetVersionProperties(
    widgetElement: WidgetElementDto,
    override?: WidgetElementOverrideDto
): IVersionProperty[] {
    const versionProperties: IVersionProperty[] = [];
    const customProperties = widgetElement.customProperties || [];

    for (const customProperty of customProperties) {
        if (isCustomTextPropertyDto(customProperty)) {
            const propertyOverride = override?.customProperties?.[customProperty.name] as
                | CustomPropertyTextOverrideDto
                | undefined;
            const value = propertyOverride?.value ?? customProperty.value;
            const versionPropertyId = customProperty.l_VpId ?? propertyOverride?.l_VpId;

            if (!versionPropertyId) {
                console.warn('Missing versionPropertyId on widgetText CustomProperty.');
                continue;
            }

            let propertyName: string;
            let textValue: IWidgetText;
            const versionPropertyName = customProperty.l_VpName ?? propertyOverride?.l_VpName;
            if (versionPropertyName === 'widgetText') {
                propertyName = 'widgetText';
                textValue = {
                    text: value ?? ''
                };
                // looks like we still have corrupted data for widgetText, COBE is aware of it, and they will fix it soon
                // for now let's have this dirty fix, we styles exists then property type will be content
                if (textValue['styles'] !== undefined) {
                    delete textValue['styles'];
                }
            } else {
                propertyName = 'content';
                const versionedText = createVersionedTextFromText(
                    createRichTextFromString(value ?? '')
                );
                // we have two variants if styles in SAPI when there are no stylesIds set
                // first one is empty array, second one is styles array splitted by tokens with empty styleIds
                // to have consistent data and save some memory we will remove styles if there are no styleIds
                if (versionedText.styles.length) {
                    const hasStyleIds = versionedText.styles.some(
                        style => Object.keys(style.styleIds).length
                    );
                    if (!hasStyleIds) {
                        versionedText.styles = [];
                    }
                }
                textValue = versionedText;
            }

            const versionProperty: IVersionProperty = {
                id: versionPropertyId,
                name: propertyName,
                value: textValue
            };

            versionProperties.push(versionProperty);
        } else if (isCustomFeedPropertyDto(customProperty)) {
            const propertyOverride = override?.customProperties?.[customProperty.name] as
                | CustomPropertyFeedOverrideDto
                | undefined;
            const versionPropertyId = customProperty.l_VpId ?? propertyOverride?.l_VpId;

            if (!versionPropertyId) {
                console.warn('Missing versionPropertyId on feed CustomProperty.');
                continue;
            }

            if (!customProperty.value) {
                continue;
            }

            const value = propertyOverride?.value ?? customProperty.value;

            const feedValue: IFeed = {
                id: value.id,
                path: value.path,
                step: value.step,
                fallback: value.fallback,
                type: value.type
            };

            const versionProperty: IVersionProperty = {
                id: versionPropertyId,
                name: 'feed',
                value: feedValue
            };

            versionProperties.push(versionProperty);
        }
    }
    return versionProperties;
}

export function populateCharacterStylesByTextSegment(
    textElement: TextLikeElementDto,
    version: IVersion,
    documentId: string
): CharacterStyleDto[] {
    const segments = textElement.textSegments;
    const versionProperty = version.properties.find(({ id }) => id === textElement.l_VpId);

    const characterStyles: CharacterStyleDto[] = [];

    if (!versionProperty || !isVersionedText(versionProperty)) {
        return [];
    }

    let spanPosition = 0;

    for (const segment of segments) {
        const versionedStyles = versionProperty.value.styles;

        const newSpan: ITextSpan = {
            length: segment.content.length,
            position: spanPosition,
            styleIds: segment.l_StyleId ? { [documentId]: segment.l_StyleId } : {},
            type: segment.variable ? SpanType.Variable : getTextSegmentType(segment),
            variable: segment.variable as ITextVariable
        };

        const existingSegment = versionedStyles.find(
            ({ position, length }) => position === newSpan.position && length === newSpan.length
        );

        const styleId = segment.l_StyleId;

        if (existingSegment) {
            if (styleId) {
                existingSegment.styleIds[documentId] = styleId;
            }
        } else {
            versionedStyles.splice(spanPosition, 0, newSpan);
        }

        if (styleId) {
            const characterStyle: CharacterStyleDto = { id: styleId, value: segment.styles ?? {} };
            characterStyles.push(characterStyle);
        }

        spanPosition += segment.content.length;
    }

    return characterStyles;
}

export function createSapiDocument(
    elementDtos: OneOfElementDtos[],
    designDto: DesignDto,
    size: SizeDto,
    version: IVersion
): SapiDocumentDto {
    const imageAssets = getImageAssetsDataFromElementDtos(elementDtos);
    const videoAssets = getVideoAssetsDataFromElementDtos(elementDtos);

    const elements = elementDtos.map(element => convertToStudioElement(element, size, version));

    const creativeDto: SapiCreativeDto = {
        id: `${size.design?.l_DocId}`,
        width: size.width,
        height: size.height,
        fill: designDto.fill,
        startTime: designDto.startTime,
        stopTime: designDto.stopTime,
        gifExport: designDto.gifExport,
        guidelines: deserializeGuidelines(designDto.guidelines),
        socialGuide: deserializeSocialGuide(designDto.socialGuide),
        loops: designDto.loops,
        audio: designDto.audio,
        preloadImage: designDto.preloadImage,
        elements
    };

    return {
        head: {
            asset: {
                images: imageAssets,
                videos: videoAssets
            },
            elements: elementDtos.map(element => ({
                id: element.id,
                type: getKindFromDesignApiElement(element)
            }))
        },
        body: {
            creative: creativeDto
        }
    };
}

export function getVideoAssetsDataFromElementDtos(
    elementDtos: OneOfElementDtos[]
): SapiVideoAssetDataDto[] {
    const videoAssets: SapiVideoAssetDataDto[] = [];

    for (const element of elementDtos) {
        if (isDapiVideoNodeDto(element) && element.videoAsset) {
            const videoAsset = element.videoAsset;
            videoAssets.push({
                id: videoAsset.id.toString(),
                name: videoAsset.name,
                fileSize: videoAsset.fileSize,
                height: videoAsset.height,
                url: videoAsset.url,
                width: videoAsset.width
            });
        }
    }

    return videoAssets;
}

export function getImageAssetsDataFromElementDtos(
    elementDtos: OneOfElementDtos[]
): SapiImageAssetDataDto[] {
    const imageAssets: SapiImageAssetDataDto[] = [];

    for (const element of elementDtos) {
        if (isDapiImageNodeDto(element) && element.imageAsset) {
            const imageAsset = element.imageAsset;
            imageAssets.push({
                id: imageAsset.id.toString(),
                height: imageAsset.original.height,
                src: imageAsset.original.url,
                width: imageAsset.original.width,
                name: imageAsset.name
            });
        }
    }

    return imageAssets;
}

export function getImageLibraryAssetFromElementDtos(
    elementDtos: OneOfElementDtos[]
): ImageLibraryAsset[] {
    const imageAssets: ImageLibraryAsset[] = [];

    for (const element of elementDtos) {
        if (isDapiImageNodeDto(element) && element.imageAsset) {
            const imageAsset = element.imageAsset;
            imageAssets.push({
                ...imageAsset,
                id: `${imageAsset.id}`,
                height: imageAsset.original.height,
                url: imageAsset.original.url,
                width: imageAsset.original.width
            });
        }
    }

    return imageAssets;
}

export function getVideoLibraryAssetFromElementDtos(
    elementDtos: OneOfElementDtos[]
): VideoLibraryAsset[] {
    const videoAssets: VideoLibraryAsset[] = [];

    for (const element of elementDtos) {
        if (isDapiVideoNodeDto(element) && element.videoAsset) {
            const videoAsset = element.videoAsset;
            videoAssets.push({
                ...videoAsset,
                id: `${videoAsset.id}`
            });
        }
    }

    return videoAssets;
}

export function getKindFromDesignApiElementForCreativesetElement(
    element: OneOfElementDtos
): ElementKind {
    if (isDapiWidgetNodeDto(element)) {
        return element.type as ElementKind;
    }

    return getKindFromDesignApiElement(element);
}

export function getUnitFromDesignApiCustomProperty(
    customProperty: WidgetElementDto['customProperties'][number]
): WidgetUnits {
    switch (customProperty.$type) {
        case 'CustomPropertyBooleanDto':
            return 'boolean';
        case 'CustomPropertyColorDto':
            return 'color';
        case 'CustomPropertyFeedDto':
            return 'feed';
        case 'CustomPropertyFontDto':
            return 'font';
        case 'CustomPropertyImageDto':
            return 'image';
        case 'CustomPropertyNumberDto':
            return 'number';
        case 'CustomPropertySelectDto':
            return 'select';
        case 'CustomPropertyTextDto':
            return 'text';
        default:
            throw new Error(`Unknown custom property type: ${customProperty}`);
    }
}

export function getOverrideTypeFromCustomProperty(
    customProperty: IWidgetCustomProperty
): OneOfCustomPropertyOverrideDtos['$type'] {
    switch (customProperty.unit) {
        case 'boolean':
            return 'CustomPropertyBooleanOverrideDto';
        case 'color':
            return 'CustomPropertyColorOverrideDto';
        case 'feed':
            return 'CustomPropertyFeedOverrideDto';
        case 'font':
            return 'CustomPropertyFontOverrideDto';
        case 'image':
            return 'CustomPropertyImageOverrideDto';
        case 'number':
            return 'CustomPropertyNumberOverrideDto';
        case 'select':
            return 'CustomPropertySelectOverrideDto';
        case 'text':
            return 'CustomPropertyTextOverrideDto';
        default:
            throw new Error(`Unknown custom property type: ${customProperty}`);
    }
}

export function convertToStudioElement(
    elementDto: OneOfElementDtos,
    size: SizeDto,
    version: IVersion
): OneOfNodesDto {
    if (isDapiGroupNodeDto(elementDto)) {
        return { ...omit(elementDto, '$type'), __groupKind: true };
    }

    const filters = mapDapiFilterToSapiFilter(elementDto.filters);
    const animations = mapDapiAnimationsSapiState(elementDto.animations);

    if (isDapiTextLikeNodeDto(elementDto)) {
        const characterStyles = populateCharacterStylesByTextSegment(
            elementDto,
            version,
            `${size.design?.l_DocId}`
        );

        const isButton = elementDto.isButton;
        const states = mapDapiTextStateToSapiTextState(elementDto.states);

        if (isButton) {
            return {
                ...omit(elementDto, '$type', 'dynamicContent', 'textSegments'),
                __buttonKind: true,
                characterStyles,
                filters,
                states,
                animations
            };
        }

        return {
            ...omit(elementDto, '$type'),
            __textKind: true,
            characterStyles,
            filters,
            states,
            animations
        };
    }

    const states = mapDapiGenericStateToSapiState(elementDto.states);

    if (isDapiImageNodeDto(elementDto)) {
        const feed = mapDapiFeedToSapiFeed(elementDto.feed);

        return {
            ...omit(elementDto, '$type'),
            imageAssetId: elementDto.imageAsset?.id.toString(),
            __imageKind: true,
            feed,
            filters,
            states,
            animations
        };
    }

    if (isDapiVideoNodeDto(elementDto)) {
        const feed = mapDapiFeedToSapiFeed(elementDto.feed);

        return {
            ...omit(elementDto, '$type'),
            videoAssetId: elementDto.videoAsset?.id.toString(),
            __videoKind: true,
            feed,
            states,
            filters,
            animations
        };
    }

    if (isDapiWidgetNodeDto(elementDto)) {
        const customProperties = elementDto.customProperties.map(
            mapDapiWidgetCustomPropertyToSapiCustomProperty
        );

        return {
            ...omit(elementDto, '$type'),
            customProperties,
            __widgetKind: true,
            filters,
            states,
            animations
        };
    }

    if (isDapiRectangleNodeDto(elementDto)) {
        return { ...omit(elementDto, '$type'), __rectangleKind: true, filters, states, animations };
    }

    if (isDapiEllipseNodeDto(elementDto)) {
        return { ...omit(elementDto, '$type'), __ellipseKind: true, states, filters, animations };
    }

    throw new Error('Unknown element type');
}

function mapDapiFeedToSapiFeed(feedDto?: FeedDto): SapiFeedDto | undefined {
    if (!feedDto) {
        return undefined;
    }

    return {
        id: feedDto.id,
        fallback: feedDto.fallback,
        path: feedDto.path,
        step: feedDto.step,
        type: feedDto.type
    };
}

function mapDapiWidgetCustomPropertyToSapiCustomProperty(
    customProperty: OneOfCustomPropertyDtos
): SapiWidgetElementDto['customProperties'][number] {
    const versionPropertyId =
        isCustomTextPropertyDto(customProperty) || isCustomFeedPropertyDto(customProperty)
            ? customProperty.l_VpId
            : undefined;

    return {
        name: customProperty.name,
        label: customProperty.label,
        unit: getUnitFromDesignApiCustomProperty(customProperty),
        value: customProperty.value,
        versionPropertyId: versionPropertyId
    } as SapiWidgetElementDto['customProperties'][number];
}

function mapDapiGenericStateToSapiState(dapiGenericStates: GenericStateDto[]): SapiStateDto[] {
    return dapiGenericStates.map(state => ({
        ...state,
        filters: state.filters ? mapDapiFilterToSapiFilter(state.filters) : undefined
    }));
}

function mapDapiAnimationsSapiState(animations: AnimationDto[]): SapiAnimationDto[] {
    return animations.map(animation => {
        const settings = animation.settings;
        const settingsDto: SapiSettingDto[] = [];
        for (const key in settings) {
            const setting = settings[key as keyof SettingDto]!;
            const name = setting.name.slice(0, 1).toUpperCase() + setting.name.slice(1);
            settingsDto.push({
                key,
                name: name,
                value: setting.value
            });
        }

        return { ...animation, settings: animation.settings ? settingsDto : undefined };
    });
}

function mapDapiTextStateToSapiTextState(dapiGenericStates: TextStateDto[]): SapiTextStateDto[] {
    return dapiGenericStates.map(state => ({
        ...state,
        filters: state.filters ? mapDapiFilterToSapiFilter(state.filters) : undefined
    }));
}

function mapDapiFilterToSapiFilter(dapiFilterDto: FilterDto): SapiFilterDto {
    const filters: SapiFilterDto = {};

    if (isNumber(dapiFilterDto.blur)) {
        filters.blur = { value: dapiFilterDto.blur };
    }
    if (isNumber(dapiFilterDto.contrast)) {
        filters.contrast = { value: dapiFilterDto.contrast };
    }
    if (isNumber(dapiFilterDto.grayscale)) {
        filters.grayscale = { value: dapiFilterDto.grayscale };
    }
    if (isNumber(dapiFilterDto.invert)) {
        filters.invert = { value: dapiFilterDto.invert };
    }
    if (isNumber(dapiFilterDto.saturate)) {
        filters.saturate = { value: dapiFilterDto.saturate };
    }
    if (isNumber(dapiFilterDto.sepia)) {
        filters.sepia = { value: dapiFilterDto.sepia };
    }

    return filters;
}

function mergeDesignElementCharacterStyles(
    creativeDataNode: CreativeDataNode,
    existingDesign: IDesign
): void {
    for (const creativeElement of creativeDataNode.elements) {
        if (!isTextNode(creativeElement)) {
            continue;
        }
        const existingElement = existingDesign.document.findNodeById_m(creativeElement.id);
        if (isTextNode(existingElement)) {
            existingElement.characterStyles = new Map([
                ...existingElement.characterStyles,
                ...creativeElement.characterStyles
            ]);
        }
    }
}

export function getGlobalElementsFromCreativeDataNode(creativeDataNode: ICreativeDataNode): IElement[] {
    return toFlatNodeList(creativeDataNode).map(({ globalElement }) => globalElement);
}

/**
 * When it's needed to patch global elements that don't exist, we create a new global element.
 * @param elementNode - The element node to create a global element from.
 */
export function createGlobalElement(elementNode: OneOfDataNodes): IElement | undefined {
    if (isWidgetNode(elementNode)) {
        // AD-2457: Don't attempt to migrate widget nodes
        return;
    }

    const properties: IElementProperty[] = [];
    if (isImageNode(elementNode)) {
        properties.push({
            id: '',
            name: AssetReference.Image,
            unit: 'id',
            value: elementNode.imageAsset?.id,
            clientId: uuidv4()
        });
    }

    if (isVideoNode(elementNode)) {
        properties.push({
            id: '',
            name: AssetReference.Video,
            unit: 'id',
            value: elementNode.videoAsset?.id,
            clientId: uuidv4()
        });
    }

    return {
        id: elementNode.id,
        name: elementNode.name ?? elementNode.kind,
        type: elementNode.kind,
        properties
    };
}

export function populateGlobalElementsToDataNodes(
    creativeDataNode: ICreativeDataNode,
    elements: IElement[]
): void {
    for (const elementNode of creativeDataNode.nodeIterator_m(true)) {
        const element = elements.find(({ id }) => id === elementNode.id);
        if (!element) {
            console.error(`Could not find global element with id '${elementNode.id}'.`);
            const globalElement = createGlobalElement(elementNode);
            if (globalElement) {
                elementNode.globalElement = globalElement;
            } else {
                throw new Error(`Could not find global element with id '${elementNode.id}'.`);
            }
            continue;
        }

        elementNode.name = element.name;
        elementNode.globalElement = element;
    }
}
