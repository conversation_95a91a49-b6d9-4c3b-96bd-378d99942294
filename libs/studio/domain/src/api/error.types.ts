import type { HttpErrorResponse } from '@angular/common/http';
import {
    OneOfCreativeSetProblemDetails,
    OneOfShowcaseProblemDetails
} from '@domain/api/problem-details';
import type { Observable } from 'rxjs';

export const enum ErrorStatus {
    BadRequest = 400,
    Forbidden = 403,
    NotFound = 404,
    Conflict = 409
}

export type BadRequestResponse = HttpErrorResponse & { status: ErrorStatus.BadRequest };
export type NotFoundResponse = HttpErrorResponse & { status: ErrorStatus.NotFound };
export type ForbiddenResponse = HttpErrorResponse & { status: ErrorStatus.Forbidden };
export type ConflictResponse = HttpErrorResponse & { status: ErrorStatus.Conflict };
export type KnownErrorResponse =
    | BadRequestResponse
    | ForbiddenResponse
    | NotFoundResponse
    | ConflictResponse;

export type HttpRequestObservable<T extends object | undefined | void> = Observable<
    T | KnownErrorResponse
>;

export type HttpErrorWithProblemDetails = Omit<HttpErrorResponse, 'error'> & {
    error: OneOfShowcaseProblemDetails | OneOfCreativeSetProblemDetails;
};
