import { BreakpointObserver } from '@angular/cdk/layout';
import { inject, Injectable } from '@angular/core';
import { Logger } from '@bannerflow/sentinel-logger';
import { AdEnvironment } from '@domain/ad/environment';
import { AppView, ICreativeConfig } from '@domain/creative/environment';
import { Breakpoint, getQueryFromBreakpoint } from '@studio/utils/breakpoints';
import { BehaviorSubject, combineLatest, map, Observable, of, tap } from 'rxjs';
import { APP_ENV } from '../environment/environment.provider';

@Injectable({ providedIn: 'root' })
export class EnvironmentService {
    private breakpointObserver = inject(BreakpointObserver);
    readonly appEnvironment = inject(APP_ENV);
    readonly origins = this.appEnvironment.origins;
    inPreviewMode = false;
    creativeConfig: ICreativeConfig;
    activePage: string;

    inShowcaseMode$: Observable<boolean>;
    inShowcaseMode: boolean;

    isMobile: boolean;
    isMobile$: Observable<boolean>;

    isMobileShowcase: boolean;
    isMobileShowcase$: Observable<boolean>;

    isMac = navigator.userAgent.includes('Mac');

    private _activePage$ = new BehaviorSubject<AppView | undefined>(undefined);
    activePage$ = this._activePage$.asObservable();
    private logger = new Logger('EnvironmentService');

    constructor() {
        this.inShowcaseMode = this.isShowcaseUrl(window.location.href);
        this.inShowcaseMode$ = of(this.inShowcaseMode);

        this.isMobile = window.matchMedia(getQueryFromBreakpoint(Breakpoint.DesktopDown)).matches;
        this.isMobile$ = this.breakpointObserver
            .observe(getQueryFromBreakpoint(Breakpoint.DesktopDown))
            .pipe(
                map(matchBreakpoint => matchBreakpoint.matches),
                tap(isMobile => (this.isMobile = isMobile))
            );

        this.isMobileShowcase = this.isMobile && this.inShowcaseMode;
        this.isMobileShowcase$ = combineLatest([this.inShowcaseMode$, this.isMobile$]).pipe(
            map(([showcaseMode, isMobile]) => showcaseMode && isMobile)
        );
        this.logger.verbose({
            inShowcaseMode: this.inShowcaseMode,
            isMobile: this.isMobile,
            isMobileShowcase: this.isMobileShowcase
        });

        this.setEnv();
    }

    setEnv(): void {
        const { fontService, imageOptimizer, feedStorage, fontStorage, videoStorage } =
            this.appEnvironment.origins;

        this.creativeConfig = {
            environment: AdEnvironment.App,
            origins: {
                FONTSERVICE: fontService,
                IMAGE_OPTIMIZER: imageOptimizer,
                FEEDS_STORAGE: feedStorage,
                FONTS_AZURE_STORAGE_CDN: fontStorage,
                VIDEOS_STORAGE: videoStorage,
                SVG_IMAGE_OPTIMIZER: ''
            }
        };
    }

    setGlobals(): void {
        // in test, we rely on some CSS hax
        if (this.appEnvironment.stage === 'test') {
            document.body.classList.add('in-test');
            this.creativeConfig.environment = AdEnvironment.Test;
        }
    }

    setPage(name: AppView): void {
        this._activePage$.next(name);
        this.activePage = name;

        if (name === AppView.DesignView) {
            this.inPreviewMode = false;
            document.body.classList.remove('manage-view');
            document.body.classList.add('design-view');
        } else if (name === AppView.ManageView) {
            this.inPreviewMode = true;
            document.body.classList.remove('design-view');
            document.body.classList.add('manage-view');
        }
    }

    isPage(name: AppView): boolean {
        return this.activePage === name;
    }

    private isShowcaseUrl(url: string): boolean {
        return !!url.match(/share/);
    }
}
