/* eslint-disable no-console */
import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { toRGBA } from '@creative/color.utils';
import {
    convertBorderToDto,
    convertCustomPropertiesToDapiDto,
    convertGenericStateToDapiDto,
    convertShadowsToDto,
    convertTextShadowsToDtos,
    convertTextStateToDapiDto,
    convertVideoSettingsToDto,
    serializeDapiTextSegments
} from '@creative/serialization';
import { convertNodeToDapiElementDto } from '@creative/serialization/design-api/data-element-serializer';
import {
    getOverrideDtoType,
    isCustomFeedPropertyDto,
    isCustomTextPropertyDto,
    isDapiEllipseElementOverrideDto,
    isDapiGroupNodeOverrideDto,
    isDapiImageElementOverrideDto,
    isDapiRectangleElementOverrideDto,
    isDapiTextLikeElementOverrideDto,
    isDapiTextLikeNodeDto,
    isDapiVideoElementOverrideDto,
    isDapiWidgetElementOverrideDto,
    isDapiWidgetNodeDto,
    isOverrideObject
} from '@data/deserialization/design-api/helpers';
import {
    createElementOverrideDtoFromDataNode,
    isValidElementOverrideKey,
    mutateDapiElement,
    serializeDimension,
    serializePosition
} from '@data/deserialization/design-api/mutation';
import {
    ElementOverrideDtoKeys,
    OneOfElementOverrideDtos,
    OneOfVisualElementOverrideDtos
} from '@domain/api/design-api.interop';
import {
    CreativeSetDto,
    EllipseElementOverrideDto,
    ImageAssetDto,
    ImageElementOverrideDto,
    NodeOverrideDto,
    RectangleElementOverrideDto,
    SizeDto,
    TextLikeElementOverrideDto,
    VideoAssetDto,
    VideoElementOverrideDto,
    WidgetElementOverrideDto
} from '@domain/api/generated/design-api';
import { MutationContext, MutationEntities } from '@domain/creativeset/creativeset-mutation';
import { IElementChange } from '@domain/element-change';
import {
    ICreativeDataNode,
    OneOfDataNodes,
    OneOfElementPropertyKeys,
    OneOfTextDataNodes
} from '@domain/nodes';
import { HttpRequestObservable } from '@studio/domain/api/error.types';
import { cloneDeep } from '@studio/utils/clone';
import { uuidv4 } from '@studio/utils/id';
import { take, tap } from 'rxjs';
import { FeatureService } from '../feature/feature.service';
import { CreativesetDataService } from './creativeset.data.service';

type AllowedPropagation = {
    creative: boolean;
    version: boolean;
    size: boolean;
};

@Injectable({
    providedIn: 'root'
})
export class CreativeSetMutationService {
    private creativesetDataService = inject(CreativesetDataService);
    private featureService = inject(FeatureService);
    private mutationContext: MutationContext;
    private currentEntities: MutationEntities;
    private creativeSet?: CreativeSetDto;
    private mutatedProperties = new Set<ElementOverrideDtoKeys>();
    private allowedPropagation: AllowedPropagation = {
        creative: false,
        version: false,
        size: false
    };

    constructor() {
        this.creativesetDataService.creativeset$.pipe(take(1), takeUntilDestroyed()).subscribe(() => {
            if (this.creativesetDataService.pristineDapiCreativeSet) {
                this.reset();
                this.setPropagationRules();
            }
        });
    }

    save(): HttpRequestObservable<void> {
        return this.creativesetDataService
            .saveDesignApiCreativeset(this.creativeSet!)
            .pipe(tap(() => this.reset()));
    }

    reset(): void {
        this.creativeSet = cloneDeep(this.creativesetDataService.pristineDapiCreativeSet);
    }

    /** Returns the mutated CreativeSetDto. */
    getCreativeSet(): CreativeSetDto {
        return this.creativeSet!;
    }

    setMutationContext(mutationContext: MutationContext): void {
        this.mutationContext = mutationContext;
    }

    setCurrentMutationEntities(entities: MutationEntities): void {
        this.currentEntities = entities;
        this.setPropagationRules();
    }

    private setPropagationRules(): void {
        if (!this.creativeSet || !this.currentEntities) {
            return;
        }

        this.allowedPropagation.version = this.creativeSet.versions.length > 1;
        this.allowedPropagation.size =
            this.creativeSet.sizes.filter(({ design }) => !!design).length === 1 &&
            !!this.currentEntities.size.design;
    }

    /**
     *
     * @param elementChange
     * @param creativeDataNode
     * @param forcedMutationContext Provide a forced mutation context to override the defaults
     * @returns
     */
    mutateDapiElementData(
        elementChange: IElementChange,
        forcedMutationContext?: MutationContext
    ): void {
        const creativeset = this.creativeSet;

        if (!creativeset || !this.featureService.isFeatureEnabled('design-api-put')) {
            return;
        }

        const { element: dataNode, changes } = elementChange;

        if (!dataNode || Object.keys(changes).length === 0) {
            return;
        }

        /**
         * Apply the default mutation context naively.
         * It can technically be multiple changes but it is
         * mainly only when moving or resizing elements that there
         * are multiple changes at the same time.
         */
        if (!forcedMutationContext) {
            this.applyDefaultMutationContext(Object.keys(changes)[0] as OneOfElementPropertyKeys);
        }

        console.group('Mutation');

        const entityText = `${this.mutationContext.context} with id ${this.mutationContext.entity.id}`;
        console.log(`Element with id ${dataNode.id} on entity ${entityText} changed`);

        const element = this.getElementOfCurrentMutationContext(dataNode);

        const isGroupNodeOverride = isDapiGroupNodeOverrideDto(element);
        const isRectangleOrEllipseElementOverride =
            isDapiRectangleElementOverrideDto(element) || isDapiEllipseElementOverrideDto(element);
        const isTextElementOverride = isDapiTextLikeElementOverrideDto(element);
        const isImageElementOverride = isDapiImageElementOverrideDto(element);
        const isVideoElementOverride = isDapiVideoElementOverrideDto(element);
        const isWidgetElementOverride = isDapiWidgetElementOverrideDto(element);

        for (const prop in changes) {
            const property = prop as OneOfElementPropertyKeys;

            // Remove override from all "active" entities
            this.removePropertyFromOverrides(dataNode, property);

            this.mutateNodeOverride(element, property, changes);

            if (isGroupNodeOverride) {
                continue;
            }

            this.mutateElementNodeOverride(element, property, changes);

            if (isRectangleOrEllipseElementOverride) {
                this.mutateRectangleAndEllipseElementOverride(element, property, changes);
            }

            if (isTextElementOverride) {
                // Content property is changed on interactionEnd (text editing blur)
                this.mutateTextElementOverride(element, property, changes);
                continue;
            }

            if (isImageElementOverride) {
                this.mutateImageElementOverride(element, property, changes);
                continue;
            }

            if (isVideoElementOverride) {
                this.mutateVideoElementOverride(element, property, changes);
                continue;
            }

            if (isWidgetElementOverride) {
                this.mutateWidgetElementOverride(element, property, changes);
                continue;
            }
        }

        for (const property in changes) {
            const valueLog = property === 'content' ? element['textSegments'] : element[property];
            console.log(`Property ${property} changed to`, valueLog);
        }

        this.propagateElementProperty(element, dataNode);

        console.log('Context:', this.mutationContext);
        console.log('Element:', element);
        console.log('Creativeset:', creativeset);
        console.groupEnd();
    }

    addDapiElement(dataNode: OneOfDataNodes, creativeDataNode: ICreativeDataNode): void {
        const creativeset = this.creativeSet;

        if (!creativeset || !this.featureService.isFeatureEnabled('design-api-put')) {
            return;
        }

        const dapiElement = convertNodeToDapiElementDto(dataNode, creativeDataNode);
        const parentNode = dataNode.__parentNode ?? dataNode.__rootNode;

        if (!parentNode) {
            throw new Error('No valid parentNode');
        }

        const sortIndex = parentNode.elements.length ? parentNode.elements.length - 1 : 0;

        const sizeElementOverride: SizeDto['elements'][number] = {
            $type: getOverrideDtoType(dapiElement),
            sortIndex
        };

        this.currentEntities.size.elements[dataNode.id] = sizeElementOverride;

        // Apply TextElement legacy properties
        if (
            isDapiTextLikeElementOverrideDto(sizeElementOverride) &&
            isDapiTextLikeNodeDto(dapiElement)
        ) {
            const legacyVpId = uuidv4();
            this.currentEntities.version.elements[dataNode.id] = {
                $type: sizeElementOverride.$type,
                l_VpId: legacyVpId,
                textSegments: dapiElement.textSegments
            };
            dapiElement.textSegments = [];
            dapiElement.l_VpId = legacyVpId;
        }

        // Apply WidgetElement legacy properties
        if (isDapiWidgetNodeDto(dapiElement)) {
            for (const customProperty of dapiElement.customProperties) {
                if (isCustomTextPropertyDto(customProperty)) {
                    customProperty.l_VpId = uuidv4();
                    customProperty.l_VpName = 'widgetText';
                }

                if (isCustomFeedPropertyDto(customProperty)) {
                    customProperty.l_VpId = uuidv4();
                    customProperty.l_VpName = 'feed';
                }
            }
        }

        creativeset.elementsPool.push(dapiElement);

        console.group('Element added');
        console.log('poolElement:', dapiElement);
        console.log('sizeElement:', this.currentEntities.size.elements[dataNode.id]);
        console.log(creativeset);
        console.groupEnd();
    }

    deleteDapiElement(dataNode: OneOfDataNodes): void {
        const creativeset = this.creativeSet;

        if (!creativeset || !this.featureService.isFeatureEnabled('design-api-put')) {
            return;
        }

        const allOverrides = [...creativeset.sizes, ...creativeset.creatives, ...creativeset.versions];

        console.group('Element deleted');

        delete this.currentEntities.size.elements[dataNode.id];
        delete this.currentEntities.version.elements[dataNode.id];
        delete this.currentEntities.creative.elements[dataNode.id];

        const shouldRemoveFromPool = !allOverrides.some(({ elements }) => elements[dataNode.id]);

        if (shouldRemoveFromPool) {
            const deletedPoolElement = creativeset.elementsPool.find(({ id }) => id === dataNode.id);
            creativeset.elementsPool = creativeset.elementsPool.filter(({ id }) => id !== dataNode.id);
            console.log('poolElement deleted:');
            console.log(deletedPoolElement);
        }

        console.log(creativeset);
        console.groupEnd();
    }

    mutatateDesign(creativeDataNode: ICreativeDataNode): void {
        if (!this.featureService.isFeatureEnabled('design-api-put')) {
            return;
        }

        if (this.mutationContext.context !== 'size') {
            return;
        }

        const design = this.mutationContext.entity.design;

        if (!design) {
            return;
        }

        design.audio = creativeDataNode.audio;
        design.fill = toRGBA(creativeDataNode.fill);
        design.gifExport = creativeDataNode.gifExport;
        design.guidelines = creativeDataNode.guidelines;
        design.loops = creativeDataNode.loops;
        design.preloadImage = creativeDataNode.preloadImage;
        design.socialGuide = creativeDataNode.socialGuide;
        design.startTime = creativeDataNode.startTime;
        design.stopTime = creativeDataNode.stopTime;

        console.group('Design');
        console.log(design);
        console.groupEnd();
    }

    private applyDefaultMutationContext(property: OneOfElementPropertyKeys): void {
        switch (property) {
            case 'content':
                if (!this.allowedPropagation.size) {
                    this.setMutationContext({
                        context: 'creative',
                        entity: this.currentEntities.creative
                    });
                    break;
                }

                this.setMutationContext({
                    context: 'version',
                    entity: this.currentEntities.version
                });
                break;

            default:
                this.setMutationContext({
                    context: 'size',
                    entity: this.currentEntities.size
                });
                break;
        }
    }

    private getElementOfCurrentMutationContext(dataNode: OneOfDataNodes): OneOfElementOverrideDtos {
        const entity = this.mutationContext.entity;
        const entityElement = entity.elements;

        if (!entityElement[dataNode.id]) {
            return this.addElementToCurrentContextEntity(dataNode);
        }

        return entityElement[dataNode.id];
    }

    private addElementToCurrentContextEntity(dataNode: OneOfDataNodes): OneOfElementOverrideDtos {
        const entity = this.mutationContext.entity;
        const entityElement = entity.elements;

        entityElement[dataNode.id] = createElementOverrideDtoFromDataNode(dataNode);

        return entityElement[dataNode.id];
    }

    private removePropertyFromOverrides(
        dataNode: OneOfDataNodes,
        property: OneOfElementPropertyKeys
    ): void {
        const { creative, size, version } = this.currentEntities;

        delete version.elements[dataNode.id]?.[property];
        delete size.elements[dataNode.id]?.[property];
        delete creative.elements[dataNode.id]?.[property];
    }

    /**
     * Propagate property to poolElement.
     * Note that this more or less will only occur while the creative set has 1 size or more than 1 version
     */
    private propagateElementProperty(
        element: OneOfElementOverrideDtos,
        dataNode: OneOfDataNodes
    ): void {
        const creativeset = this.creativeSet;

        if (!this.allowedPropagation.version && !this.allowedPropagation.size) {
            return;
        }

        const poolElement = creativeset?.elementsPool.find(({ id }) => id === dataNode.id);

        if (!poolElement) {
            throw new Error('Not possible to propagate property to non-existing poolElement.');
        }

        console.group('Propagating to poolElement:');

        for (const property of this.mutatedProperties) {
            if (property === 'textSegments') {
                console.log('Propagation of textSegments skipped');
                continue;
            }

            console.log('Propagated', property, element[property]);

            if (this.allowedPropagation.size) {
                if (element[property]) {
                    const propertyValue = element[property];

                    if (isOverrideObject(propertyValue)) {
                        poolElement[property] = propertyValue.value;
                    } else {
                        poolElement[property] = propertyValue;
                    }

                    delete element[property];
                }
            }
        }

        console.log('poolElement:', poolElement);

        console.groupEnd();

        this.mutatedProperties.clear();
    }

    private mutateNodeOverride(
        element: NodeOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, 'NodeOverrideDto')) {
            return;
        }

        switch (property) {
            case 'hidden':
                mutateDapiElement(element, 'hidden', changes.hidden);
                break;

            case 'locked':
                mutateDapiElement(element, 'locked', changes.locked);
                break;

            case 'parentNodeId':
                mutateDapiElement(element, 'parentNodeId', { value: changes.parentNodeId });
                break;
        }

        this.mutatedProperties.add(property);
    }

    private mutateElementNodeOverride(
        element: OneOfVisualElementOverrideDtos,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, 'ElementOverrideDto')) {
            return;
        }

        switch (property) {
            case 'actions':
                mutateDapiElement(element, 'actions', changes.actions);
                break;

            case 'animations':
                mutateDapiElement(element, 'animations', changes.animations);
                break;

            case 'border':
                mutateDapiElement(element, 'border', { value: convertBorderToDto(changes.border) });
                break;

            case 'duration':
                mutateDapiElement(element, 'duration', changes.duration);
                break;

            case 'fill':
                mutateDapiElement(element, 'fill', {
                    value: changes.fill ? toRGBA(changes.fill) : undefined
                });
                break;

            case 'filters':
                mutateDapiElement(element, 'filters', changes.filters);
                break;

            case 'height':
                mutateDapiElement(element, 'height', serializeDimension(changes.height));
                break;

            case 'mirrorX':
                mutateDapiElement(element, 'mirrorX', changes.mirrorX);
                break;

            case 'mirrorY':
                mutateDapiElement(element, 'mirrorY', changes.mirrorY);
                break;

            case 'opacity':
                mutateDapiElement(element, 'opacity', changes.opacity);
                break;

            case 'originX':
                mutateDapiElement(element, 'originX', changes.originX);
                break;

            case 'originY':
                mutateDapiElement(element, 'originY', changes.originY);
                break;

            case 'radius':
                mutateDapiElement(element, 'radius', changes.radius);
                break;

            case 'ratio':
                mutateDapiElement(element, 'ratio', { value: changes.ratio });
                break;

            case 'rotationX':
                mutateDapiElement(element, 'rotationX', changes.rotationX);
                break;

            case 'rotationY':
                mutateDapiElement(element, 'rotationY', changes.rotationY);
                break;

            case 'rotationZ':
                mutateDapiElement(element, 'rotationZ', changes.rotationZ);
                break;

            case 'scaleX':
                mutateDapiElement(element, 'scaleX', changes.scaleX);
                break;

            case 'scaleY':
                mutateDapiElement(element, 'scaleY', changes.scaleY);
                break;

            case 'shadows':
                mutateDapiElement(
                    element,
                    'shadows',
                    changes.shadows ? convertShadowsToDto(changes.shadows) : undefined
                );
                break;

            case 'time':
                mutateDapiElement(element, 'time', changes.time);
                break;

            case 'width':
                mutateDapiElement(element, 'width', serializeDimension(changes.width));
                break;

            case 'x':
                mutateDapiElement(element, 'x', serializePosition(changes.x));
                break;

            case 'y':
                mutateDapiElement(element, 'y', serializePosition(changes.y));
                break;

            case 'parentId':
                mutateDapiElement(element, 'parentId', { value: changes.parentId });
                break;
        }

        this.mutatedProperties.add(property);
    }

    private mutateRectangleAndEllipseElementOverride(
        element: RectangleElementOverrideDto | EllipseElementOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, element.$type)) {
            return;
        }

        switch (property) {
            case 'masking':
                mutateDapiElement(element, 'masking', { value: changes.masking });
                break;

            case 'states':
                mutateDapiElement(element, 'states', changes.states?.map(convertGenericStateToDapiDto));
                break;
        }

        this.mutatedProperties.add(property);
    }

    private mutateTextElementOverride(
        element: TextLikeElementOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (property === 'content') {
            mutateDapiElement(
                element,
                'textSegments',
                serializeDapiTextSegments(changes as OneOfTextDataNodes)
            );
            this.mutatedProperties.add('textSegments');
            return;
        }

        if (!isValidElementOverrideKey(property, element.$type)) {
            return;
        }

        switch (property) {
            case 'states':
                mutateDapiElement(element, 'states', changes.states?.map(convertTextStateToDapiDto));
                break;

            case 'textColor':
                if (changes.textColor) {
                    mutateDapiElement(element, 'textColor', toRGBA(changes.textColor));
                }
                break;

            case 'textShadows':
                mutateDapiElement(
                    element,
                    'textShadows',
                    convertTextShadowsToDtos(changes.textShadows)
                );
                break;

            case 'font':
                mutateDapiElement(element, 'font', { value: changes.font?.id });
                break;

            case 'fontSize':
                mutateDapiElement(element, 'fontSize', changes.fontSize);
                break;

            case 'characterSpacing':
                mutateDapiElement(element, 'characterSpacing', changes.characterSpacing);
                break;

            case 'horizontalAlignment':
                mutateDapiElement(element, 'horizontalAlignment', changes.horizontalAlignment);
                break;

            case 'lineHeight':
                mutateDapiElement(element, 'lineHeight', changes.lineHeight);
                break;

            case 'maxRows':
                mutateDapiElement(element, 'maxRows', changes.maxRows);
                break;

            case 'padding':
                mutateDapiElement(element, 'padding', changes.padding);
                break;

            case 'strikethrough':
                mutateDapiElement(element, 'strikethrough', changes.strikethrough);
                break;

            case 'textOverflow':
                mutateDapiElement(element, 'textOverflow', changes.textOverflow);
                break;

            case 'underline':
                mutateDapiElement(element, 'underline', changes.underline);
                break;

            case 'uppercase':
                mutateDapiElement(element, 'uppercase', changes.uppercase);
                break;

            case 'verticalAlignment':
                mutateDapiElement(element, 'verticalAlignment', changes.verticalAlignment);
                break;
        }

        this.mutatedProperties.add(property);
    }

    private mutateImageElementOverride(
        element: ImageElementOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, element.$type)) {
            return;
        }

        switch (property) {
            case 'feed':
                mutateDapiElement(element, 'feed', { value: changes.feed });
                break;

            case 'imageAsset':
                const imageLibraryAsset = this.creativesetDataService.getAsset(
                    changes.imageAsset?.id,
                    'image'
                );
                const imageAsset: ImageAssetDto | undefined = imageLibraryAsset
                    ? {
                          ...imageLibraryAsset,
                          id: +imageLibraryAsset.id
                      }
                    : undefined;
                mutateDapiElement(element, 'imageAsset', { value: imageAsset });
                break;

            case 'imageSettings':
                mutateDapiElement(element, 'imageSettings', changes.imageSettings);
                break;

            case 'masking':
                mutateDapiElement(element, 'masking', { value: changes.masking });
                break;

            case 'states':
                mutateDapiElement(element, 'states', changes.states?.map(convertGenericStateToDapiDto));
                break;
        }

        this.mutatedProperties.add(property);
    }

    private mutateVideoElementOverride(
        element: VideoElementOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, element.$type)) {
            return;
        }

        switch (property) {
            case 'feed':
                mutateDapiElement(element, 'feed', { value: changes.feed });
                break;

            case 'videoAsset': {
                const videoLibraryAsset = this.creativesetDataService.getAsset(
                    changes.videoAsset?.id,
                    'video'
                );
                const videoAsset: VideoAssetDto | undefined = videoLibraryAsset
                    ? {
                          ...videoLibraryAsset,
                          id: +videoLibraryAsset.id
                      }
                    : undefined;
                mutateDapiElement(element, 'videoAsset', { value: videoAsset });
                break;
            }

            case 'videoSettings':
                mutateDapiElement(
                    element,
                    'videoSettings',
                    changes.videoSettings ? convertVideoSettingsToDto(changes.videoSettings) : undefined
                );
                break;

            case 'masking':
                mutateDapiElement(element, 'masking', { value: changes.masking });
                break;

            case 'states':
                mutateDapiElement(element, 'states', changes.states?.map(convertGenericStateToDapiDto));
                break;

            case 'checksum':
                mutateDapiElement(element, 'checksum', { value: changes.checksum });
        }

        this.mutatedProperties.add(property);
    }

    private mutateWidgetElementOverride(
        element: WidgetElementOverrideDto,
        property: OneOfElementPropertyKeys,
        changes: IElementChange['changes']
    ): void {
        if (!isValidElementOverrideKey(property, element.$type)) {
            return;
        }

        switch (property) {
            case 'customProperties':
                mutateDapiElement(
                    element,
                    'customProperties',
                    changes.customProperties
                        ? convertCustomPropertiesToDapiDto(changes.customProperties)
                        : undefined
                );
                break;

            case 'states':
                mutateDapiElement(element, 'states', changes.states?.map(convertGenericStateToDapiDto));
                break;
        }

        this.mutatedProperties.add(property);
    }
}
