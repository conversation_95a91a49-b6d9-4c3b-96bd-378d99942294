import { parseColor, toHEX } from '@creative/color.utils';
import { cloneDeep } from '../clone';
import {
    clamp,
    decimal,
    decimalToPercentage,
    deepEqual,
    distinctCharsFromString,
    exclude,
    generateUniqueName,
    getIntersectionArray,
    getObjectDifference,
    isMultipleOf,
    lerp,
    moveItemInArray,
    moveItemsInList,
    omitUndefined,
    roundToNearestMultiple,
    sanitizeString,
    sortByFormatSize,
    toFixedDecimal
} from '../utils';

describe('Utils', () => {
    const OLD_ENV = process.env;

    beforeEach(() => {
        jest.resetModules(); // clears the cache
        process.env = { ...OLD_ENV }; // Make a copy
    });

    afterAll(() => {
        process.env = OLD_ENV; // Restore old environment
    });

    describe('clamp', () => {
        it('should return the value within the range', () => {
            const result = clamp(5, 0, 10);
            expect(result).toBe(5);
        });

        it('should return the minimum value', () => {
            const result = clamp(-4, 0, 10);
            expect(result).toBe(0);
        });

        it('should return the maximum value', () => {
            const result = clamp(15, 0, 10);
            expect(result).toBe(10);
        });

        it('should return min value if max value is smaller than minValue', () => {
            const result = clamp(15, 0, -10);
            expect(result).toBe(0);
        });

        it('should not have a max limit when no maxValue is provided', () => {
            const result = clamp(200, 0);
            expect(result).toBe(200);
        });

        it('should not have a min limit when no minValue is provided', () => {
            const result = clamp(-200, undefined, 0);
            expect(result).toBe(-200);
        });
    });

    describe('moveItemInArray', () => {
        it('should move item 0 in array to index 3', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            moveItemInArray(arr, 0, 3);
            expect(arr).toEqual([1, 2, 3, 0, 4, 5, 6]);
        });

        it('should move item 3 in array to index 0', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            moveItemInArray(arr, 3, 0);
            expect(arr).toEqual([3, 0, 1, 2, 4, 5, 6]);
        });

        it('should throw error when moving item outside array range', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            expect(() => moveItemInArray(arr, 0, 7)).toThrow();
        });
    });

    describe('moveItemsInList', () => {
        it('should move item 0 in array to index 3', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            const newArrray = moveItemsInList(arr, [0], 3);
            expect(newArrray).toEqual([1, 2, 3, 0, 4, 5, 6]);
        });

        it('should move item 3 in array to index 0', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            const newArrray = moveItemsInList(arr, [3], 0);
            expect(newArrray).toEqual([3, 0, 1, 2, 4, 5, 6]);
        });

        it('should move item 3 and 2 in array to index 0', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            const newArrray = moveItemsInList(arr, [3, 2], 0);
            expect(newArrray).toEqual([3, 2, 0, 1, 4, 5, 6]);
        });

        it('should not change array when trying to move to the same index', () => {
            const arr = [1, 2, 3, 4, 5];
            const newArrray = moveItemsInList(arr, [5], 4);
            expect(newArrray).toEqual([1, 2, 3, 4, 5]);
        });

        it('should throw error when moving item outside array range', () => {
            const arr = [0, 1, 2, 3, 4, 5, 6];
            expect(() => moveItemsInList(arr, [0], 7)).toThrow();
        });
    });

    describe('lerp', () => {
        it('0.5 should return the average', () => {
            expect(lerp(0, 2, 0.5)).toBe(1);
        });
        it('should return the "to value" for progress 1', () => {
            expect(lerp(-2, 2, 1)).toBe(2);
        });
        it('should return the "from value" for progress 0', () => {
            expect(lerp(-2, 2, 0)).toBe(-2);
        });
        it('should continue curve in the same pace for bigger values than 1', () => {
            expect(lerp(-2, 2, 2)).toBe(6);
        });
        it('should never change the value when interpolating between the same property ', () => {
            expect(lerp(0.5, 0.5, 0.003999999999999958)).toBe(0.5);
            expect(lerp(0.5, 0.5, 0.996)).toBe(0.5);
        });
    });

    describe('decimal', () => {
        it('should round to 2 decimals', () => {
            expect(decimal(1.12345)).toBe(1.12);
        });

        it('should round to 2 decimals', () => {
            expect(decimal(-1123)).toBe(-1123);
        });

        it('should handle strange flotaing points', () => {
            expect(decimal(0.4 + 0.2)).toBe(0.6);
        });
    });

    describe('roundToNearestMultiple', () => {
        it('should round to 2 decimals when providing 0.01', () => {
            expect(roundToNearestMultiple(1.12345, 0.01)).toBe(1.12);
        });

        it('should round to nearest hundred when providing 100', () => {
            expect(roundToNearestMultiple(-1123, 100)).toBe(-1100);
        });

        it('should round to nearest tenth when providing 0.1', () => {
            // 0.2 + 0.4 = 0.6000000000000001...
            expect(roundToNearestMultiple(0.4 + 0.2, 0.1)).toBe(0.6);
        });
    });

    describe('isMultipleOf', () => {
        it('should detect if value is a mutiple of a decimal value', () => {
            expect(isMultipleOf(32.4, 0.1)).toBe(true);
        });
        it('should handle floating points', () => {
            expect(isMultipleOf(29.000000000000155, 1)).toBe(true);
            // 0.2 + 0.4 = 0.6000000000000001...
            expect(isMultipleOf(0.4 + 0.2, 0.1)).toBe(true);
        });

        it('should detect if a value is a multiple of a integer', () => {
            expect(isMultipleOf(30.1, 10)).toBe(false);
        });
    });

    describe('toFixedDecimal', () => {
        it('should pad to 0 decimals', () => {
            const result = toFixedDecimal(1.12345, 0);
            expect(result).toBe(1);
        });

        it('should pad to 1 decimals', () => {
            const result = toFixedDecimal(1.12345, 1);
            expect(result).toBe(1.1);
        });

        it('should pad to 2 decimals', () => {
            const result = toFixedDecimal(1.12345, 2);
            expect(result).toBe(1.12);
        });

        it('should pad to 6 decimals by adding a 0', () => {
            const result = toFixedDecimal(1.12345, 6);
            expect(result).toBe(1.12345);
        });

        it('should convert a string (that is a valid number) to floating before padding', () => {
            const result = toFixedDecimal('1.123');
            expect(result).toBe(1.123);
        });

        it('should convert a string (that is a valid number) to floating before padding', () => {
            expect(() => toFixedDecimal('Hello world!')).toThrowError();
        });
    });

    describe('sortByFormatSize', () => {
        it('should sort a flat array of sizes', () => {
            const arr = [
                {
                    width: 200,
                    height: 200
                },
                {
                    width: 500,
                    height: 300
                },
                {
                    width: 300,
                    height: 300
                },
                {
                    width: 300,
                    height: 250
                }
            ];
            const result = sortByFormatSize(arr);
            expect(result).toEqual([
                {
                    width: 200,
                    height: 200
                },
                {
                    width: 300,
                    height: 250
                },
                {
                    width: 300,
                    height: 300
                },
                {
                    width: 500,
                    height: 300
                }
            ]);
        });

        it('should sort an array of deeply nested sizes', () => {
            const arr = [
                {
                    size: {
                        width: 200,
                        height: 200
                    }
                },
                {
                    size: {
                        width: 500,
                        height: 300
                    }
                },
                {
                    size: {
                        width: 300,
                        height: 300
                    }
                },
                {
                    size: {
                        width: 300,
                        height: 250
                    }
                }
            ];
            const result = sortByFormatSize(arr, 'size');
            expect(result).toEqual([
                {
                    size: {
                        width: 200,
                        height: 200
                    }
                },
                {
                    size: {
                        width: 300,
                        height: 250
                    }
                },
                {
                    size: {
                        width: 300,
                        height: 300
                    }
                },
                {
                    size: {
                        width: 500,
                        height: 300
                    }
                }
            ]);
        });
    });

    describe('distinctCharsFromString', () => {
        it("should distinct the string's characters and sort them alphanumerically", () => {
            const string = 'aaa2cb1c3YXZA3B!%&.B';
            const expected = [
                '!',
                '%',
                '&',
                '.',
                '1',
                '2',
                '3',
                'A',
                'B',
                'X',
                'Y',
                'Z',
                'a',
                'b',
                'c'
            ];
            const result = distinctCharsFromString(string);

            expect(result).toStrictEqual(expected);
        });

        it('should normalize characters using NFC and NFKD strategies - Thai', () => {
            const thaiText = 'ำ'; // length 1
            const result = distinctCharsFromString(thaiText);
            expect(result).toHaveLength(3);
        });

        it('should normalize characters using NFC and NFKD strategies - Swedish', () => {
            const swedishText = 'ä'; // length 1
            const result = distinctCharsFromString(swedishText);
            expect(result).toHaveLength(3);
        });

        it('should distinct Swedish text', () => {
            const swedishText = 'är år är';
            // å is composed of 2 unicode characters, a and combining ring "̊ above
            const result = distinctCharsFromString(swedishText); // length 7

            expect(result).toStrictEqual([' ', 'a', '̈', 'a', '̊', 'r', 'ä', 'å']);
        });

        it('should normalize characters using NFC and NFKD strategies - Latin characters', () => {
            const latinText = 'a'; // length 1
            const result = distinctCharsFromString(latinText);

            expect(result).toHaveLength(1); // shouldn't be affected
        });

        it('should distinct a string including emojis', () => {
            const latinText = 'DELTA I SOMMERFESTIVALEN 😎STILL DE ANDRE I SKYGGEN🌞😎';
            const result = distinctCharsFromString(latinText);

            expect(result).toHaveLength(19);
            expect(result).toStrictEqual([
                ' ',
                'A',
                'D',
                'E',
                'F',
                'G',
                'I',
                'K',
                'L',
                'M',
                'N',
                'O',
                'R',
                'S',
                'T',
                'V',
                'Y',
                '🌞',
                '😎'
            ]);
        });

        it('should distinct a string including composed characters', () => {
            const stringWithComposingChars = 'Raktárürités';
            const result = distinctCharsFromString(stringWithComposingChars);
            expect(result).toStrictEqual([
                'R',
                'a',
                'a',
                '́',
                'e',
                '́',
                'i',
                'k',
                'r',
                's',
                't',
                'u',
                '̈',
                'á',
                'é',
                'ü'
            ]);
        });
    });

    describe('deepEqual', () => {
        it('should compare numbers', () => {
            expect(deepEqual(13, 13)).toBeTruthy();
            expect(deepEqual(2, 1)).toBeFalsy();
        });
        it('should compare strings', () => {
            expect(deepEqual('abc', 'abc')).toBeTruthy();
            expect(deepEqual('abc', 'bde')).toBeFalsy();
        });

        it('should compare objects', () => {
            const a = { number: 1, string: 'abc', object: { d: 'Hi' } };
            const b = { number: 1, string: 'abc', object: { d: 'Hi' } };
            const c = { number: 1, string: 'abc', object: { d: 'Hi!' } };
            expect(deepEqual(a, b)).toBeTruthy();
            expect(deepEqual(a, c)).toBeFalsy();
        });

        it('should ignore keys beginning with "__"', () => {
            const a = { number: 1, string: 'abc', object: { __d: 1 } };
            const b = { number: 1, string: 'abc', object: { __d: 0 } };
            expect(deepEqual(a, b)).toBeTruthy();
        });

        it('should compare arrays', () => {
            const a = [1, 2, 3];
            const b = [1, 2, 3];
            const c = [3, 2, 1];
            expect(deepEqual(a, b)).toBeTruthy();
            expect(deepEqual(a, c)).toBeFalsy();
        });

        it('should compare colors', () => {
            const a = parseColor('#000000');
            const b = parseColor('#000');
            const c = parseColor('#FF0000');
            expect(deepEqual(a, b)).toBeTruthy();
            expect(deepEqual(a, c)).toBeFalsy();
        });
    });

    describe('cloneDeep', () => {
        it('should create new instance of object', () => {
            const obj = { start: 1, end: 2 };
            const clone = cloneDeep(obj);
            expect(clone).not.toBe(obj);
        });

        it('should create new instance of object', () => {
            const obj = { start: 1, end: 2, deep: { name: 'Halo' } };
            const clone = cloneDeep(obj);
            expect(clone.deep?.name).toBe('Halo');
        });

        it('should handle Color', () => {
            const color = parseColor('#FFF');
            const obj = { start: 1, end: 2, color };
            const clone = cloneDeep(obj);
            expect(toHEX(clone.color)).toBe('#FFFFFF');
            expect(clone.color).not.toBe(color);
        });
    });

    describe('omitUndefined', () => {
        it('should remove any undefined properties', () => {
            const obj = omitUndefined({ a: undefined, b: 2, c: undefined, d: 3 });
            expect(obj).not.toHaveProperty('a');
            expect(obj).not.toHaveProperty('c');
            expect(obj.b).toBe(2);
            expect(obj.d).toBe(3);
        });
    });

    describe('decimalToPercentage', () => {
        it('should default to 100% if value is undefined', () => {
            expect(decimalToPercentage(undefined)).toEqual(100);
        });

        it('should return 0 if 0 is passed as decimal argument', () => {
            expect(decimalToPercentage(0)).toEqual(0);
        });

        it('should return 100 if 1 is passed as decimal argument', () => {
            expect(decimalToPercentage(1)).toEqual(100);
        });
    });

    describe('getObjectDifference', () => {
        it('should return the difference between two objects', () => {
            const result = getObjectDifference({ a: 1, b: 2, c: 3 }, { a: 3, b: 2, d: 4 });
            expect(result).toEqual({ a: 3, c: undefined, d: 4 });
        });

        it('should return added parameters', () => {
            expect(getObjectDifference({ a: 1 }, { a: 1, b: 2 })).toEqual({ b: 2 });
        });

        it('should return removed parameters', () => {
            expect('a' in getObjectDifference({ a: 1 }, {})).toBeTruthy();
        });
    });

    describe('sanitizeString', () => {
        it('should return empty string if undefined is passed as argument', () => {
            const sanitized = undefined as unknown as string;
            expect(sanitizeString(sanitized)).toEqual('');
        });

        it('should sanitize by escaping double backslash', () => {
            expect(sanitizeString('a\\b')).toEqual('a\\\\b');
        });

        it('should sanitize by escaping quotation marks', () => {
            expect(sanitizeString('a"b')).toEqual('a"b');
        });

        it('should sanitize by replacing ampersand with namespace', () => {
            expect(sanitizeString('a&b')).toEqual('a;ampersand;b');
        });
    });

    describe('getIntersectionArray', () => {
        it('should return an array with only the values that exist in both input arrays', () => {
            const arr1 = [1, 2, 3, 4];
            const arr2 = [3, 4, 5, 6];
            const expected = [3, 4];

            const result = getIntersectionArray(arr1, arr2);
            expect(result).toEqual(expected);
        });

        it('should return an empty array if the input arrays have no common values', () => {
            const arr1 = [1, 2, 3, 4];
            const arr2 = [5, 6, 7, 8];
            const expected = [];

            const result = getIntersectionArray(arr1, arr2);
            expect(result).toEqual(expected);
        });

        it('should return an empty array if either of the input arrays is empty', () => {
            const arr1 = [1, 2, 3, 4];
            const arr2 = [];
            const expected = [];

            const result = getIntersectionArray(arr1, arr2);
            expect(result).toEqual(expected);
        });
    });

    describe('generateUniqueName', () => {
        it('should return the same name if it is unique', () => {
            const items = [
                { name: 'item1', id: '1' },
                { name: 'item2', id: '2' }
            ];
            const name = 'item3';
            const result = generateUniqueName(name, items);
            expect(result).toBe(name);
        });

        it('should trim name when generating unique name', () => {
            const items = [
                { name: 'item1', id: '1' },
                { name: 'item2', id: '2' }
            ];
            const name = 'item1  ';
            const result = generateUniqueName(name, items);
            expect(result).toBe('item1 (1)');
        });

        it('should append (1) to the name if it is not unique', () => {
            const items = [
                { name: 'item1', id: '1' },
                { name: 'item2', id: '2' }
            ];
            const name = 'item1';
            const result = generateUniqueName(name, items);
            expect(result).toBe('item1 (1)');
        });

        it('should increment the number in parentheses if the name is not unique and already ends with a number in parentheses', () => {
            const items = [
                { name: 'item1', id: '1' },
                { name: 'item1 (1)', id: '2' }
            ];
            const name = 'item1 (1)';
            const result = generateUniqueName(name, items);
            expect(result).toBe('item1 (2)');
        });
    });

    describe('exclude', () => {
        it('should exclude items from array', () => {
            const array = ['a', 'b', 'c', 'd'] as const;

            const newArray = exclude(array, 'c', 'd');
            expect(newArray).toEqual(['a', 'b']);
        });
    });
});
