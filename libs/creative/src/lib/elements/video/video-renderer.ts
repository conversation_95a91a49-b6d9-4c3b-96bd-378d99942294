import { destroyDiInstance, diInject } from '@di/di';
import { Token } from '@di/di.token';
import { AdEnvironment } from '@domain/ad/environment';
import { IVideoRenderer } from '@domain/creative/elements/video/video-renderer.header';
import { AppView } from '@domain/creative/environment';
import { IVideoElementDataNode, IVideoViewElement, OneOfElementDataNodes } from '@domain/nodes';
import { IVideoRendererSettings, VideoSettingPropertyType, VideoSizeMode } from '@domain/video';
import { isAppleBrowser } from '@studio/utils/ad/browser';
import { isH265HVC1Supported, isHlsSupported } from '@studio/utils/ad/browser-feature-support';
import { getH265FallbackVideoUrl, updateVideoUrlToManifestUrl } from '@studio/utils/asset';
import { cloneDeep } from '@studio/utils/clone';
import { getVideoErrorMessage, VideoError } from '@studio/utils/errors';
import { ErrorMessage } from '@studio/utils/errors/error-message.enum';
import {
    hasParameter,
    isBase64Mp4Video,
    isRelativeUrl,
    parameterToBool,
    replaceOrigin
} from '@studio/utils/url';
import { deepEqual } from '@studio/utils/utils';
import { isValidUrl } from '@studio/utils/validation';
import {
    CREATIVE_MUTED_ICON,
    CREATIVE_UNMUTED_ICON,
    DEFAULT_VIDEO_STYLES,
    PLAYBACK_BUTTON_HTML
} from './video-renderer.constants';
import { StreamManager } from './video-streamer/stream-manager';
import {
    getEffectiveVideoDuration,
    getVideoEndTime,
    getVideoStartTime
} from './video-time-calculations';
/** @@remove STUDIO:START */
import { createSVGLoaderImage } from '../../svg-background';
/** @@remove STUDIO:END */

const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';
const FRAME_RATE_TOLERANCE = 1 / 60; // Tolerance for frame rate, so we don't miss the end of the video time

export class VideoRenderer implements IVideoRenderer {
    private _settings: IVideoRendererSettings;
    private _dataElement: IVideoElementDataNode;
    private _loaderElement: SVGElement;
    private _shadowRoot: ShadowRoot;
    private _videoDOMElement: HTMLVideoElement;
    private _videoPlaybackButtons: HTMLDivElement;
    private _requestId: number;
    private _destroyed = false;
    private _userInitiatedPlaybackState: 'play' | 'pause' | undefined;
    private _streamManager?: StreamManager;
    private _streamingMode?: 'dash' | 'hls';
    private _creativeMuted = false;
    private _effectiveVideoDuration = 0;

    private get creativeRootElement(): HTMLDivElement {
        return this._renderer.rootElement;
    }
    private get _isPlaying(): boolean {
        return !this._videoDOMElement.paused;
    }

    private get _progressTime(): number {
        return this._animator.time - this._viewElement.time;
    }

    private get _startTime(): number {
        const startTime = getVideoStartTime({
            duration: this.duration_m,
            effectiveDuration: this._effectiveVideoDuration,
            progressTime: this._progressTime,
            settingsStartTime: this._settings.startTime
        });
        return startTime;
    }

    private get _endTime(): number {
        return getVideoEndTime({
            progressTime: this._progressTime,
            effectiveDuration: this._effectiveVideoDuration
        });
    }

    private get canStartPlaying(): boolean {
        return (
            !!(this._settings.loop && this._effectiveVideoDuration) ||
            this._progressTime < this._effectiveVideoDuration
        );
    }

    get duration_m(): number {
        return this._videoDOMElement.duration;
    }

    get videoDOMElement_m(): HTMLVideoElement {
        return this._videoDOMElement;
    }

    private _ad = diInject(Token.AD);
    private _renderer = diInject(Token.RENDERER);
    private _feedStore = diInject(Token.FEED_STORE, { optional: true });
    private _animator = diInject(Token.ANIMATOR);
    private _creativeConfig = diInject(Token.CREATIVE_CONFIG);

    constructor(private _viewElement: IVideoViewElement) {
        this._dataElement = _viewElement.__data;

        this._settings = {
            ...this._dataElement.videoSettings,
            url: this._getVideoUrl()
        };

        if (!this._animator) {
            throw new Error('Animator not provided.');
        }

        this._animator.on('play', this._onAnimatorPlay);
        this._animator.on('pause', this._pause);
        this._animator.on('stop', this._stop);
        this._animator.on('loop', this._onAnimationLoop);
        this._animator.on('animation_start', this._onAnimationStart);
        this._animator.on('animation_end', this._onAnimationEnd);
        this._animator.on('seek', this._onAnimatorSeek);
        this._animator.on('isPlaying', this._onAnimatorIsPlaying);
        this._renderer.on('creativeAudioToggled', this._onCreativeAudioSettingToggled);
        this._renderer.on('mute', this._onRendererMute);
        this._init();
    }

    private async _init(): Promise<void> {
        this._creativeMuted = this._isVideosDefaultMuted();
        await this._createVideoDOMElement();
        this.updateVideo(true);

        if (this._shouldShowCreativeAudioToggle()) {
            this._onCreativeAudioSettingToggled(this._renderer.creativeDocument.audio);
        }
    }

    private async _createVideoDOMElement(): Promise<void> {
        const videoRenderer = document.createElement('video-renderer');
        this._shadowRoot = videoRenderer.attachShadow({ mode: 'open' });
        videoRenderer.style.width = '100%';
        videoRenderer.style.height = '100%';
        videoRenderer.style.display = 'block';

        const video = document.createElement('video');
        video.preload = 'auto';
        this._setMuted(video);
        video.setAttribute('playsinline', '');
        video.style.pointerEvents = 'none';
        video.disableRemotePlayback = true; // Required for Apple to play streaming with ManagedMediaSource

        this._videoDOMElement = video;

        this._setStreamingMode(video);

        this._setStartTime();

        this._preparePreloading();

        this._setVideoStyles();

        try {
            await this._initVideoSource();
        } catch (e: unknown) {
            const error = e as Error;
            throw new VideoError(`${ErrorMessage.VideoSourceInitFailed}: ${error.message}`);
        }

        this._shadowRoot.appendChild(this._videoDOMElement);
        const foreignObject = this._viewElement.__rootElement?.querySelector('foreignObject');
        foreignObject?.firstElementChild?.appendChild(videoRenderer);
        this._createPlaybackButtons();
    }

    private _setStreamingMode(video: HTMLVideoElement): void {
        if (
            this._creativeConfig.environment === AdEnvironment.LiveAd &&
            !this._dataElement.feed && // Streaming not supported with feeds yet
            this._dataElement.videoSettings.streaming.enabled &&
            this._isStreamingEnabledInUrlParams()
        ) {
            this._streamingMode = isAppleBrowser && isHlsSupported(video) ? 'hls' : 'dash';
        }
    }

    private _isStreamingEnabledInUrlParams(): boolean {
        if (hasParameter(this._ad.parameters, 'streaming')) {
            return !!parameterToBool(this._ad.parameters.streaming);
        }
        return true;
    }

    private _setVideoStyles(): void {
        const style = document.createElement('style');
        style.textContent = DEFAULT_VIDEO_STYLES;

        this._shadowRoot.appendChild(style);
    }

    private _createPlaybackButtons(): void {
        this._videoPlaybackButtons = document.createElement('div');
        this._videoPlaybackButtons.className = 'playbackButton';
        this._videoPlaybackButtons.style.position = 'absolute';
        this._videoPlaybackButtons.style.display = 'none';
        this._videoPlaybackButtons.style.top = '0px';
        this._videoPlaybackButtons.style.left = '0px';
        this._videoPlaybackButtons.style.width = '100%';
        this._videoPlaybackButtons.style.height = '100%';
        this._videoPlaybackButtons.style.objectFit = 'cover';
        this._videoPlaybackButtons.style.objectPosition = 'center center';
        this._videoPlaybackButtons.addEventListener('click', this._onPlaybackButtonClick);

        this._videoPlaybackButtons.innerHTML = PLAYBACK_BUTTON_HTML;
        this._shadowRoot.appendChild(this._videoPlaybackButtons);
    }

    private _onPlaybackButtonClick = (event: MouseEvent): void => {
        if (this._renderer.isEditorMode) {
            return;
        }

        event.stopPropagation();
        event.preventDefault();

        if (!this._isPlaying) {
            this._userInitiatedPlaybackState = 'play';
            this._play();
        } else {
            this._userInitiatedPlaybackState = 'pause';
            this._pause();
        }
    };

    private _preparePreloading(): void {
        let videoPromise: { resolve: () => void; reject: (error: Error) => void };
        const videoLoadPromise = new Promise<void>((resolve, reject) => {
            videoPromise = {
                resolve,
                reject
            };
        });

        this._renderer.preloadingElements.set(this._dataElement.id, videoLoadPromise);

        // HLS on Safari IOS requires the loadedmetadata event to be triggered before the video can be played
        // https://stackoverflow.com/questions/50051639/javascript-html5-video-event-canplay-not-firing-on-safari
        const eventType = this._streamingMode === 'hls' ? 'loadedmetadata' : 'canplaythrough';

        const onVideoLoaded = (): void => {
            videoPromise.resolve();
            this.setEffectiveVideoDuration();
            this._videoDOMElement.removeEventListener(eventType, onVideoLoaded);
        };

        this._videoDOMElement.addEventListener(eventType, onVideoLoaded);

        const onVideoError = async (): Promise<void> => {
            const videoError = new VideoError(getVideoErrorMessage(this._videoDOMElement.error?.code), {
                src: this._videoDOMElement.src,
                code: this._videoDOMElement.error?.code
            });
            videoPromise.reject(videoError);
            this._videoDOMElement.removeEventListener('error', onVideoError);
            await this.destroy();
        };
        this._videoDOMElement.addEventListener('error', onVideoError);
        this._videoDOMElement.addEventListener('ended', this._onVideoEnd);
    }

    updateVideo(initialRender?: boolean): void {
        /** @@remove STUDIO:START */
        this._setVideoLoader();
        /** @@remove STUDIO:END */

        if ((this._isPlaying || this._animator.isPlaying) && !initialRender) {
            return;
        }

        for (const videoSetting in this._settings) {
            const setting = videoSetting as keyof IVideoRendererSettings;

            if (
                !initialRender &&
                deepEqual(this._settings[setting], this._dataElement.videoSettings[setting])
            ) {
                continue;
            }

            const settingValue = this._getElementSettingValue(setting);
            this._settings[setting as string] = cloneDeep(settingValue);

            /**
             * The autoplay attribute always has to be unset as it instead
             * has to be controlled from a timeline standpoint through
             * the animator events
             **/
            if (setting === 'autoplay') {
                continue;
            }

            switch (setting) {
                case 'startTime':
                case 'endTime':
                    this._setStartTime();
                    this.setEffectiveVideoDuration();
                    break;
                case 'url':
                    this._setVideoSrc();
                    break;
                case 'sizeMode':
                    this._setSizeMode();
                    break;
                case 'playbackButton':
                    this._setPlaybackButton();
                    break;
                default:
                    this._videoDOMElement[setting] = settingValue;
                    break;
            }
        }

        if (this._startTime !== this._videoDOMElement.currentTime) {
            this._setStartTime();
        }
    }

    private setEffectiveVideoDuration(): void {
        if (!this.duration_m) {
            this._effectiveVideoDuration = 0;
        } else {
            const { startTime = 0, endTime } = this._settings;
            this._effectiveVideoDuration = getEffectiveVideoDuration({
                duration: this.duration_m,
                settingsStartTime: startTime,
                settingsEndTime: endTime
            });
        }
    }

    private _setMuted(video: HTMLVideoElement): void {
        video.muted = this._isVideosDefaultMuted();
    }

    private _isVideosDefaultMuted(): boolean {
        return (
            this._creativeConfig.environment !== AdEnvironment.VideoGenerator &&
            this._creativeConfig.appView !== AppView.DesignView
        );
    }

    private _setPlaybackButton(): void {
        const { color, size, enabled } = this._settings.playbackButton;
        const style = this._videoPlaybackButtons.style;

        style.display = enabled || size === 0 ? 'block' : 'none';
        style.setProperty(PlaybackButtonVariable.Color, color.toString());
        style.setProperty(PlaybackButtonVariable.Size, String(size / 100));
        this._setPlaybackButtonState();
    }

    private _setPlaybackButtonState(): void {
        const style = this._videoPlaybackButtons.style;
        const playButtonVisible = this._isPlaying ? 'none' : 'block';
        const pauseButtonVisible = !this._isPlaying ? 'none' : 'block';

        style.setProperty(PlaybackButtonVariable.PlayState, playButtonVisible);
        style.setProperty(PlaybackButtonVariable.PauseState, pauseButtonVisible);
    }

    private _getVideoUrl(): string {
        if (this._dataElement.feed) {
            const feedValue = this._feedStore?.getFeedValueUrl(
                this._dataElement.feed,
                this._dataElement.id,
                this._dataElement
            );
            return (feedValue ?? '').trim();
        }

        let assetUrl = this._dataElement.videoAsset?.url;
        if (!assetUrl) {
            throw new Error('Video asset URL is not defined.');
        }

        if (
            this._creativeConfig.environment === AdEnvironment.LiveAd &&
            !this._streamingMode &&
            this._videoDOMElement &&
            isH265HVC1Supported(this._videoDOMElement)
        ) {
            assetUrl = getH265FallbackVideoUrl(assetUrl);
        }

        const customOrigin = this._creativeConfig.CUSTOM_AD_DOMAIN;
        if (customOrigin) {
            return replaceOrigin(assetUrl, customOrigin).trim();
        }

        return assetUrl
            .trim()
            .replace('http://storage-emulator:10000', this._creativeConfig.origins.VIDEOS_STORAGE);
    }

    private _getElementSettingValue(
        setting: keyof IVideoRendererSettings
    ): VideoSettingPropertyType | string {
        if (setting === 'url') {
            return this._getVideoUrl();
        }

        return this._dataElement.videoSettings[setting];
    }

    private _setSizeMode(): void {
        const mode = this._dataElement.videoSettings.sizeMode;

        switch (mode) {
            case VideoSizeMode.Fit:
                this._videoDOMElement.classList.remove('fillMode');
                break;
            case VideoSizeMode.Fill:
                this._videoDOMElement.classList.add('fillMode');
                break;
        }
    }

    private async _initVideoSource(): Promise<void> {
        if (this._streamingMode) {
            try {
                if (this._streamingMode === 'hls') {
                    await this._setHlsVideoStream();
                } else {
                    await this._setDashVideoStream();
                }
            } catch {
                delete this._streamManager;
                this._streamingMode = undefined;
                console.warn(`Failed to stream video, falling back to: ${this._getVideoUrl()}`);
                this._setVideoSrc();
            }
        } else {
            this._setVideoSrc();
        }
    }

    private async _setDashVideoStream(): Promise<void> {
        const manifestUrl = updateVideoUrlToManifestUrl(this._getVideoUrl(), 'dash');
        this._streamManager = new StreamManager(this._videoDOMElement, manifestUrl);
        this._videoDOMElement.src = await this._streamManager.loadStreams_m();
    }

    private async _setHlsVideoStream(): Promise<void> {
        const manifestUrl = updateVideoUrlToManifestUrl(this._getVideoUrl(), 'hls');

        this._streamManager = new StreamManager(this._videoDOMElement, manifestUrl);
        this._videoDOMElement.src = await this._streamManager.loadStreams_m();
    }

    private _setVideoSrc(): void {
        const url = this._getVideoUrl();

        if (this._streamingMode || this._videoDOMElement.src === url || !this._isSrcValid(url)) {
            return;
        }

        this._videoDOMElement.src = url;
        this._videoDOMElement.load();
    }

    private _isSrcValid(url: string): boolean {
        if (!url) {
            return false;
        }
        if (isRelativeUrl(url)) {
            return true;
        }
        const { pathname, hostname, href: src } = new URL(url);
        const isStorageEmulatorUrl = hostname === 'storage-emulator';
        const isBase64VideoUrl = isBase64Mp4Video(pathname);

        return !(isStorageEmulatorUrl && isValidUrl(src) && isBase64VideoUrl);
    }

    private _onAnimatorPlay = (): void => {
        this._userInitiatedPlaybackState = undefined;

        if (!this._settings.autoplay) {
            return;
        }
        const animatorTime = this._animator?.time || 0;
        if (animatorTime >= this._viewElement.time) {
            this._play();
        }
    };

    private _onAnimatorIsPlaying = (isPlaying: boolean): void => {
        if (this._renderer.audioToggle_m) {
            this._handleCreativeAudioToggleVisibility(isPlaying);
        }
    };

    private _onAnimationStart = (element: OneOfElementDataNodes): void => {
        if (this._dataElement.id !== element.id) {
            return;
        }

        if (!this._settings.autoplay) {
            if (!this._userInitiatedPlaybackState || this._userInitiatedPlaybackState === 'pause') {
                return;
            }
        }

        if (!this._settings.loop && this._settings.restartWithCreative) {
            this._loop();
            this._play();
        } else if (this._settings.loop) {
            this._play();
        }
    };

    private _onAnimationLoop = (): void => {
        if (this._settings.restartWithCreative) {
            this._loop();
        }
        if (this._dataElement.feed) {
            this._setVideoSrc();
            this._play();
        }
    };

    private _onAnimationEnd = (element: OneOfElementDataNodes): void => {
        if (this._dataElement.id !== element.id) {
            return;
        }

        this._pause();

        if (this._animator.duration > this._dataElement.duration) {
            this._loop();
        }
    };

    private _onAnimatorSeek = (): void => {
        if (!this._videoDOMElement) {
            return;
        }
        this._setStartTime();
    };

    private _onRendererMute = (muted: boolean): void => {
        this._creativeMuted = muted;
        this._setAudioToggleIcon(muted);
        if (this._videoDOMElement) {
            this._videoDOMElement.muted = muted;
        }

        if (this._streamManager) {
            this._streamManager.toggleAudioMute_m(muted);
        }
    };

    private _play = (): void => {
        if (this._isPlaying) {
            // Trigger check when looped video is playing
            this._requestId = requestAnimationFrame(this._checkVideoEnd);
            return;
        }

        if (!this.canStartPlaying) {
            return;
        }

        this._videoDOMElement.play();
        this._setPlaybackButtonState();
        this._requestId = requestAnimationFrame(this._checkVideoEnd);
    };

    private _pause = (): void => {
        const continueLooping = !this._settings.stopWithCreative && this._settings.loop;
        if (continueLooping && this._creativeConfig.environment === AdEnvironment.LiveAd) {
            return;
        }
        this._videoDOMElement.pause();
        this._setPlaybackButtonState();
        cancelAnimationFrame(this._requestId);
    };

    private _stop = (): void => {
        if (this._settings.stopWithCreative) {
            this._pause();
        }
    };

    private _loop(): void {
        this._setStartTime();
    }

    private _onVideoEnd = (): void => {
        if (!this._isPlaying) {
            return;
        }

        if (this._settings.loop) {
            this._loop();
            this._play();
        } else {
            this.videoDOMElement_m.currentTime =
                this._settings.startTime + this._effectiveVideoDuration;
            this._pause();
        }
    };

    private _setStartTime = (): void => {
        if (this._isPlaying) {
            // When video is playing, reset currentTime to settings start time
            this._videoDOMElement.currentTime = this._settings.startTime + this._progressTime;
            return;
        }

        // When video is not playing, use calculated start time if video can start playing, otherwise show end frame
        this._videoDOMElement.currentTime = this.canStartPlaying
            ? this._startTime
            : this._settings.startTime + this._effectiveVideoDuration + this._progressTime;
    };

    private _checkVideoEnd = (): void => {
        if (this._destroyed) {
            return;
        }

        const timePassedOnVideoWithTolerance = this._progressTime + FRAME_RATE_TOLERANCE;
        if (this._endTime && timePassedOnVideoWithTolerance >= this._endTime) {
            this._onVideoEnd();
            return;
        }

        this._requestId = requestAnimationFrame(this._checkVideoEnd);
    };

    /**
     * Adds or removes an audio toggle to the creative, appended in the creative root element
     * @param showAudioToggle Controls whether the audio toggle should be visible or not
     */
    private _onCreativeAudioSettingToggled = (showAudioToggle: boolean | undefined): void => {
        if (!showAudioToggle) {
            this._removeCreativeAudioToggle();

            // Toggle back video audio to default behaviour
            this._creativeMuted = this._isVideosDefaultMuted();
            this._setMuted(this.videoDOMElement_m);
            return;
        }

        // toggle will exist already when node is rerendered
        if (!this._renderer.audioToggle_m) {
            this._renderer.audioToggle_m = this._createAudioToggleElement();
        }

        this._renderer.rootElement.appendChild(this._renderer.audioToggle_m);
    };

    private _createAudioToggleElement(): HTMLDivElement {
        const audioToggleElement = document.createElement('div');
        Object.assign(audioToggleElement.style, {
            borderRadius: '4px',
            width: '42px',
            height: '42px',
            background: 'black',
            opacity: '0.3',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            bottom: '4px',
            left: '4px'
        });

        const svg = document.createElementNS(SVG_NAMESPACE, 'svg');
        const iconPath = document.createElementNS(SVG_NAMESPACE, 'path');

        svg.setAttribute('viewBox', '0 0 28 28');
        svg.setAttribute('width', '20');
        svg.setAttribute('height', '20');
        iconPath.setAttribute('d', this._creativeMuted ? CREATIVE_MUTED_ICON : CREATIVE_UNMUTED_ICON);
        iconPath.setAttribute('fill', 'white');

        svg.appendChild(iconPath);
        audioToggleElement.appendChild(svg);

        audioToggleElement.addEventListener('click', this._onCreativeAudioToggleClicked);

        return audioToggleElement;
    }

    private _onCreativeAudioToggleClicked = (event: MouseEvent): void => {
        event.stopPropagation();
        this._creativeMuted = !this._creativeMuted;
        this._setAudioToggleIcon(this._creativeMuted);
        this._renderer.mute(this._creativeMuted);
    };

    private _setAudioToggleIcon(muted: boolean): void {
        const iconPath = this._renderer.audioToggle_m?.querySelector('path');
        iconPath?.setAttribute('d', muted ? CREATIVE_MUTED_ICON : CREATIVE_UNMUTED_ICON);
    }

    private _handleCreativeAudioToggleVisibility(isPlaying: boolean): void {
        if (!this._renderer.audioToggle_m) {
            return;
        }

        this._renderer.audioToggle_m.style.visibility = isPlaying ? 'hidden' : 'visible';

        if (isPlaying) {
            this.creativeRootElement.addEventListener('mouseout', this._showCreativeAudioToggle);
            this.creativeRootElement.addEventListener('mouseover', this._showCreativeAudioToggle);
        } else {
            this.creativeRootElement.removeEventListener('mouseover', this._showCreativeAudioToggle);
            this.creativeRootElement.removeEventListener('mouseout', this._showCreativeAudioToggle);
        }
    }

    private _showCreativeAudioToggle = ({ type }: MouseEvent): void => {
        if (this._renderer.audioToggle_m) {
            this._renderer.audioToggle_m.style.visibility = type === 'mouseover' ? 'visible' : 'hidden';
        }
    };

    private _shouldShowCreativeAudioToggle(): boolean {
        const { environment } = this._creativeConfig;
        return (
            environment === AdEnvironment.App ||
            environment === AdEnvironment.LiveAd ||
            environment === AdEnvironment.Preview
        );
    }

    private _removeCreativeAudioToggle(): void {
        this.creativeRootElement.removeEventListener('mouseover', this._showCreativeAudioToggle);
        this.creativeRootElement.removeEventListener('mouseout', this._showCreativeAudioToggle);
        this._renderer.audioToggle_m?.removeEventListener('click', this._onCreativeAudioToggleClicked);
        this._renderer.audioToggle_m?.remove();
        this._renderer.audioToggle_m = undefined;
    }

    /** @@remove STUDIO:START */
    private _setVideoLoader(): void {
        const loading = this._viewElement.__data.videoAsset?.__loading;

        if (loading) {
            this._showLoader();
        } else {
            this._hideLoader();
        }
    }

    private _showLoader(): void {
        if (!this._loaderElement) {
            this._createLoaderElement();
        }
        if (!this._loaderElement.parentNode) {
            this._shadowRoot.appendChild(this._loaderElement);
        }
    }

    private _createLoaderElement(): void {
        this._loaderElement = document.createElementNS(SVG_NAMESPACE, 'svg');
        const svg = createSVGLoaderImage(this._viewElement);
        const { style } = this._loaderElement;

        style.position = 'absolute';
        style.left = '0';
        style.top = '0';
        style.width = '100%';
        style.height = '100%';
        style.overflow = 'visible';
        style.pointerEvents = 'none';

        this._loaderElement.appendChild(svg);
    }

    private _hideLoader(): void {
        if (this._loaderElement?.parentNode) {
            this._shadowRoot.removeChild(this._loaderElement);
        }
    }

    /** @@remove STUDIO:END */

    async destroy(): Promise<void> {
        this._animator?.off('play', this._onAnimatorPlay);
        this._animator?.off('pause', this._pause);
        this._animator?.off('stop', this._stop);
        this._animator?.off('loop', this._onAnimationLoop);
        this._animator?.off('animation_start', this._onAnimationStart);
        this._animator?.off('animation_end', this._onAnimationEnd);
        this._animator?.off('seek', this._onAnimatorSeek);
        this._animator?.off('isPlaying', this._onAnimatorIsPlaying);
        this._renderer?.off('mute', this._onRendererMute);
        this._renderer?.off('creativeAudioToggled', this._onCreativeAudioSettingToggled);

        const videoElements = this._renderer.creativeDocument.elements.filter(
            element => element.kind === 'video'
        );
        const preloadPromises = videoElements.map(element => {
            const promise = this._renderer.preloadingElements.get(element.id);
            return promise ? promise.catch(() => true) : Promise.resolve(false);
        });

        const preloadStatuses = await Promise.all(preloadPromises);
        const isAllRejected = preloadStatuses.every(Boolean);

        // Remove audio toggle if all video elements errors or no video elements exist
        if (isAllRejected || videoElements.length === 0) {
            this._removeCreativeAudioToggle();
        }

        this._videoDOMElement.removeEventListener('ended', this._onVideoEnd);
        cancelAnimationFrame(this._requestId);
        this._destroyed = true;
        destroyDiInstance(this);
    }
}

enum PlaybackButtonVariable {
    Color = '--playbackButtonColor',
    Size = '--playbackButtonSize',
    PlayState = '--playbackButtonPlayState',
    PauseState = '--playbackButtonPauseState'
}
