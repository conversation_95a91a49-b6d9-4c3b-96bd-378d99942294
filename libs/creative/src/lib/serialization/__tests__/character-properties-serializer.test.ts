import { ICharacterProperties, SpanType } from '@domain/text';
import { createMockCharacterProperties } from '../__mocks__/character-properties.mock';
import { convertCharacterStyleToDto, getTextSegmentType } from '../character-properties-serializer';

describe('convertCharacterStyleToDto', () => {
    it('should convert character properties', () => {
        const style: ICharacterProperties = createMockCharacterProperties({
            font: {
                id: 'arial',
                weight: 2,
                style: 'italic',
                src: '',
                fontFamilyId: ''
            },
            variable: {
                id: 'myVariable',
                type: 'text',
                path: 'myPath',
                fallback: 'myFallback',
                step: {
                    start: 1,
                    size: 1,
                    occurrence: 'loop'
                }
            }
        });
        const expected = {
            font: 'arial',
            characterSpacing: 0,
            lineHeight: 1,
            fontSize: 25,
            variable: {
                id: 'myVariable',
                type: 'text',
                path: 'myPath',
                fallback: 'myFallback',
                step: {
                    size: 1,
                    start: 1,
                    occurrence: 'loop'
                }
            }
        };
        expect(convertCharacterStyleToDto(style)).toEqual(expected);
    });

    it('should convert character properties with empty variable', () => {
        const style: ICharacterProperties = createMockCharacterProperties({
            variable: undefined
        });
        expect(convertCharacterStyleToDto(style).variable).toEqual(undefined);
    });

    it('should convert font size to min if its out of bounds', () => {
        const style: ICharacterProperties = createMockCharacterProperties({
            fontSize: -4
        });
        expect(convertCharacterStyleToDto(style).fontSize).toEqual(0.01);
    });

    it('should convert font size to max if its out of bounds', () => {
        const style: ICharacterProperties = createMockCharacterProperties({
            fontSize: 25000
        });
        expect(convertCharacterStyleToDto(style).fontSize).toEqual(2500);
    });

    describe('getTextSegmentType', () => {
        it('should correctly parse words', () => {
            expect(getTextSegmentType({ content: '乡月' })).toBe(SpanType.Word);
            expect(getTextSegmentType({ content: 'Ἑλληνικ' })).toBe(SpanType.Word);
            expect(getTextSegmentType({ content: 'abcdefghijklmnopqrstvwxyzpåäö' })).toBe(
                SpanType.Word
            );
            expect(getTextSegmentType({ content: '|  Gratis' })).toBe(SpanType.Word);
        });

        it('should correctly parse spaces', () => {
            expect(getTextSegmentType({ content: ' ' })).toBe(SpanType.Space);
            expect(getTextSegmentType({ content: '    ' })).toBe(SpanType.Space);
            const whitespaces =
                '\u0020\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u200B\u200C\u200D\ufeff'.split(
                    ''
                );
            for (const whitespace of whitespaces) {
                expect(getTextSegmentType({ content: whitespace })).toBe(SpanType.Space);
            }
        });

        it('should correctly parse newlines', () => {
            const newlineSegment = {
                content: '\n'
            };
            expect(getTextSegmentType(newlineSegment)).toBe(SpanType.Newline);
        });
    });
});
