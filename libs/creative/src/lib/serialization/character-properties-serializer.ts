import { TextLikeElementDto, TextSegmentDto } from '@domain/api/generated/design-api';
import { CharacterStyleDto, CharacterStyleValueDto } from '@domain/api/generated/sapi';
import { IFontStyle } from '@domain/font';
import { IButtonElementDataNode, ITextElementDataNode } from '@domain/nodes';
import { ITextShadow } from '@domain/style';
import {
    ICharacterProperties,
    ICharacterStylesMap,
    IContentSpan,
    IText,
    ITextVariable,
    SpanType
} from '@domain/text';
import { clamp, decimal } from '@studio/utils/utils';
import { fromRGBAstring } from '../color.utils';
import { copyStyle, getHashFromStyle, isContentSpan } from '../elements/rich-text/utils';
import { convertTextShadowsToDtos } from './property-serializer';

export function convertCharacterStyleToDto(
    style: Partial<ICharacterProperties>
): CharacterStyleValueDto {
    const characterStyleDto: CharacterStyleValueDto = {};
    for (const property in style) {
        const value = style[property];
        switch (property) {
            case '__fontFamilyId':
                continue;
            case 'font':
                if (typeof value === 'string') {
                    characterStyleDto.font = value;
                } else {
                    characterStyleDto.font = (value as IFontStyle).id;
                }
                break;
            case 'textColor':
                characterStyleDto[property] = value.toString();
                break;
            case 'textShadows':
                const shadow = (value || []) as ITextShadow[];
                characterStyleDto[property] = convertTextShadowsToDtos(shadow);
                break;
            case 'variable':
                const variable = value as ITextVariable | undefined;
                if (variable) {
                    variable.type = 'text';
                }
                characterStyleDto.variable = variable;
                break;
            case 'fontSize':
                characterStyleDto.fontSize = decimal(clamp(value, 0.01, 2500));
                break;
            default:
                characterStyleDto[property] = value;
                break;
        }
    }
    return characterStyleDto;
}

export function deserializeCharacterStyles(characterStylesDto: CharacterStyleDto[]): {
    characterStyles: ICharacterStylesMap;
    styleHashMap: Map<string, string>;
} {
    const characterStyles: ICharacterStylesMap = new Map<string, Partial<ICharacterProperties>>();
    const styleHashMap = new Map</* styleHash */ string, /* styleId */ string>();
    for (const characterStyle of characterStylesDto || []) {
        const id = characterStyle.id;
        const style = deserializeCharacterProperties(characterStyle.value);
        styleHashMap.set(getHashFromStyle(style), id);
        characterStyles.set(id, style);
    }

    return { characterStyles, styleHashMap };
}

export function deserializeCharacterProperties(
    characterStyleDto: CharacterStyleValueDto
): Partial<ICharacterProperties> {
    const style = {} as Partial<ICharacterProperties>;
    for (const property in characterStyleDto) {
        switch (property) {
            case 'textColor':
                style[property] = fromRGBAstring(characterStyleDto[property]!);
                break;
            case 'textShadows':
                style[property] = characterStyleDto[property]!.map(s => ({
                    offsetX: s.offsetX,
                    offsetY: s.offsetY,
                    blur: s.blur,
                    color: fromRGBAstring(s.color)
                }));
                break;
            case 'variable':
                const variable = characterStyleDto[property]!;
                if (!variable) {
                    break;
                }

                style[property] = {
                    ...variable,
                    type: 'text'
                };
                break;
            default:
                style[property] = characterStyleDto[property];
                break;
        }
    }
    return style;
}

export function serializeRichText(text: IText): IText {
    return {
        spans: text.spans.map(span => {
            const result = { ...span };
            if (isContentSpan(span)) {
                const r = result as IContentSpan;
                r.styleId = span.styleId;
                r.styleIds = { ...span.styleIds };
                if (span.__previousStyleIdToHistoryIndexMap) {
                    r.__previousStyleIdToHistoryIndexMap = new Map(
                        span.__previousStyleIdToHistoryIndexMap
                    );
                }
            }
            return result;
        }),
        style: copyStyle(text.style)
    };
}

export function deserializeDapiTextSegments(textElement: TextLikeElementDto): {
    characterStyles: ICharacterStylesMap;
    styleHashMap: Map<string, string>;
} {
    const segments = textElement.textSegments;
    const characterStyles: CharacterStyleDto[] = [];

    for (const segment of segments) {
        // Apply character styles to node
        const styleId = segment.l_StyleId;

        if (styleId) {
            const characterStyle: CharacterStyleDto = { id: styleId, value: segment.styles || {} };
            characterStyles.push(characterStyle);
        }
    }

    return deserializeCharacterStyles(characterStyles);
}

export function serializeDapiTextSegments(
    textNode: ITextElementDataNode | IButtonElementDataNode
): TextSegmentDto[] {
    const textSegments: TextSegmentDto[] = [];

    // TODO: set dynamic content
    for (const span of textNode.content.spans) {
        if (!isContentSpan(span)) {
            continue;
        }

        const segment: TextSegmentDto = {
            content: span.content
        };

        if (Object.keys(span.style).length) {
            segment.styles = convertCharacterStyleToDto(span.style);
        }

        if (span.style.variable) {
            segment.variable = span.style.variable;
        }

        if (span.styleId) {
            segment.l_StyleId = span.styleId;
        }

        textSegments.push(segment);
    }

    return textSegments;
}

const onlyWhitespace = /^[\t\f\v\u0020\u2000-\u200D\ufeff]+$/;
/**
 * Should match everything that is not whitespace or newline
 * https://regexr.com/8fc9p
 * \u0020 = regular space
 */
const onlyAlphanumerical = /^[^\t\f\v\u0020\u2000-\u200D\ufeff\r\n]+$/;
const onlyNewline = /(\r\n|\r|\n)/;

export function getTextSegmentType(segment: TextSegmentDto): SpanType {
    if (onlyAlphanumerical.test(segment.content)) {
        return SpanType.Word;
    }

    if (onlyWhitespace.test(segment.content)) {
        return SpanType.Space;
    }

    if (onlyNewline.test(segment.content)) {
        return SpanType.Newline;
    }

    throw new Error(`Unknown text segment type for content: ${segment.content}`);
}
