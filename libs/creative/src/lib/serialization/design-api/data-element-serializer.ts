import {
    OneOfCustomPropertyDtos,
    OneOfElementDtos,
    OneOfVisualElementDtos
} from '@domain/api/design-api.interop';
import {
    ElementDto,
    FeedDto,
    GroupNodeDto,
    ImageElementDto,
    NodeDto,
    TextLikeElementDto,
    VideoElementDto,
    WidgetElementDto,
    WidgetFontStyleDto,
    WidgetImageDto,
    WidgetSelectionOptionDto
} from '@domain/api/generated/design-api';
import {
    ICreativeDataNode,
    IImageElementDataNode,
    IVideoElementDataNode,
    OneOfDataNodes,
    OneOfElementDataNodes,
    OneOfTextDataNodes
} from '@domain/nodes';
import { IWidgetCustomProperty, IWidgetElementDataNode } from '@domain/widget';
import {
    isButtonNode,
    isEllipseNode,
    isGroupDataNode,
    isImageNode,
    isMaskingSupported,
    isRectangleNode,
    isTextNode,
    isVideoNode,
    isWidgetNode
} from '../../nodes/helpers';
import { convertActionToDto } from '../action-serializer';
import { serializeDapiTextSegments } from '../character-properties-serializer';
import {
    convertBorderToDto,
    convertRadiusToDto,
    convertShadowsToDto,
    convertTextShadowsToDtos,
    convertVideoSettingsToDto,
    serializeDapiFilter
} from '../property-serializer';
import { roundDimension, roundPosition } from '../serialization.utils';
import { convertGenericStateToDapiDto, convertTextStateToDapiDto } from '../state-serializer';

type BasePropertiesDto = Omit<NodeDto, '$type'>;
type SharedPropertiesDto = Omit<ElementDto, keyof NodeDto>;
type PrecompletedElementDto = BasePropertiesDto & SharedPropertiesDto;
// a trick in typescript to omit from discriminated union OneOfVisualElementDtos
type OmitFromVisualElementDtos<
    K extends keyof OneOfVisualElementDtos,
    T = OneOfVisualElementDtos
> = T extends unknown ? Omit<T, K> : never;
type ExclusiveElementDto<T extends OneOfVisualElementDtos> = Omit<T, keyof PrecompletedElementDto>;

export function serializeDataNode(
    creativeDataNode: ICreativeDataNode,
    node: OneOfDataNodes
): OneOfElementDtos {
    const baseDto: BasePropertiesDto = {
        id: node.id,
        hidden: node.hidden,
        locked: node.locked,
        name: node.name,
        parentNodeId: node.__parentNode?.id
    };

    if (isGroupDataNode(node)) {
        return {
            ...baseDto,
            $type: 'GroupNodeDto'
        } as GroupNodeDto;
    }

    return { ...baseDto, ...serializeDataElement(creativeDataNode, node) };
}

function serializeDataElement(
    creativeDataNode: ICreativeDataNode,
    node: OneOfElementDataNodes
): OmitFromVisualElementDtos<keyof BasePropertiesDto> {
    const dtoSharedProperties: SharedPropertiesDto = {
        animations: node.animations,
        actions: node.actions.map(action => convertActionToDto(action, creativeDataNode.elements)),
        time: node.time,
        duration: node.duration,
        ratio: node.ratio,
        opacity: node.opacity,
        x: roundPosition(node.x),
        y: roundPosition(node.y),
        width: roundDimension(node.width),
        height: roundDimension(node.height),
        originX: node.originX,
        originY: node.originY,
        rotationX: node.rotationX,
        rotationY: node.rotationY,
        rotationZ: node.rotationZ,
        scaleX: node.scaleX,
        scaleY: node.scaleY,
        radius: convertRadiusToDto(node.radius),
        mirrorX: node.mirrorX,
        mirrorY: node.mirrorY,
        filters: serializeDapiFilter(node.filters),
        border: convertBorderToDto(node.border),
        fill: node.fill?.toString(),
        shadows: node.shadows ? convertShadowsToDto(node.shadows) : undefined,
        parentId: node.parentId
    };

    return {
        ...dtoSharedProperties,
        ...serializeSpecificElementData(node),
        ...(isMaskingSupported(node) ? { masking: node.masking } : {})
    };
}

function serializeTextNode(node: OneOfTextDataNodes): ExclusiveElementDto<TextLikeElementDto> {
    return {
        $type: 'TextLikeElementDto',
        font: node.__fontStyleId ?? node.font?.id,
        states: node.states.map(convertTextStateToDapiDto),
        textColor: node.textColor?.toString(),
        fontSize: node.fontSize,
        lineHeight: node.lineHeight,
        characterSpacing: node.characterSpacing,
        underline: node.underline,
        uppercase: node.uppercase,
        strikethrough: node.strikethrough,
        maxRows: node.maxRows,
        padding: node.padding,
        textOverflow: node.textOverflow,
        horizontalAlignment: node.horizontalAlignment,
        verticalAlignment: node.verticalAlignment,
        textShadows: convertTextShadowsToDtos(node.textShadows),
        textSegments: serializeDapiTextSegments(node),
        isButton: isButtonNode(node)
    };
}

function serializeImageNode(node: IImageElementDataNode): ExclusiveElementDto<ImageElementDto> {
    return {
        $type: 'ImageElementDto',
        states: node.states.map(convertGenericStateToDapiDto),
        imageSettings: { ...node.imageSettings, sizeMode: node.imageSettings.sizeMode },
        imageAsset: node.imageAsset
            ? {
                  id: parseInt(node.imageAsset.id, 10),
                  isGenAi: node.imageAsset.isGenAi,
                  original: {
                      url: node.imageAsset.url,
                      width: node.imageAsset.width,
                      height: node.imageAsset.height
                  },
                  // TODO: Change these properties when asset is mutated
                  thumbnail: node.dapiImageAsset?.thumbnail
                      ? {
                            url: node.dapiImageAsset.thumbnail.url,
                            width: node.dapiImageAsset.thumbnail.width,
                            height: node.dapiImageAsset.thumbnail.height
                        }
                      : {
                            url: node.imageAsset.url,
                            width: node.imageAsset.width,
                            height: node.imageAsset.height
                        },
                  name: node.imageAsset.name!,
                  fileSize: node.dapiImageAsset?.fileSize ?? 0,
                  created: node.dapiImageAsset?.created || new Date().toISOString()
              }
            : undefined
    };
}

function serializeVideoNode(node: IVideoElementDataNode): ExclusiveElementDto<VideoElementDto> {
    return {
        $type: 'VideoElementDto',
        states: node.states.map(convertGenericStateToDapiDto),
        videoSettings: convertVideoSettingsToDto(node.videoSettings),
        videoAsset: node.videoAsset
            ? {
                  ...node.videoAsset,
                  id: parseInt(node.videoAsset.id, 10),
                  // TODO: Change these properties when asset is mutated
                  durationInMilliseconds: node.dapiVideoAsset?.durationInMilliseconds ?? 0,
                  thumbnail: {
                      url: node.dapiVideoAsset?.thumbnail.url || '',
                      width: node.dapiVideoAsset?.thumbnail.width ?? 0,
                      height: node.dapiVideoAsset?.thumbnail.height ?? 0
                  },
                  created: node.dapiVideoAsset?.created || new Date().toISOString()
              }
            : undefined
    };
}

function serializeWidgetNode(node: IWidgetElementDataNode): ExclusiveElementDto<WidgetElementDto> {
    return {
        $type: 'WidgetElementDto',
        states: node.states.map(convertGenericStateToDapiDto),
        customProperties: node.customProperties.map(serializeDapiWidgetCustomProperty),
        widgetContentBlobReference: node.dapiWidgetContentBlobReference!,
        type: node.dapiWidgetType!,
        widgetAsset: node.dapiWidgetAsset
    };
}

function serializeSpecificElementData(
    node: OneOfElementDataNodes
): OmitFromVisualElementDtos<keyof PrecompletedElementDto> {
    if (isTextNode(node)) {
        return serializeTextNode(node);
    }
    if (isImageNode(node)) {
        return serializeImageNode(node);
    }
    if (isVideoNode(node)) {
        return serializeVideoNode(node);
    }
    if (isWidgetNode(node)) {
        return serializeWidgetNode(node);
    }
    if (isRectangleNode(node)) {
        return {
            $type: 'RectangleElementDto',
            states: node.states.map(convertGenericStateToDapiDto)
        };
    }
    if (isEllipseNode(node)) {
        return {
            $type: 'EllipseElementDto',
            states: node.states.map(convertGenericStateToDapiDto)
        };
    }

    throw new Error('unrecognized element type');
}

export function serializeDapiWidgetCustomProperty(
    customProperty: IWidgetCustomProperty
): OneOfCustomPropertyDtos {
    switch (customProperty.unit) {
        case 'boolean':
            return {
                $type: 'CustomPropertyBooleanDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: !!customProperty.value
            };

        case 'color':
            return {
                $type: 'CustomPropertyColorDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value?.toString()
            };

        case 'feed':
            return {
                $type: 'CustomPropertyFeedDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as FeedDto
            };

        case 'font':
            return {
                $type: 'CustomPropertyFontDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as WidgetFontStyleDto
            };

        case 'image':
            return {
                $type: 'CustomPropertyImageDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as WidgetImageDto
            };

        case 'number':
            return {
                $type: 'CustomPropertyNumberDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as number
            };

        case 'select':
            return {
                $type: 'CustomPropertySelectDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as WidgetSelectionOptionDto[]
            };

        case 'text':
            return {
                $type: 'CustomPropertyTextDto',
                name: customProperty.name,
                label: customProperty.label!,
                value: customProperty.value as string
            };
    }
}

export function convertNodeToDapiElementDto(
    element: OneOfDataNodes,
    creativeDataNode: ICreativeDataNode
): OneOfElementDtos {
    return serializeDataNode(creativeDataNode, element);
}
