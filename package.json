{"private": true, "name": "studio", "version": "1.0.0", "packageManager": "pnpm@9.12.3", "scripts": {"build:scripts": "nx run-many --t=build --p=creative,ad", "build:studio:test": "nx build studio --configuration=test", "cf:workers:deploy": "wrangler deploy", "cypress:open": "nx open-cypress studio-e2e", "cypress:run": "nx e2e studio-e2e", "debug:acg": "node --inspect-brk dist/apps/acg/main.js --deps -1", "format:check": "nx format:check", "generate:api-schema": "pnpx esno tools/generators/generate-api-schema.ts", "prepare": "husky", "watch:studio": "nx run studio:build:development -- --watch", "watch:studio:remote": "nx run studio:build:remote -- --watch", "cypress:chromatic:serve": "nx run studio:serve:test"}, "dependencies": {"@actions/core": "^1.11.1", "@actions/github": "^6.0.0", "@angular/animations": "20.0.3", "@angular/cdk": "20.0.3", "@angular/common": "20.0.3", "@angular/core": "20.0.3", "@angular/forms": "20.0.3", "@angular/material": "20.0.3", "@angular/platform-browser": "20.0.3", "@angular/platform-browser-dynamic": "20.0.3", "@angular/router": "20.0.3", "@apollo/client": "^3.12.6", "@auth0/auth0-angular": "^2.2.3", "@azure/app-configuration": "^1.8.0", "@azure/identity": "^4.5.0", "@azure/keyvault-secrets": "^4.9.0", "@azure/opentelemetry-instrumentation-azure-sdk": "1.0.0-beta.7", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "12.17.0", "@bannerflow/comments": "^20.3.3", "@bannerflow/feature-flags": "20.0.0", "@bannerflow/intercom-messenger": "^20.0.5", "@bannerflow/notification": "^20.1.4", "@bannerflow/semantic-conventions": "^1.0.4", "@bannerflow/sentinel": "^20.0.2", "@bannerflow/sentinel-logger": "1.1.3", "@bannerflow/ui": "20.9.11", "@fastify/otel": "^0.5.2", "@fastify/reply-from": "^12.0.2", "@fastify/static": "^8.2.0", "@fastify/view": "11.1.0", "@juggle/resize-observer": "3.4.0", "@keyv/redis": "^4.2.0", "@microsoft/signalr": "8.0.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "4.0.0", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/serve-static": "^5.0.2", "@nestjs/terminus": "^11.0.0", "@newrelic/browser-agent": "^1.293.0", "@ngrx/effects": "19.0.0", "@ngrx/entity": "19.0.0", "@ngrx/operators": "19.0.0", "@ngrx/router-store": "19.0.0", "@ngrx/store": "19.0.0", "@ngrx/store-devtools": "19.0.0", "@nx/devkit": "21.2.0", "@nx/plugin": "21.2.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/auto-instrumentations-node": "^0.57.0", "@opentelemetry/auto-instrumentations-web": "^0.46.0", "@opentelemetry/context-zone": "^2.0.0", "@opentelemetry/core": "^2.0.0", "@opentelemetry/exporter-logs-otlp-http": "^0.200.0", "@opentelemetry/exporter-logs-otlp-proto": "^0.200.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.200.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.200.0", "@opentelemetry/host-metrics": "^0.36.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/instrumentation-document-load": "^0.45.0", "@opentelemetry/instrumentation-fetch": "^0.200.0", "@opentelemetry/instrumentation-winston": "^0.45.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-web": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.30.0", "@opentelemetry/winston-transport": "^0.11.0", "@types/archiver": "^6.0.3", "ag-psd": "^26.0.0", "ajv": "8.12.0", "ajv-formats": "^2.1.1", "ajv-keywords": "5.1.0", "apollo-angular": "8.0.0", "archiver": "^7.0.1", "auth0": "^4.18.0", "axios": "^1.7.8", "cache-manager": "^6.4.0", "cacheable": "^1.8.8", "compression": "1.8.0", "connect-modrewrite": "0.10.2", "cors": "2.8.5", "dayjs": "^1.11.13", "emmet-monaco-es": "5.3.0", "express": "^5.0.1", "fastify": "5.3.3", "gifuct-js": "^2.1.2", "glob": "10.3.10", "graphql": "16.10.0", "graphql-tag": "2.12.6", "hbs": "^4.2.0", "intersection-observer": "0.12.2", "jwks-rsa": "^3.1.0", "keyv": "^5.2.3", "lighthouse": "12.5.1", "monaco-editor": "0.45.0", "mongodb": "^6.11.0", "morgan": "1.10.0", "nest-winston": "^1.10.2", "newrelic": "^12.21.0", "ngrx-store-localstorage": "^19.0.0", "node-os-utils": "^1.3.7", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "playwright": "1.49.0", "puppeteer": "^24.6.0", "qr-code-styling": "^1.8.4", "redis": "^4.7.0", "reflect-metadata": "0.2.2", "rxjs": "^7.8.1", "tslib": "^2.8.1", "unleash-client": "^6.1.3", "unleash-proxy-client": "^3.6.1", "uuid": "^10.0.0", "webpack-remove-code-blocks": "^0.1.6", "winston": "^3.17.0", "zod": "^3.23.8", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "20.0.2", "@angular-devkit/core": "20.0.2", "@angular-devkit/schematics": "20.0.2", "@angular-eslint/eslint-plugin": "20.0.0", "@angular-eslint/eslint-plugin-template": "20.0.0", "@angular-eslint/schematics": "20.0.0", "@angular-eslint/template-parser": "20.0.0", "@angular/cli": "~20.0.0", "@angular/compiler": "20.0.3", "@angular/compiler-cli": "20.0.3", "@angular/language-service": "20.0.3", "@bannerflow/eslint-config-bannerflow": "^20.0.0", "@chromatic-com/cypress": "^0.11.0", "@cloudflare/workers-types": "^4.20250407.0", "@esbuilder/html": "^0.0.6", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "~8.48.0", "@golevelup/ts-jest": "^0.5.6", "@jest/globals": "29.7.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@ngrx/eslint-plugin": "19.0.0", "@ngrx/schematics": "19.0.0", "@nx/angular": "21.2.0", "@nx/cypress": "21.2.0", "@nx/esbuild": "21.2.0", "@nx/eslint-plugin": "21.2.0", "@nx/jest": "21.2.0", "@nx/js": "21.2.0", "@nx/nest": "21.2.0", "@nx/node": "21.2.0", "@nx/playwright": "21.2.0", "@nx/rspack": "21.2.0", "@nx/web": "21.2.0", "@nx/webpack": "21.2.0", "@percy/cli": "^1.30.9", "@percy/playwright": "^1.0.7", "@playwright/test": "1.49.0", "@rspack/cli": "1.0.5", "@rspack/core": "1.3.9", "@rspack/dev-server": "1.1.3", "@rspack/plugin-minify": "^0.7.5", "@schematics/angular": "20.0.2", "@swc-node/register": "1.9.2", "@swc/cli": "0.6.0", "@swc/core": "1.5.7", "@swc/helpers": "~0.5.15", "@types/cache-manager": "^4.0.6", "@types/cors": "2.8.17", "@types/css-font-loading-module": "0.0.13", "@types/express": "^5.0.0", "@types/glob": "8.1.0", "@types/hbs": "^4.0.4", "@types/jest": "29.5.14", "@types/morgan": "1.9.9", "@types/newrelic": "^9.14.6", "@types/node": "20.11.26", "@types/passport-jwt": "^4.0.1", "@types/resize-observer-browser": "0.1.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@typescript-eslint/utils": "^8.33.1", "angular-eslint": "20.0.0", "canvas": "^3.1.0", "chart.js": "^4.4.9", "chromatic": "^11.28.0", "css-loader": "6.7.2", "cypress": "14.4.1", "esbuild": "^0.25.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-cypress": "^4.2.0", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-playwright": "^2.2.0", "husky": "^9.1.7", "jest": "29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.7.0", "jest-preset-angular": "14.6.0", "jest-standard-reporter": "^2.0.0", "jsonc-eslint-parser": "^2.4.0", "lint-staged": "^15.5.2", "nx": "21.2.0", "openapi-typescript": "^7.4.3", "prettier": "3.3.3", "style-loader": "3.3.4", "terser": "5.29.1", "terser-webpack-plugin": "^5.3.10", "ts-jest": "29.1.1", "ts-loader": "9.4.4", "ts-node": "10.9.1", "tsconfig-paths-webpack-plugin": "4.1.0", "typescript": "5.8.3", "webpack": "5.98.0", "webpack-bundle-analyzer": "4.10.1", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.2", "webpack-strip-block": "0.3.0", "wrangler": "4.8.0"}, "lint-staged": {"*.{js,ts}": ["nx affected:lint --quiet --fix --files"], "*": ["nx format:write --files"]}, "pnpm": {"overrides": {"esbuild@<=0.24.2": ">=0.25.0", "koa@<2.16.1": ">=2.16.1", "webpack-dev-server@<=5.2.0": ">=5.2.1", "multer@>=1.4.4-lts.1 <2.0.2": ">=2.0.2", "on-headers@<1.1.0": ">=1.1.0"}}}