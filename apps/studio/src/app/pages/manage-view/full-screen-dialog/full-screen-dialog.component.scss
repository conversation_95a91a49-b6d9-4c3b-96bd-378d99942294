:host ::ng-deep {
    .target-url .wrapper {
        display: flex;
        flex-direction: row-reverse;
        align-items: center;

        .switch {
            margin-left: 10px;
            height: 20px;
            width: 40px;

            &.checked:after {
                left: calc(100% - 16px);
            }

            &:after {
                width: 16px;
                height: 16px;
            }
        }

        .checkbox-label {
            font-family: 'Open Sans', sans-serif;
            font-style: normal;
            font-weight: 700;
            color: var(--ui-color-white);
        }
    }
}

// ::ng-deep .full-screen .header:first-child {
//     display: none;
// }

.full-screen-header,
.full-screen-footer {
    background: rgba(54, 54, 54, 0.9);
    position: absolute;
    width: 100%;
    display: flex;
    align-items: center;
    z-index: 10;
    transition: 200ms all;
}
.full-screen-footer.hidden {
    visibility: hidden;
    position: absolute;
    overflow: hidden;
    height: 0;
}
.full-screen-header.hidden {
    visibility: hidden;
    transform: translateY(-100%);
}

.full-screen-header {
    height: 50px;
    top: 0;
    justify-content: space-between;

    .full-screen-header-left {
        display: flex;
        align-items: center;
        max-height: 100%;
    }

    ui-svg-icon {
        --font-size: 2rem;
        color: var(--ui-color-text-inverted);
        padding: 15px;
        width: auto;
        height: auto;
    }

    ui-toggle-switch {
        margin-right: 10px;
    }
}

.full-screen-body {
    height: var(--app-height);
    display: flex;
    align-items: center;
    justify-content: center;

    .creative-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.full-screen-footer {
    height: 40px;
    bottom: 0;
    justify-content: space-between;

    .size,
    .size-name {
        font-family: 'Open Sans', sans-serif;
        font-style: normal;
    }

    .size {
        font-weight: 700;
        text-transform: uppercase;
        color: var(--ui-color-text-inverted);
    }

    .size-name {
        font-weight: 400;
        color: var(--ui-color-text-second);
        max-width: 50%;
        display: inline-block;
    }

    .size + .size-name {
        margin-left: 7px;
    }

    ui-svg-icon {
        --font-size: 2rem;
        color: var(--ui-static-color-white);
        width: auto;
        height: auto;
        padding: 8.5px 12px;

        &.disable {
            opacity: 0.5;
        }
    }
}
