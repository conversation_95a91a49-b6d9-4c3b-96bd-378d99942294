:where(:root:not([data-uinew]) :host) {
    display: block;

    .custom-size {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;

        &__cross {
            margin: 0 12px;
        }

        #add-button {
            margin-left: 20px;
            width: 60px;

            --background-color: transparent;

            &:not(.disabled) {
                color: var(--studio-color-black);
            }

            &.disabled {
                color: var(--studio-color-grey-dark);
                cursor: default;
                user-select: none;
            }
        }
    }
}
