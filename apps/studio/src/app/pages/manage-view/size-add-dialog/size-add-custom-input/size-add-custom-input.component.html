<div class="custom-size">
    <div class="input-wrapper">
        <ui-number-input
            #widthInput
            data-test-id="custom-width-input"
            id="interaction-custom-width"
            placeholder="Width"
            size="sm"
            [arrowButtons]="false"
            [keyboardEmit]="true"
            [min]="0"
            [value]="customWidth()"
            (valueChange)="setWidth($event)"
            (submit)="addCustomSize()"></ui-number-input>
    </div>

    @if (isNewUI()) {
        <ui-svg-icon
            icon="none"
            nuiIcon="close_small"
            size="xs"></ui-svg-icon>
    } @else {
        <span class="custom-size__cross">×</span>
    }

    <div class="input-wrapper">
        <ui-number-input
            #heightInput
            data-test-id="custom-height-input"
            id="interaction-custom-height"
            placeholder="Height"
            size="sm"
            [arrowButtons]="false"
            [keyboardEmit]="true"
            [min]="0"
            [value]="customHeight()"
            (valueChange)="setHeight($event)"
            (submit)="addCustomSize()"></ui-number-input>
    </div>

    <ui-button
        id="add-button"
        [type]="isNewUI() ? 'solid-primary' : 'discrete'"
        icon="plus-skinny-minimal"
        text="Add"
        size="sm"
        [border]="false"
        [disabled]="!customWidth() || !customHeight()"
        (click)="addCustomSize()"></ui-button>
</div>
