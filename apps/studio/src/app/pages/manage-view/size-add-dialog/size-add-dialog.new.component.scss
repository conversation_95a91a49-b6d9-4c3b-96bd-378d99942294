$sidePanelWidth: 362px;
$buttonsHeight: 40px;
$tabHeight: 32px;

:where(:root[data-uinew]) :host {
    height: 100%;
    width: 100%;
    display: grid;
    grid-template-columns: $sidePanelWidth 1fr;
    grid-template-rows: 1fr $buttonsHeight;

    grid-template-areas:
        'side-panel sizes-content'
        'buttons buttons';

    row-gap: var(--nui-dialog-space-gap);

    size-add-collection-list {
        margin-top: calc(-1 * var(--nui-space-500));
    }

    ui-dropdown-divider.add-sizes-divider {
        padding: 0;
    }

    .add-sizes-container {
        display: flex;
        flex-direction: column;
        padding-top: var(--nui-space-500);
        gap: var(--nui-space-500);
        height: 100%;
        overflow-y: scroll;
    }

    .side-panel {
        padding: var(--nui-space-400) 0;

        border-radius: var(--nui-border-radius-medium);
        border: var(--nui-border-width-small) solid var(--nui-border-neutral-secondary-bold);
        background: var(--nui-surface-neutral-subtlest);

        ::ng-deep ui-tabs {
            .header {
                width: calc(100% - var(--nui-space-400) * 2);
                margin: 0 var(--nui-space-400);
                .tab {
                    width: 100%;
                }
            }

            .content {
                height: calc(100% - $tabHeight);
                .content {
                    height: 100%;
                }
            }
        }

        size-add-collection-list {
            overflow-y: scroll;
            overflow-x: hidden;
            height: 100%;
        }
    }

    ui-dropdown-divider {
        padding: var(--nui-space-500) 0;
    }

    ui-tabs {
        height: 100%;
    }
    .side-panel {
        grid-area: side-panel;
        overflow-y: auto;
    }

    .buttons {
        grid-area: buttons;
        display: flex;
        justify-content: space-between;
    }

    .psd-preview-container {
        grid-area: sizes-content;
        width: 100%;
        display: grid;
        grid-template-columns: 1px 1fr;
        grid-template-rows: 100%;
        overflow: hidden;

        padding-left: var(--nui-space-1200);

        psd-fix-font-panel {
            grid-column: 1;
            grid-row: 1;
            z-index: 2;
            margin-left: calc(-1 * var(--nui-space-800));
        }

        psd-import-preview {
            grid-column: 1 / 3;
            grid-row: 1;
            z-index: 1;
        }
    }
}
