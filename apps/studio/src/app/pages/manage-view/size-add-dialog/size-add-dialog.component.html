<div class="side-panel ui-scrollbar">
    @if (isNewUI()) {
        <ui-tabs size="sm">
            <ui-tab
                name="Add Sizes"
                headerId="add-sizes"
                #tab1
                (selectedChange)="selectTabNewUI($event, 0)">
                <div class="add-sizes-container">
                    <ng-container *ngTemplateOutlet="sizesContentTemplate" />
                </div>
            </ui-tab>
            <ui-tab
                name="Import PSD"
                headerId="import-psd"
                #tab2
                (selectedChange)="selectTabNewUI($event, 1)">
                <ng-container *ngTemplateOutlet="psdContentTemplate" />
            </ui-tab>
        </ui-tabs>
    } @else {
        <size-add-tabs (selectedTabChange)="selectTab($event)">
            <div tab1>
                <ng-container *ngTemplateOutlet="sizesContentTemplate" />
            </div>
            <div tab2>
                <ng-container *ngTemplateOutlet="psdContentTemplate" />
            </div>
        </size-add-tabs>
    }
</div>

<ng-template #sizesContentTemplate>
    <size-add-custom-input (customSizeAdded)="addSizeToCustomSizesCollection($event)" />
    @if (isNewUI()) {
        <ui-dropdown-divider class="add-sizes-divider" />
    }
    <size-add-collection-select />
    <size-add-collection-overview />
    <size-add-collection-list />
</ng-template>

<ng-template #psdContentTemplate>
    @if (selectedTab === Tabs.PSD_IMPORT) {
        @if (isNewUI()) {
            <ui-dropdown-divider />
        }
        <psd-list />
    }
</ng-template>

@switch (selectedTab) {
    @case (Tabs.SIZE_ADD) {
        <size-add-preview
            #sizeAddPreview
            [sizes]="addedSizes()"
            (onCancel)="close()" />

        @if (isNewUI()) {
            <div class="buttons">
                <ui-button
                    id="cancel-button"
                    text="Cancel"
                    type="solid-secondary"
                    (click)="close()" />
                <ui-button
                    id="add-sizes-button"
                    text="Add sizes ({{ sizeAddPreview.sizes.length }})"
                    type="primary"
                    [submit]="sizeAddPreview.onAddSizes"
                    [done]="sizeAddPreview.onAddSizesFinished"
                    [disabled]="!sizeAddPreview.sizes.length" />
            </div>
        }
    }
    @case (Tabs.PSD_IMPORT) {
        <div class="psd-preview-container">
            <psd-import-preview
                #psdImportPreview
                (onCancel)="close()" />
            <psd-fix-font-panel />
        </div>

        @if (isNewUI()) {
            <div class="buttons">
                <ui-button
                    id="cancel-button"
                    text="Cancel"
                    type="solid-secondary"
                    (click)="close()" />
                <ui-button
                    id="interaction-save-psd-creative"
                    text="Add size"
                    type="solid-primary"
                    data-test-id="save-psd-creative"
                    (click)="psdImportPreview.saveCreative()"
                    [loading]="psdImportPreview.isSaving"
                    [disabled]="psdImportPreview.isSaving || !psdImportPreview.creative" />
            </div>
        }
    }
}
