import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { SizeAddStateService } from '../state/size-add.service';
import { SelectedSizes } from '@studio/domain/state';
import { UICheckboxComponent, UINewThemeService } from '@bannerflow/ui';

@Component({
    imports: [UICheckboxComponent],
    selector: 'size-add-collection-overview',
    templateUrl: './size-add-collection-overview.component.html',
    styleUrls: [
        './size-add-collection-overview.component.scss',
        './size-add-collection-overview.new.component.scss'
    ]
})
export class SizeAddCollectionOverviewComponent {
    private uiNewThemeService = inject(UINewThemeService);
    private sizeAddStateService = inject(SizeAddStateService);

    hasSelectedCollections = computed(() => this.computeHasSelectedCollections());
    hasSelectedSizes = computed(() => this.computeHasSelectedSizes());
    isIndeterminate = computed(
        () => this.hasSelectedSizes() && Object.keys(this.selectedSizes()).length < this.sizes().length
    );

    collections = toSignal(this.sizeAddStateService.collections$, { initialValue: [] });
    sizes = toSignal(this.sizeAddStateService.sizes$, { initialValue: [] });

    private selectedCollections = toSignal(this.sizeAddStateService.selectedCollections$, {
        initialValue: []
    });
    private filteredSizes = toSignal(this.sizeAddStateService.filteredSizes$, { initialValue: [] });
    selectedSizes = toSignal(this.sizeAddStateService.selectedSizes$, {
        initialValue: {} as SelectedSizes
    });

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    selectAllSizes(): void {
        this.sizeAddStateService.selectAllSizes();
    }

    deselectAllSizes(): void {
        this.sizeAddStateService.deselectAllSizes();
    }

    toggleAllSizes(): void {
        if (this.hasSelectedSizes()) {
            this.sizeAddStateService.deselectAllSizes();
        } else {
            this.sizeAddStateService.selectAllSizes();
        }
    }

    private computeHasSelectedSizes(): boolean {
        const filteredSizes = this.filteredSizes();
        const selectedSizes = this.selectedSizes();

        return filteredSizes.some(({ id }) => selectedSizes[id]?.numberOfSizes);
    }

    private computeHasSelectedCollections(): boolean {
        const selectedCollections = this.selectedCollections();
        return !!selectedCollections.length;
    }
}
