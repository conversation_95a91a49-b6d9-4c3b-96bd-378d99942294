:where(:root:not([data-uinew])) :host {
    position: absolute;
    top: -24px; // parent has a padding of 40px
    left: 50%;
    transform: translateX(-50%);

    .wrapper {
        width: 280px;
        height: 48px;

        background: var(--ui-color-grey-96);
        border: 1px solid var(--ui-color-grey-84);
        border-radius: 2px;

        display: flex;
        justify-content: space-between;
        padding: 10px;

        align-items: center;

        a {
            color: var(--ui-color-primary);
            cursor: pointer;
        }
    }
}
