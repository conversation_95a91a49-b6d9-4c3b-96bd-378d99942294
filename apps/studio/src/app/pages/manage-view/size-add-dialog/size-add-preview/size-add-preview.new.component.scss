:where(:root[data-uinew]) :host {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: var(--nui-space-1000, 40px);
    min-width: 0;
    max-height: 100%;
    box-sizing: border-box;
    margin-top: 30px;

    .no-sizes-wrapper {
        text-align: center;

        p {
            color: var(--nui-icon-secondary);
        }
    }

    .section {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &.creative-preview {
            height: 100%;
            max-height: calc(100% - 10px);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            flex-wrap: wrap;

            .creative-wrapper {
                position: relative;

                display: flex;
                padding: var(--nui-space-400, 16px);
                flex-direction: column;
                align-items: center;
                gap: var(--nui-space-400, 16px);
                box-sizing: content-box;

                .creative-container {
                    height: inherit;
                    width: inherit;
                    animation: creative-bounce 0.75s ease;
                    animation-fill-mode: forwards;
                    box-sizing: content-box;

                    border-radius: var(--nui-border-radius-tiny, 2px);
                    border: 1px solid var(--nui-border-neutral-secondary-bold, #cdced7);
                    background: var(--nui-surface-neutral-subtlest, #fff);
                }

                .creative-size {
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    top: calc(-1 * var(--nui-space-400, 16px));
                }
            }
        }
    }
}
