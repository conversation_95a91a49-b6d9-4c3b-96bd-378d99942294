<div
    #creativePreviewWrapper
    class="section creative-preview ui-scrollbar">
    @if (sizes.length) {
        @for (size of sizes; track size.id) {
            <div
                class="creative-wrapper"
                [ngStyle]="{
                    width: size.width * (zoomValue / 100) + 'px',
                    height: size.height * (zoomValue / 100) + 'px'
                }">
                @if (isNewUI()) {
                    <ui-label
                        class="creative-size"
                        size="sm">
                        {{ size.width }} × {{ size.height }}
                    </ui-label>
                } @else {
                    <div class="creative-size">{{ size.width }} × {{ size.height }}</div>
                }
                @if (version$ | async; as version) {
                    <div class="creative-container">
                        <studio-creative
                            [version]="version"
                            [size]="size"
                            [renderOnInit]="true" />
                    </div>
                }
            </div>
        }
    } @else {
        @if (isNewUI()) {
            <div class="no-sizes-wrapper">
                <h1
                    [nuiTypography]="'nui-title'"
                    [uiSize]="'lg'">
                    Get started
                </h1>
                <p
                    [nuiTypography]="'nui-body'"
                    [uiSize]="'md'">
                    Select your creative sizes on the left!
                </p>
            </div>
        }
    }

    <size-add-tooltip />
</div>

@if (!isNewUI()) {
    <ui-dialog-buttons align="right">
        <ui-button
            id="cancel-button"
            text="Cancel"
            (click)="cancel()" />
        <ui-button
            id="add-sizes-button"
            text="Add sizes ({{ sizes.length }})"
            type="primary"
            [submit]="onAddSizes"
            [done]="onAddSizesFinished"
            [disabled]="!sizes.length" />
    </ui-dialog-buttons>
}
