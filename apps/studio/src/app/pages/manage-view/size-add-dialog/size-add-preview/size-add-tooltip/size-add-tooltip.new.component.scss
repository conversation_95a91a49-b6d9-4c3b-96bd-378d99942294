:where(:root[data-uinew]) :host {
    position: absolute;
    top: -70px;
    left: 50%;
    transform: translateX(-50%);

    .wrapper {
        display: flex;
        width: 400px;
        padding: var(--nui-space-200, 8px) var(--nui-space-200, 8px) var(--nui-space-200, 8px)
            var(--nui-space-400, 16px);
        justify-content: space-between;
        align-items: center;

        border-radius: var(--nui-border-radius-small, 4px);
        border: 1px solid var(--nui-border-neutral-secondary-bold, #cdced7);
        background: var(--nui-fill-brand-secondary-subtlest, #f9f9fb);
    }
}
