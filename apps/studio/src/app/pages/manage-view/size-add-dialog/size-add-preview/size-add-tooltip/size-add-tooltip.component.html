@let _numberOfSelectedSizes = numberOfSelectedSizes();

@if (_numberOfSelectedSizes) {
    <div class="wrapper">
        @if (isNewUI()) {
            <p [uiSize]="'sm'">
                @if (_numberOfSelectedSizes === 1) {
                    1 size selected
                } @else {
                    {{ _numberOfSelectedSizes }} sizes selected
                }
            </p>
            <ui-button
                size="xs"
                [type]="'ghost-primary'"
                text="Clear selection"
                (click)="clearSelection()"></ui-button>
        } @else {
            <span>
                @if (_numberOfSelectedSizes === 1) {
                    1 size selected
                } @else {
                    {{ _numberOfSelectedSizes }} sizes selected
                }
            </span>
            <a
                class="action"
                (click)="clearSelection()">
                Clear the selection
            </a>
        }
    </div>
}
