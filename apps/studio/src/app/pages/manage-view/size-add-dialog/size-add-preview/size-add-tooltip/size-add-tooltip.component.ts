import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { SelectedSizes } from '@studio/domain/state';
import { SizeAddStateService } from '../../state/size-add.service';
import { UIButtonComponent, UIComponentSizeDirective, UINewThemeService } from '@bannerflow/ui';

@Component({
    imports: [UIButtonComponent, UIComponentSizeDirective],
    selector: 'size-add-tooltip',
    templateUrl: './size-add-tooltip.component.html',
    styleUrls: ['./size-add-tooltip.component.scss', './size-add-tooltip.new.component.scss']
})
export class SizeAddTooltipComponent {
    private uiNewThemeService = inject(UINewThemeService);
    private sizeAddStateService = inject(SizeAddStateService);
    private selectedSizes = toSignal(this.sizeAddStateService.selectedSizes$, {
        initialValue: {} as SelectedSizes
    });

    numberOfSelectedSizes = computed(() => this.computeNumberOfSelectedSizes());

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    clearSelection(): void {
        this.sizeAddStateService.clearSelection();
    }

    private computeNumberOfSelectedSizes(): number {
        const selectedSizes = this.selectedSizes();

        const count = Object.values(selectedSizes).reduce(
            (acc, { numberOfSizes }) => acc + numberOfSizes,
            0
        );

        return count;
    }
}
