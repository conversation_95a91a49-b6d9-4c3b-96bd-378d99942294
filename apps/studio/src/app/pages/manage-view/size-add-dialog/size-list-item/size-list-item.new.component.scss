:where(:root[data-uinew] :host) {
    cursor: pointer;
    .compatibility__icon {
        fill: var(--nui-icon-disabled);
    }
    .size {
        display: flex;
        padding: var(--nui-space-200, 8px) var(--nui-space-400, 16px);
        align-items: center;
        gap: var(--nui-space-400, 16px);
        align-self: stretch;
        justify-content: space-between;

        &:hover {
            background: var(--nui-fill-brand-primary-subtlest, #e9f3ff);
        }

        .thumbnail__icon {
            fill: var(--nui-icon-disabled);
        }

        &__wrapper {
            display: flex;
            align-items: center;
            gap: var(--nui-space-400, 16px);
            align-self: stretch;
            justify-content: space-between;
        }

        .thumbnail {
            display: flex;
        }

        .value {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--nui-space-200);

            .size-value {
                display: flex;
                flex-direction: column;
                gap: var(--nui-space-100);
            }
        }

        .add__multiple {
            display: flex;
            gap: var(--nui-space-200);
            visibility: hidden;
        }

        &.added {
            background: var(--nui-fill-brand-secondary-subtler);
            .add__multiple {
                visibility: visible;
            }

            &:hover {
                background: var(--nui-fill-brand-primary-subtlest, #e9f3ff);
            }
        }

        &.is-social {
            .compatibility {
                display: flex;
                align-items: flex-start;
                align-content: flex-start;
                gap: 4px var(--nui-space-100, 4px);
                align-self: stretch;
                flex-wrap: wrap;
            }
        }
    }
}
