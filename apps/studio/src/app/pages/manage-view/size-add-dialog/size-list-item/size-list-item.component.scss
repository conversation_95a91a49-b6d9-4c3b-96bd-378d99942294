:where(:root:not([data-uinew]) :host) {
    .size {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 34px;
        cursor: pointer;
        background: var(--studio-color-surface-second);
        padding-left: 5px;
        padding-right: 8px;

        &:hover {
            background: var(--studio-color-grey-93);

            .add {
                &__checkbox {
                    display: block;
                }

                &__multiple {
                    display: flex;
                }
            }

            .thumbnail {
                &__icon {
                    --color1: var(--studio-color-second);
                }
            }
        }

        &__wrapper {
            display: flex;
        }

        .thumbnail {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 30px;
            height: 30px;
            min-width: 1px;
            min-height: 1px;
            margin: 0 20px;

            &__icon {
                --color1: var(--studio-color-third);
                --font-size: 3.4rem;
            }
        }

        .value {
            display: flex;
            justify-content: center;
            align-items: center;

            .size-value {
                min-width: 60px;
            }

            .size-name {
                color: var(--studio-color-text);
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                font-weight: bold;
                margin-left: 8px;
                max-width: 110px;
            }
        }

        &.add-multiple {
            .add {
                width: 50px;
            }
        }

        .add {
            width: 40px;
            text-align: center;

            &__checkbox,
            &__multiple {
                display: none;
            }

            &__multiple {
                justify-content: space-evenly;
                align-items: start;

                ui-svg-icon:hover {
                    --color: var(--studio-color-primary);
                }

                &__sub,
                &__add {
                    height: 20px;
                    width: 20px;
                }
            }
        }

        &.added {
            .add {
                &__checkbox {
                    display: block;
                }

                &__multiple {
                    display: flex;
                }
            }
        }

        &.is-social {
            height: auto;

            .compatibility {
                display: block;
                margin-top: 3px;

                &__icon {
                    color: var(--studio-color-text-third);
                    font-size: 13px;
                    margin-right: 7px;
                }
            }
        }
    }
}
