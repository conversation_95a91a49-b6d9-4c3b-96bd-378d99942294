@let _collectionName = collectionName();
@let _size = size();
@let _numberOfSelected = numberOfSelected();

<div
    id="{{ _collectionName | slug }}-sizes-{{ _size.width }}x{{ _size.height }}"
    class="size add-multiple"
    [class.added]="_numberOfSelected > 0"
    [class.is-social]="!!_size.compatibility?.length"
    (click)="addSize(_size)">
    <div class="size__wrapper">
        @if (isNewUI()) {
            <ui-checkbox
                (click)="toggleSize(_size); $event.stopPropagation()"
                [selected]="_numberOfSelected > 0">
            </ui-checkbox>
        }
        <div class="thumbnail">
            @if (_size.thumbnail) {
                <ui-svg-icon
                    class="thumbnail__icon"
                    [icon]="_size.thumbnail"></ui-svg-icon>
            } @else {
                <size-thumbnail [size]="_size"></size-thumbnail>
            }
        </div>
        <div class="value">
            <div class="size-value">
                @if (isNewUI()) {
                    <ui-label size="sm">{{ _size.width }} × {{ _size.height }}</ui-label>
                } @else {
                    {{ _size.width }} × {{ _size.height }}
                }
                @if (_size.compatibility?.length) {
                    <div class="compatibility">
                        @for (icon of _size.compatibility; track $index) {
                            <ui-svg-icon
                                class="compatibility__icon"
                                size="xs"
                                [icon]="icon"
                                [nuiIcon]="icon" />
                        }
                    </div>
                }
            </div>
            @if (_size.name) {
                @if (isNewUI()) {
                    <ui-label
                        size="sm"
                        weight="bold"
                        >{{ _size.name }}</ui-label
                    >
                } @else {
                    <div
                        class="size-name"
                        [uiTooltip]="_size.name">
                        {{ _size.name }}
                    </div>
                }
            }
        </div>
    </div>
    <div class="add">
        <div class="add__multiple">
            <div
                class="add__multiple__sub"
                (click)="removeSize(_size); $event.stopPropagation()">
                @if (isNewUI()) {
                    <ui-button
                        size="sm"
                        [type]="'plain-primary'"
                        [nuiSvgIcon]="'remove'">
                    </ui-button>
                } @else {
                    <ui-svg-icon icon="minus-small"></ui-svg-icon>
                }
            </div>
            @if (isNewUI()) {
                <ui-label size="sm">{{ _numberOfSelected }}</ui-label>
            } @else {
                <div class="add__multiple__label">{{ _numberOfSelected }}</div>
            }
            <div
                class="add__multiple__add"
                (click)="addSize(_size); $event.stopPropagation()">
                @if (isNewUI()) {
                    <ui-button
                        size="sm"
                        [type]="'plain-primary'"
                        [nuiSvgIcon]="'add'">
                    </ui-button>
                } @else {
                    <ui-svg-icon icon="plus-tiny"></ui-svg-icon>
                }
            </div>
        </div>
    </div>
</div>
