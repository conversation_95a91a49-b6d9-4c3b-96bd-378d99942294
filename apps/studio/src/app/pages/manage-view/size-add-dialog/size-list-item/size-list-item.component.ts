import { Component, computed, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { UIModule, UINewThemeService } from '@bannerflow/ui';
import { ISelectableSize } from '@studio/domain/components/size-list.types';
import { SelectedSizes } from '@studio/domain/state';
import { SlugPipe } from '../../../../shared/pipes/slug.pipe';
import { SizeThumbnailComponent } from '../size-thumbnail/size-thumbnail.component';
import { SizeAddStateService } from '../state/size-add.service';

@Component({
    imports: [UIModule, SizeThumbnailComponent, SlugPipe],
    selector: 'size-list-item',
    templateUrl: './size-list-item.component.html',
    styleUrls: ['./size-list-item.component.scss', './size-list-item.new.component.scss']
})
export class SizeListItemComponent {
    private uiNewThemeService = inject(UINewThemeService);

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    private sizeAddStateService = inject(SizeAddStateService);

    size = input.required<ISelectableSize>();

    collectionName = computed(() => this.computeCollectionName());
    numberOfSelected = computed(() => this.computeNumberOfSelected());

    private selectedCollections = toSignal(this.sizeAddStateService.selectedCollections$, {
        initialValue: []
    });
    private selectedSizes = toSignal(this.sizeAddStateService.selectedSizes$, {
        initialValue: {} as SelectedSizes
    });

    addSize(size: ISelectableSize): void {
        this.sizeAddStateService.increaseSelectedSize(size);
    }

    removeSize(size: ISelectableSize): void {
        const delta = -1;
        this.sizeAddStateService.increaseSelectedSize(size, delta);
    }

    toggleSize(size: ISelectableSize): void {
        if (this.numberOfSelected() > 0) {
            const delta = -this.numberOfSelected();
            this.sizeAddStateService.increaseSelectedSize(size, delta);
        } else {
            this.sizeAddStateService.increaseSelectedSize(size);
        }
    }

    private computeNumberOfSelected(): number {
        const selectedSizes = this.selectedSizes();
        const sizeId = this.size().id;

        return selectedSizes?.[sizeId]?.numberOfSizes ?? 0;
    }

    private computeCollectionName(): string {
        const sizeId = this.size().id;
        const collections = this.selectedCollections();

        return (
            collections.find(({ sizeFormatIds }) => sizeFormatIds?.includes(sizeId))?.name ?? 'no-coll'
        );
    }
}
