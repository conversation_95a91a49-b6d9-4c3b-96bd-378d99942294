@let _inShowcaseMode = inShowcaseMode();
@let _shouldShowTranslationPanel = shouldShowTranslationPanel();
@let _shouldShowHorizontalSizeList = shouldShowHorizontalSizeList();
@let _shouldShowCreativesetHistoryPanel = shouldShowCreativesetHistoryPanel();
@let _hasSidePanel =
    _shouldShowTranslationPanel ||
    (isCreativesetHistoryEnabled() && _shouldShowCreativesetHistoryPanel);

<div
    class="manage-view-container"
    uiSize="sm"
    [class.has-creativeset-history-panel]="_shouldShowCreativesetHistoryPanel"
    [ngClass]="{
        'no-side-panel': !_hasSidePanel,
        showcase: _inShowcaseMode
    }">
    @if (creativeset) {
        <manage-view-topbar [creativeset]="creativeset" />
    }

    @if (isNewUI()) {
        <div
            uiCard
            [size]="'sm'"
            class="sidebar"
            *media="Breakpoint.DesktopUp">
            <ng-container *ngTemplateOutlet="sidebarContent"></ng-container>
        </div>
        <div
            class="sidebar"
            *media="Breakpoint.DesktopDown">
            <ng-container *ngTemplateOutlet="sidebarContent"></ng-container>
        </div>
    } @else {
        <div class="sidebar">
            <ng-container *ngTemplateOutlet="sidebarContent"></ng-container>
        </div>
    }
    <ng-template #sidebarContent>
        <div
            *permissions="['Default', 'AddSize']"
            class="add">
            <ui-button
                id="add-design-button"
                icon="plus-skinny-minimal"
                text="Add size"
                [type]="isNewUI() ? 'solid-secondary' : 'primary'"
                size="sm"
                nuiSvgIcon="add"
                (click)="openAddSizeDialog()"></ui-button>
        </div>
        <filter-list
            *media="Breakpoint.DesktopUp"
            #filterList
            class="creative-filters ui-scrollbar"></filter-list>

        <div class="controls">
            <animation-control
                id="interaction-animation-control"
                class="animation-controls"></animation-control>
            <size-selector *media="Breakpoint.DesktopDown"></size-selector>
        </div>
        <ng-container *media="Breakpoint.DesktopDown">
            <ng-container *ngTemplateOutlet="versionPickerTemplate"></ng-container>
        </ng-container>
    </ng-template>

    <div
        #scrollView
        cdk-scrollable
        class="ui-scrollbar creative-list"
        [ngClass]="{ 'add-margin': _shouldShowHorizontalSizeList }"
        (mouseenter)="scrollViewMouseEnter()"
        (mouseleave)="scrollViewMouseLeave()"
        (click)="deselect($event)"
        (scroll)="creativeListScroll($event)">
        <mv-context-menu></mv-context-menu>

        @if (hasActiveFilters() && isViewEmpty()) {
            <div class="no-search-results">No creatives matching your filters</div>
        }

        <creative-list
            [creativeItemHeaderTemplate]="headerTemplate"
            [creativeItemFooterTemplate]="footerTemplate">
            <ng-template
                #headerTemplate
                let-creative
                let-creativeIndex="creativeIndex"
                let-isCreativeGroup="isCreativeGroup">
                <creative-list-item-select
                    [index]="creativeIndex"
                    [creative]="creative">
                </creative-list-item-select>
                <creative-list-item-title
                    #creativeListItemTitle
                    [index]="creativeIndex"
                    [isCreativeGroup]="isCreativeGroup"
                    [creative]="creative">
                </creative-list-item-title>
                @if (isNewUI() && creativeListItemTitle.isEditing()) {
                    <ui-button
                        type="ghost-secondary"
                        class="close-button"
                        nuiSvgIcon="close"
                        size="xs" />
                } @else {
                    <creative-list-item-menu [creative]="creative"></creative-list-item-menu>
                }
            </ng-template>
            <ng-template
                #footerTemplate
                let-creative
                let-scale="scale">
                @if (!_inShowcaseMode) {
                    <lib-heavy-asset-indicator
                        class="footer__item"
                        [creative]="creative" />
                }

                <creative-list-item-weight
                    class="footer__item"
                    [creative]="creative">
                </creative-list-item-weight>

                <creative-list-item-preview
                    class="footer__item"
                    [scale]="scale"
                    [creative]="creative">
                </creative-list-item-preview>
            </ng-template>
        </creative-list>
    </div>
    @if (_shouldShowTranslationPanel && !_shouldShowCreativesetHistoryPanel) {
        <ng-container *permissions="'TranslateVersions'">
            <ng-container *media="Breakpoint.DesktopUp">
                <div
                    class="side-panel ui-scrollbar deselect-trigger"
                    data-test-id="translation-panel">
                    <translation-panel></translation-panel>
                </div>
            </ng-container>
        </ng-container>
    }
    @if (_shouldShowCreativesetHistoryPanel) {
        <ng-container *media="Breakpoint.MobileUp">
            <div
                class="side-panel creativeset-history-panel ui-scrollbar deselect-trigger"
                data-test-id="creativeset-history-panel">
                <creativeset-history-panel></creativeset-history-panel>
            </div>
        </ng-container>
    }
</div>

<ng-template #versionPickerTemplate>
    <size-picker *media="Breakpoint.DesktopUp" />
    <version-picker
        class="versions"
        [allowManageVersions]="true"
        [showAllVersions]="true">
    </version-picker>
</ng-template>
<studio-devtools [manageView]="this"></studio-devtools>
