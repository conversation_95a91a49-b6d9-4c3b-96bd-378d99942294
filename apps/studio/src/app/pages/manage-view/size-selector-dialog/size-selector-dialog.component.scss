@use 'mixins' as *;
@use 'variables' as *;

.size-selector-dialog {
    font-size: 12px;
    display: flex;
    flex-direction: column;
    height: 100%;

    & .size-selector-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 13px;
        background-color: var(--ui-color-surface-second);
        border-bottom: 1px solid var(--ui-color-grey-93);

        &.right {
            justify-content: flex-end;
        }

        & ui-svg-icon {
            --font-size: 1.4rem;
            color: var(--ui-static-color-black);

            @include media-breakpoint-down(desktop) {
                --font-size: #{$mobile-icon-size};
            }
        }

        & .header-filter {
            display: flex;
            align-items: center;

            & ui-svg-icon {
                color: var(--ui-color-text-second);
            }

            & span {
                color: var(--ui-color-text-discrete);
                font-size: 12px;
                padding: 0 5px;

                @include mobile-text;
            }
        }
    }
}

::ng-deep {
    @include media-breakpoint-down(desktop) {
        & .content {
            height: 100% !important;
        }

        & :not(.new-ui).size-selector-dialog {
            // & .sizes {
            //     & .header {
            //         padding: 14px !important;

            //         ui-svg-icon.collapse {
            //             display: none;
            //         }
            //     }

            //     & .size-section {
            //         padding: 0 14px;
            //         border-bottom: 1px solid transparent !important;

            //         & .size {
            //             height: 46px !important;
            //         }
            //     }
            // }
        }
    }
}
