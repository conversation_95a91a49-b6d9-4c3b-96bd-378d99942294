import { BreakpointObserver } from '@angular/cdk/layout';
import { Component, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UIDialogComponent, UIModule, UINewThemeService, UiCardDirective } from '@bannerflow/ui';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { Breakpoint, getQueryFromBreakpoint } from '@studio/utils/breakpoints';

import { FilterListComponent } from '../filter-list';

@Component({
    imports: [UIModule, FilterListComponent, UiCardDirective],
    selector: 'size-selector-dialog',
    templateUrl: './size-selector-dialog.component.html',
    styleUrls: ['./size-selector-dialog.component.scss']
})
export class SizeSelectorDialogComponent implements OnInit, OnDestroy {
    private breakpointObserver = inject(BreakpointObserver);
    private dialog = inject(UIDialogComponent);
    private unsubscribe$ = new Subject<void>();
    private uiNewThemeService = inject(UINewThemeService);

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    ngOnInit(): void {
        this.breakpointObserver
            .observe(getQueryFromBreakpoint(Breakpoint.DesktopUp))
            .pipe(
                filter(matchBreakpoint => matchBreakpoint.matches),
                takeUntil(this.unsubscribe$)
            )
            .subscribe(() => {
                this.dialog.close();
            });
    }

    closeDialog(): void {
        this.dialog.close();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }
}
