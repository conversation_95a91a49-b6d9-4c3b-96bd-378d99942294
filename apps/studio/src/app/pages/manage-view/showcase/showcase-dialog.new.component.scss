:where(:root[data-uinew]) :host {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 100%;

    --tabs-height: 40px;

    .container {
        width: 100%;
        height: 100%;

        .create-new-link-content,
        .manage-access-content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            padding-top: var(--nui-space-500);
        }

        .manage-access-content {
            justify-content: start;
        }

        ui-dialog-buttons {
            padding-top: 0;
            margin-top: 1.6rem;
            justify-content: flex-start;

            ui-button {
                width: 186px;
            }
        }

        .create-wrapper {
            display: flex;
            justify-content: space-between;
            gap: var(--nui-space-800);

            .inputs {
                display: flex;
                flex-direction: column;
                gap: var(--nui-space-300);
                flex-grow: 1;

                .invite-wrapper {
                    display: flex;
                    gap: var(--nui-space-200);

                    ui-select {
                        width: 100%;
                        max-width: 100%;
                    }

                    ui-button {
                        margin-right: var(--nui-space-300);
                    }
                }
            }

            .options-wrapper {
                display: flex;
                width: 414px;
                flex-direction: column;
                gap: var(--nui-space-500, 20px);

                .options {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: var(--nui-space-300, 12px);

                    ui-select {
                        width: 100%;
                    }
                }
            }
        }

        .versions {
            width: 100%;
        }

        .version-name {
            display: inline-flex;
            justify-content: space-between;
            width: calc(100% - var(--nui-icon-width));
        }

        .share-wrapper {
            display: flex;
            padding: var(--nui-space-400);
            justify-content: space-between;
            align-items: center;
            align-self: stretch;

            border-radius: var(--nui-border-radius-medium, 8px);
            border: 1px solid var(--nui-border-neutral-secondary-boldest, #80828d);

            .share-text-wrapper {
                flex-grow: 1;
                flex-direction: column;
                gap: var(--nui-space-200);

                .share-text {
                    color: var(--nui-text-secondary, #62636c);

                    font-size: var(--nui-body-regular-font-size, 14px);
                    font-weight: var(--nui-body-regular-font-weight, 400);
                    line-height: var(--nui-body-regular-line-height, 21px);
                    letter-spacing: var(--nui-body-regular-letter-spacing, 0.18px);
                }
            }
        }

        .permissions-wrapper,
        .permissions {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: var(--nui-space-300, 12px);
            align-self: stretch;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: var(--nui-toggle-space-gap, 8px);
            align-self: stretch;

            ui-label {
                flex: 1 0 0;
            }
        }

        .ui-loader {
            --background-color: transparent !important;
        }

        .link {
            display: flex;
            align-items: center;
            gap: var(--nui-space-200);
            width: 100%;

            .linkRef {
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .versions,
    .sizes,
    .users,
    .translations,
    .styling,
    .statuses,
    .fullAccess,
    .comments {
        display: flex;
    }
}

.name-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
