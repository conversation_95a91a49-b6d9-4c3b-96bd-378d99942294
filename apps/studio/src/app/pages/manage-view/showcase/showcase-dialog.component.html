@if (loading$ | async) {
    <ui-loader></ui-loader>
} @else {
    <div class="container">
        <ui-tabs
            #tabs
            [class.hide-tabs]="showcaseList && showcaseList.data.length === 0">
            <ui-tab
                #createLinkTab
                headerId="create-link-tab"
                name="Create new link">
                @if (isNewUI()) {
                    <div class="create-new-link-content">
                        <ng-container *ngTemplateOutlet="createNewLinkContent"></ng-container>
                    </div>
                } @else {
                    <ng-container *ngTemplateOutlet="createNewLinkContent"></ng-container>
                }
                <ng-template #createNewLinkContent>
                    <div class="create-wrapper">
                        <div
                            class="inputs"
                            ui-theme="default">
                            @if (isNewUI()) {
                                <ui-label weight="bold">Share with team members</ui-label>
                            } @else {
                                <h3>Share with team members</h3>
                            }
                            <div class="invite-wrapper">
                                <ui-select
                                    [multiSelect]="true"
                                    [searchable]="true"
                                    [tokenField]="true"
                                    [showMultiselectIcon]="false"
                                    chipType="secondary"
                                    placeholder="Invite people"
                                    [(selected)]="selectedRecipients"
                                    theme="default">
                                    @for (user of sortedUsers; track user.id) {
                                        <ui-option [value]="user">
                                            @if (isNewUI()) {
                                                <div class="name-wrapper">
                                                    {{ user.name }}
                                                    <div class="flags">
                                                        @for (
                                                            localization of user.localizations;
                                                            track localization.id
                                                        ) {
                                                            <version-flag
                                                                [localizationId]="
                                                                    localization.id
                                                                "></version-flag>
                                                        }
                                                    </div>
                                                </div>
                                            } @else {
                                                {{ user.name }}
                                                <div class="flags">
                                                    @for (
                                                        localization of user.localizations;
                                                        track localization.id
                                                    ) {
                                                        <version-flag
                                                            [localizationId]="
                                                                localization.id
                                                            "></version-flag>
                                                    }
                                                </div>
                                            }
                                        </ui-option>
                                    }
                                </ui-select>
                                <ui-button
                                    text="Send link"
                                    type="primary"
                                    (click)="createLinkWithRecipients()"
                                    [disabled]="!(selectedRecipients.length > 0)">
                                </ui-button>
                            </div>
                            <ui-textarea
                                class="note"
                                placeholder="Add a note"
                                [autosize]="false"
                                [(value)]="invitationMessage">
                            </ui-textarea>
                        </div>
                        <div
                            class="options-wrapper"
                            ui-theme="small">
                            <div class="options">
                                @if (isNewUI()) {
                                    <ui-label weight="bold">Sharing options</ui-label>
                                } @else {
                                    <h3>Sharing options</h3>
                                }

                                <ui-select
                                    type="primary"
                                    class="option"
                                    placeholder="All versions"
                                    theme="small"
                                    [multiSelect]="true"
                                    [searchable]="true"
                                    [useTargetWidth]="true"
                                    [width]="'100%'"
                                    (selectedChange)="sortUsers(); resetLink()"
                                    [(selected)]="selectedVersions">
                                    @for (version of versions; track version.id) {
                                        @if (isNewUI()) {
                                            <ui-option
                                                [value]="version"
                                                [nuiFlag]="
                                                    version.localization.id | localizationIdToFlag
                                                ">
                                                <span class="version-name">
                                                    {{ version.name }}
                                                    @if (version.id === (defaultVersion$ | async)?.id) {
                                                        <span>Default</span>
                                                    }
                                                </span>
                                            </ui-option>
                                        } @else {
                                            <ui-option [value]="version">
                                                <version-flag
                                                    [title]="version.name"
                                                    [showName]="true"
                                                    [localizationId]="
                                                        version.localization.id
                                                    "></version-flag>
                                                <span>
                                                    @if (version.id === (defaultVersion$ | async)?.id) {
                                                        <span>Default</span>
                                                    }
                                                </span>
                                            </ui-option>
                                        }
                                    }
                                </ui-select>
                                <ui-select
                                    class="option sizes"
                                    placeholder="All sizes"
                                    theme="small"
                                    [multiSelect]="true"
                                    [searchable]="true"
                                    [useTargetWidth]="true"
                                    [showMultiselectIcon]="true"
                                    [width]="'100%'"
                                    (selectedChange)="resetLink()"
                                    [(selected)]="selectedSizes">
                                    @for (size of sizes; track size.id) {
                                        <ui-option
                                            [value]="size"
                                            class="size-option">
                                            {{ size.width + ' × ' + size.height }}
                                            @if (size.name) {
                                                <span
                                                    class="size-name"
                                                    truncateSpan
                                                    [spanText]="size.name"></span>
                                            }
                                        </ui-option>
                                    }
                                </ui-select>
                            </div>
                            <div class="permissions-wrapper">
                                @if (isNewUI()) {
                                    <ui-label weight="bold">Permissions</ui-label>
                                } @else {
                                    <h3>Permissions</h3>
                                }
                                <div class="permissions">
                                    <div
                                        class="permission-item"
                                        [class.toggleOff]="!allowFullAccess">
                                        @if (isNewUI()) {
                                            <ui-label
                                                leadingIcon="crown"
                                                trailingIcon="help"
                                                weight="bold"
                                                >Allow full access</ui-label
                                            >
                                        } @else {
                                            <div>
                                                <ui-svg-icon
                                                    icon="crown"
                                                    class="icon"></ui-svg-icon>
                                                <label>Allow full access</label>
                                                <ui-svg-icon
                                                    [uiTooltip]="
                                                        'Please note that only logged-in users within your organization will get the full access'
                                                    "
                                                    icon="question-mark"
                                                    class="info">
                                                </ui-svg-icon>
                                            </div>
                                        }

                                        <ui-toggle-switch
                                            [(selected)]="allowFullAccess"
                                            id="interaction-toggle-full-access"
                                            (selectedChange)="sortUsers(); resetLink()">
                                        </ui-toggle-switch>
                                    </div>

                                    <div
                                        class="permission-item"
                                        [class.toggleOff]="!allowTranslations"
                                        [class.disabled]="allowFullAccess">
                                        @if (isNewUI()) {
                                            <ui-label
                                                leadingIcon="edit"
                                                weight="bold"
                                                [type]="allowFullAccess ? 'disabled' : 'primary'"
                                                >Allow text editing</ui-label
                                            >
                                        } @else {
                                            <div>
                                                <ui-svg-icon
                                                    icon="edit"
                                                    class="icon"></ui-svg-icon>
                                                <label>Allow text editing</label>
                                            </div>
                                        }
                                        <ui-toggle-switch
                                            [disabled]="allowFullAccess"
                                            [selected]="allowTranslations"
                                            id="interaction-toggle-translations"
                                            (selectedChange)="allowTextEditingChange($event)">
                                        </ui-toggle-switch>
                                    </div>
                                    <div
                                        class="permission-item"
                                        [class.toggleOff]="!allowStyling"
                                        [class.disabled]="!allowTranslations || allowFullAccess">
                                        @if (isNewUI()) {
                                            <ui-label
                                                leadingIcon="format_size"
                                                weight="bold"
                                                [type]="
                                                    !allowTranslations || allowFullAccess
                                                        ? 'disabled'
                                                        : 'primary'
                                                "
                                                >Allow preset-styling changes</ui-label
                                            >
                                        } @else {
                                            <div>
                                                <ui-svg-icon
                                                    icon="text-style-apply-formatting"
                                                    class="icon"></ui-svg-icon>
                                                <label>Allow preset-styling changes</label>
                                            </div>
                                        }
                                        <ui-toggle-switch
                                            [disabled]="!allowTranslations || allowFullAccess"
                                            [selected]="allowStyling"
                                            id="interaction-toggle-styling"
                                            (selectedChange)="allowStylingChange($event)">
                                        </ui-toggle-switch>
                                    </div>
                                    <div
                                        class="permission-item"
                                        [class.toggleOff]="!allowComments"
                                        [class.disabled]="allowFullAccess">
                                        @if (isNewUI()) {
                                            <ui-label
                                                leadingIcon="chat_bubble"
                                                weight="bold"
                                                [type]="allowFullAccess ? 'disabled' : 'primary'"
                                                >Allow commenting</ui-label
                                            >
                                        } @else {
                                            <div>
                                                <ui-svg-icon
                                                    icon="comments"
                                                    class="icon"></ui-svg-icon>
                                                <label>Allow commenting</label>
                                            </div>
                                        }
                                        <ui-toggle-switch
                                            [disabled]="allowFullAccess"
                                            [(selected)]="allowComments"
                                            id="interaction-toggle-comments"
                                            (selectedChange)="resetLink()">
                                        </ui-toggle-switch>
                                    </div>
                                    <div
                                        class="permission-item"
                                        [class.toggleOff]="!allowStatuses"
                                        [class.disabled]="allowFullAccess">
                                        @if (isNewUI()) {
                                            <ui-label
                                                leadingIcon="check_circle"
                                                weight="bold"
                                                [type]="allowFullAccess ? 'disabled' : 'primary'"
                                                >Allow to change status</ui-label
                                            >
                                        } @else {
                                            <div>
                                                <ui-icon
                                                    icon="checkmark"
                                                    class="icon"></ui-icon>
                                                <label>Allow to change status</label>
                                            </div>
                                        }
                                        <ui-toggle-switch
                                            [disabled]="allowFullAccess"
                                            [(selected)]="allowStatuses"
                                            id="interaction-toggle-status"
                                            (selectedChange)="resetLink()">
                                        </ui-toggle-switch>
                                    </div>
                                    @if (allowExportingFromShowcaseFFEnabled()) {
                                        <div
                                            class="permission-item"
                                            [class.toggleOff]="!allowExport"
                                            [class.disabled]="allowFullAccess">
                                            @if (isNewUI()) {
                                                <ui-label
                                                    leadingIcon="output"
                                                    weight="bold"
                                                    [type]="allowFullAccess ? 'disabled' : 'primary'"
                                                    >Allow export creatives</ui-label
                                                >
                                            } @else {
                                                <div>
                                                    <ui-svg-icon
                                                        icon="export"
                                                        class="icon"></ui-svg-icon>
                                                    <label>Allow export creatives</label>
                                                </div>
                                            }
                                            <ui-toggle-switch
                                                [disabled]="allowFullAccess"
                                                [(selected)]="allowExport"
                                                id="interaction-toggle-export"
                                                (selectedChange)="resetLink()">
                                            </ui-toggle-switch>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        class="share-wrapper"
                        ui-theme="default">
                        @if ({ createdShowcase: createdShowcase$ | async }; as data) {
                            <div class="share-text-wrapper">
                                @if (isNewUI()) {
                                    <ui-label weight="bold">Share a link externally</ui-label>
                                } @else {
                                    <h3>Share a link externally</h3>
                                }
                                <span class="share-text"
                                    >Share your creative set with external stakeholders, reviewers,
                                    copywriters or translators</span
                                >
                            </div>
                            @if (
                                !allowFullAccess &&
                                (!data.createdShowcase || data.createdShowcase.isTemporary)
                            ) {
                                <ui-button
                                    id="create-showcase-link"
                                    text="Create link"
                                    [type]="isNewUI() ? 'ghost-primary' : 'default'"
                                    [svgIcon]="'decision-tree'"
                                    [nuiSvgIcon]="'add_link'"
                                    (click)="createLink()">
                                </ui-button>
                            }
                            @if (
                                !allowFullAccess &&
                                data.createdShowcase &&
                                !data.createdShowcase.isTemporary
                            ) {
                                <ui-button
                                    text="Copy link"
                                    [type]="isNewUI() ? 'ghost-primary' : 'default'"
                                    [svgIcon]="'copy'"
                                    [nuiSvgIcon]="'file_copy'"
                                    (click)="onCopyLink(data.createdShowcase.showcaseLink)">
                                </ui-button>
                            }
                            @if (allowFullAccess) {
                                <ui-button
                                    text="Manage users"
                                    class="manage-users-btn"
                                    [type]="isNewUI() ? 'ghost-secondary' : 'default'"
                                    [svgIcon]="'users'"
                                    [nuiSvgIcon]="'manage_accounts'"
                                    (click)="goToManageUsersView()">
                                </ui-button>
                                <ui-button
                                    text="Copy link"
                                    [type]="isNewUI() ? 'ghost-primary' : 'default'"
                                    [svgIcon]="'copy'"
                                    [nuiSvgIcon]="'file_copy'"
                                    (click)="onCopyMVLink()">
                                </ui-button>
                            }
                        }
                    </div>
                </ng-template>
            </ui-tab>
            <ui-tab
                name="Manage access"
                ui-theme="tiny"
                headerId="manage-access-tab"
                (selectedChange)="selectedTabChanged()">
                @if (isNewUI()) {
                    <div class="manage-access-content">
                        <ng-container *ngTemplateOutlet="manageAccessContent"></ng-container>
                    </div>
                } @else {
                    <ng-container *ngTemplateOutlet="manageAccessContent"></ng-container>
                }

                <ng-template #manageAccessContent>
                    @if (isNewUI()) {
                        <ui-label weight="bold">Manage your shared links</ui-label>
                    } @else {
                        <h3 class="table-text">Manage your shared links</h3>
                    }
                    @if (showcaseList && showcaseList.data.length > 0) {
                        <ui-list
                            [dataSource]="showcaseList"
                            columnHeight="34px"
                            [expandable]="false"
                            #linkList
                            [multiSelect]="false">
                            <ui-list-column
                                name="Link"
                                property="disabled"
                                width="30%">
                                <ng-template
                                    let-data="data"
                                    let-value="value"
                                    ui-list-cell-template>
                                    <div
                                        class="link"
                                        ui-theme="tiny">
                                        <ui-toggle-switch
                                            size="sm"
                                            [selected]="!data.disabled"
                                            (selectedChange)="toggleEnabled($event, data.showcaseKey)">
                                        </ui-toggle-switch>
                                        @if (!data.disabled) {
                                            <ui-svg-icon
                                                [icon]="isNewUI() ? 'none' : 'link-s'"
                                                nuiIcon="file_copy"
                                                (click)="copyLink(data.showcaseLink)"></ui-svg-icon>
                                        }
                                        @if (data.disabled) {
                                            <ui-svg-icon
                                                [icon]="isNewUI() ? 'none' : 'delete'"
                                                nuiIcon="delete"
                                                (click)="deleteShowcase(data.showcaseKey)">
                                            </ui-svg-icon>
                                        }
                                        <a
                                            [href]="data.showcaseLink"
                                            class="linkRef ui-link"
                                            target="_blank"
                                            (click)="goToLink(data.showcaseLink)"
                                            >{{ data.showcaseLink }}</a
                                        >
                                    </div>
                                </ng-template>
                            </ui-list-column>
                            <ui-list-column
                                name="Invited people"
                                property="invitations"
                                width="20%">
                                <ng-template
                                    let-value="value"
                                    let-data="data"
                                    ui-list-cell-template>
                                    <div class="users">
                                        @if (value.length === 0) {
                                            External access
                                        }
                                        @if (value.length > 0) {
                                            <span class="invitations">
                                                {{ value[0].name }}
                                            </span>
                                        }
                                    </div>
                                </ng-template>
                            </ui-list-column>
                            <ui-list-column
                                name="Versions"
                                property="versions"
                                width="15%">
                                <ng-template
                                    let-value="value"
                                    let-data="data"
                                    ui-list-cell-template>
                                    <div class="versions">
                                        @if (value.length === versions.length || value.length === 0) {
                                            All versions
                                        }
                                        @if (value.length !== versions.length) {
                                            <item-list-overflow>
                                                @for (version of value; track version.id) {
                                                    <overflow-item
                                                        class="version"
                                                        style="white-space: nowrap">
                                                        <version-flag
                                                            [localizationId]="version.localization.id"
                                                            [uiTooltipDelay]="0"
                                                            [uiTooltip]="version.name"></version-flag>
                                                        &nbsp;<span class="separator"
                                                            >&nbsp;&nbsp;&nbsp;</span
                                                        >
                                                    </overflow-item>
                                                }
                                            </item-list-overflow>
                                        }
                                    </div>
                                </ng-template>
                            </ui-list-column>
                            <ui-list-column
                                name="Sizes"
                                property="sizes"
                                [width]="isNewUI() ? '20%' : '25%'">
                                <ng-template
                                    let-value="value"
                                    let-data="data"
                                    ui-list-cell-template>
                                    <div class="sizes">
                                        @if (value.length === sizes.length || value.length === 0) {
                                            All sizes
                                        }
                                        @if (value.length !== sizes.length) {
                                            <item-list-overflow>
                                                @for (size of value; track size; let i = $index) {
                                                    <overflow-item
                                                        class="size"
                                                        style="white-space: nowrap">
                                                        {{
                                                            size.name ||
                                                                size.width + ' × ' + size.height
                                                        }}<span class="separator">,</span>
                                                    </overflow-item>
                                                }
                                            </item-list-overflow>
                                        }
                                    </div>
                                </ng-template>
                            </ui-list-column>
                            <ui-list-column
                                name="Allowed"
                                property="allowedOperations"
                                [width]="isNewUI() ? '15%' : '10%'">
                                <ng-template
                                    let-data="data"
                                    let-value="value"
                                    ui-list-cell-template>
                                    @if (!(value.comments || value.statuses || value.translations)) {
                                        View only
                                    }
                                    @if (value.fullAccess) {
                                        <div class="fullAccess">
                                            <ui-svg-icon
                                                [icon]="isNewUI() ? 'none' : 'crown'"
                                                nuiIcon="crown"
                                                size="sm"></ui-svg-icon>
                                        </div>
                                    }
                                    @if (
                                        !value.fullAccess &&
                                        (value.comments || value.statuses || value.translations)
                                    ) {
                                        @if (value.comments) {
                                            <div class="comments">
                                                <ui-svg-icon
                                                    [icon]="isNewUI() ? 'none' : 'comments'"
                                                    nuiIcon="chat_bubble"
                                                    size="sm"></ui-svg-icon>
                                            </div>
                                        }
                                        @if (value.statuses) {
                                            <div class="statuses">
                                                <ui-svg-icon
                                                    [icon]="
                                                        isNewUI()
                                                            ? 'none'
                                                            : 'checkbox-checkmark-default'
                                                    "
                                                    nuiIcon="check_circle"
                                                    size="sm"></ui-svg-icon>
                                            </div>
                                        }
                                        @if (value.translations) {
                                            <div class="translations">
                                                <ui-svg-icon
                                                    [icon]="isNewUI() ? 'none' : 'edit'"
                                                    nuiIcon="edit"
                                                    size="sm"></ui-svg-icon>
                                            </div>
                                        }
                                        @if (value.styling) {
                                            <div class="styling">
                                                <ui-svg-icon
                                                    [icon]="
                                                        isNewUI()
                                                            ? 'none'
                                                            : 'text-style-apply-formatting'
                                                    "
                                                    nuiIcon="format_size"
                                                    size="sm"></ui-svg-icon>
                                            </div>
                                        }
                                        @if (allowExportingFromShowcaseFFEnabled()) {
                                            @if (value.export) {
                                                <div class="export">
                                                    <ui-svg-icon
                                                        [icon]="isNewUI() ? 'none' : 'export'"
                                                        nuiIcon="output"
                                                        size="sm"></ui-svg-icon>
                                                </div>
                                            }
                                        }
                                    }
                                </ng-template>
                            </ui-list-column>
                        </ui-list>
                    }
                </ng-template>
            </ui-tab>
        </ui-tabs>
    </div>
}
