import { ChangeDetectorRef, Component, inject, input, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
    UIComponentSizeDirective,
    UIDropdownComponent,
    UIModule,
    UINewThemeService
} from '@bannerflow/ui';
import { ApprovalStatus, ICreative } from '@domain/creativeset/creative';
import { EditCreativeService } from '../services/edit-creative.service';
import { TileSelectService } from '../services/tile-select.service';

@Component({
    selector: 'status-dropdown',
    templateUrl: './status-dropdown.component.html',
    styleUrls: ['./status-dropdown.component.scss', './status-dropdown.new.component.scss'],
    imports: [UIModule],
    hostDirectives: [
        {
            directive: UIComponentSizeDirective,
            inputs: ['uiSize: size()']
        }
    ]
})
export class StatusDropdownComponent {
    private uiNewThemeService = inject(UINewThemeService);
    @ViewChild('menu', { static: true }) dropdown: UIDropdownComponent;
    ApprovalStatus = ApprovalStatus;
    size = input<'xs' | 'md'>('md');

    private creatives: ICreative[];
    private selectedStatuses: ApprovalStatus[];
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        private tileSelectService: TileSelectService,
        private editCreativeService: EditCreativeService
    ) {
        this.tileSelectService.selection$.pipe(takeUntilDestroyed()).subscribe(creatives => {
            this.creatives = creatives;
            this.selectedStatuses = this.creatives.map(creative => creative.approvalStatus);
        });
    }

    isHighlight(status: ApprovalStatus): boolean {
        return this.selectedStatuses.includes(status);
    }

    statusChange(status: ApprovalStatus): void {
        const creatives = this.tileSelectService.getSelected();
        if (creatives.length < 1) {
            return;
        }
        this.editCreativeService.updateApprovalStatus(status, creatives);
        this.dropdown.closePopover();
        this.changeDetectorRef.detectChanges();
    }
}
