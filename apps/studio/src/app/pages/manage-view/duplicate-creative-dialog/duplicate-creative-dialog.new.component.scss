$size-list-width: 342px;
$version-list-width: 246px;
$header-height: 60px;

:where(:root[data-uinew]) :host {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--nui-dialog-space-gap);
    height: 100%;

    .information-text {
        margin-top: var(--nui-dialog-space-gap-content);
        color: var(--nui-text-secondary);

        font-family: var(--nui-body-regular-font-family);
        font-size: var(--nui-body-regular-font-size);
        font-style: normal;
        font-weight: var(--nui-body-regular-font-weight);
        line-height: var(--nui-body-regular-line-height);
        letter-spacing: var(--nui-body-regular-letter-spacing);
    }

    .duplicate-creative-dialog {
        display: grid;
        grid-template-columns: $size-list-width 1fr;
        grid-template-rows: 100%;
        overflow: hidden;
        height: 100%;

        &:has(.versions) {
            grid-template-columns: $size-list-width $version-list-width 1fr;
        }

        .sizes,
        .versions {
            border-radius: var(--nui-border-radius-medium);
            border: 1px solid var(--nui-border-neutral-secondary-bold);
            background: var(--nui-surface-neutral-subtlest);
        }

        .versions {
            margin-left: var(--nui-space-400);
        }

        .target-list {
            margin-left: var(--nui-space-800);
        }

        .load {
            cursor: pointer;
        }
    }

    .action-info {
        display: flex;
        justify-content: end;
        align-items: center;
        width: 100%;
        gap: var(--nui-space-100);

        &.hidden {
            visibility: hidden;
        }

        b {
            cursor: default;
            font-weight: var(--nui-label-bold-font-weight);
        }
    }

    .action-buttons {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
}
