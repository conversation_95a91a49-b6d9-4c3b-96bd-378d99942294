import { inject, Injectable } from '@angular/core';
import { Logger } from '@bannerflow/sentinel-logger';
import { UIDialogService, UINotificationService, UINotificationType } from '@bannerflow/ui';
import { ICreative } from '@domain/creativeset/creative';
import { EditCreativeService } from '../services/edit-creative.service';
import { SizeAddService } from '../size-add-dialog/size-add.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { DuplicateCreativeDialogComponent } from './duplicate-creative-dialog.component';

@Injectable({
    providedIn: 'root'
})
export class DuplicateCreativeService {
    private editCreativeService = inject(EditCreativeService);
    private sizeAddService = inject(SizeAddService);
    private uiDialogService = inject(UIDialogService);
    private uiNotificationService = inject(UINotificationService);
    private versionsService = inject(VersionsService);

    private logger = new Logger('DuplicateCreativeService');

    openDialog(duplicateToNew: boolean): void {
        const headerText = duplicateToNew
            ? 'Duplicate creatives to new creative set'
            : 'Duplicate creatives to existing creative set';

        this.uiDialogService.openComponent(DuplicateCreativeDialogComponent, {
            headerText: headerText,
            panelClass: ['no-padding', 'fullscreen'],
            maxWidth: '100%',
            width: '100%',
            height: '100%',
            padding: 0,
            data: {
                duplicateToNew
            }
        });
    }

    async duplicateCreatives(creatives: ICreative[]): Promise<void> {
        await this.createSizes(creatives);
        let message = 'Creatives duplicated successfully';
        if (creatives.length === 1) {
            message = 'Creative duplicated successfully';
        }
        this.showNotification(message, 'info');
    }

    private async createSizes(creatives: ICreative[]): Promise<void> {
        try {
            this.logger.verbose('Creating new sizes');
            const sizes = creatives.map(creative => creative.size);
            if (!sizes.length) {
                return;
            }
            const { versions } = await this.sizeAddService.duplicateSizes(sizes);
            this.versionsService.onUpdateDesignsInCreativeset(versions);
            this.editCreativeService.updateView();
        } catch {
            this.showNotification(
                `There was an error when creating the creative size. Please try again. If the problem persists, please contact our support team for assistance. We apologize for any inconvenience.`,
                'error'
            );
        }
    }

    private showNotification(text: string, type: UINotificationType): void {
        this.uiNotificationService.open(text, {
            type,
            placement: 'top',
            autoCloseDelay: 5000
        });
    }
}
