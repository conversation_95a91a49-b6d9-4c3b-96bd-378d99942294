@use 'mixins' as *;
@use 'variables' as *;

$header-height: 5rem;
$sidebar-width: calc(200px + 16px);
$side-panel-width: 22rem;
$controls-height: 58px;

// :root[data-uinew] {
//     --app-height: 100%;
// }

:where(:root[data-uinew]) :host {
    width: 100%;

    & > ui-header {
        box-shadow: 0 1px 0px 0 var(--studio-color-border-second);
    }

    // ================== START SIDEBAR ==================
    .add {
        margin-bottom: var(--nui-space-600);

        ui-button {
            display: block;
            --width: 100%;
        }
    }

    .sidebar {
        width: 200px;
        grid-area: sidebar;
        margin: var(--nui-space-200);

        @include media-breakpoint-down(desktop) {
            display: flex;
            padding: var(--nui-space-200, 8px);
            justify-content: space-between;
            align-items: center;
            align-self: stretch;
            width: 100%;
            border: 1px solid var(--nui-border-neutral-subtle, #eff0f3);
            background: var(--nui-surface-neutral-subtlest, #fff);
            margin: 0;
        }
    }

    // ================== END SIDEBAR ==================

    .close-button {
        z-index: 4;
    }

    .no-search-results {
        font-size: 15px;
        display: grid;
        align-items: center;
        justify-items: center;
        width: 100%;
        height: 100%;
    }

    .creativeset-name {
        width: 100%;
        display: flex;
        white-space: nowrap;
        padding-left: 2rem;
        user-select: text;
        color: var(--studio-color-text);

        span {
            max-width: calc(100% - 25px);
        }

        @include media-breakpoint-down(desktop) {
            max-width: calc(100% - 50px);
            width: unset;
            padding: 0;
            margin-left: 5px;
            margin-right: 3rem;

            span {
                width: auto;
                max-width: 100%;
            }
        }
    }

    .help-menu {
        margin: 0 8px;
    }

    .manage-view-container {
        position: relative;
        height: 100%;
        display: grid;
        grid-template-columns: $sidebar-width 1fr $side-panel-width;
        grid-template-rows: $header-height 1fr;
        gap: 0 0;
        grid-template-areas:
            'header header header'
            'sidebar creatives side-panel';

        background-color: rgba(132, 150, 179, 0.2);

        &.no-side-panel {
            grid-template-areas:
                'header header header'
                'sidebar creatives creatives';

            @include media-breakpoint-down(desktop) {
                grid-template-areas:
                    'header'
                    'creatives'
                    'sidebar';
            }
        }

        &.showcase {
            .sidebar {
                grid-template-rows: 1fr $controls-height;
                grid-template-areas:
                    'filters'
                    'controls';

                .add {
                    display: none;
                }

                .creative-filters {
                    height: calc(var(--app-height) - $controls-height - $header-height);
                }
            }

            & .creative-filters {
                height: calc(var(--app-height) - 2 * $controls-height);
            }
        }

        @include media-breakpoint-down(desktop) {
            grid-template-columns: 1fr;
            grid-template-rows: calc($header-height + 6px) 1fr calc($header-height + 6px);

            grid-template-areas:
                'header'
                'creatives'
                'sidebar';

            .add {
                display: none;
            }
        }
    }

    .header {
        grid-area: header;

        .creativeset-name {
            @include mobile-text;
        }
    }

    .creative-list {
        grid-area: creatives;

        overflow-y: auto;
        overflow-x: hidden;

        &:has(.virtual-scroll-viewport) {
            overflow-y: hidden;
        }

        .size-header:not(:nth-child(2)) {
            border-top: 1px solid var(--studio-color-grey-92);
        }

        .size-header:nth-last-child(2) {
            &.collapsed {
                border-bottom: 1px solid var(--studio-color-grey-92);
            }
        }

        &.add-margin {
            margin-top: 50px;
        }
    }

    .side-panel {
        grid-area: side-panel;
        position: relative;

        background-color: var(--studio-color-white-off-light);
        border-left: 1px solid var(--studio-color-border-second);
        overflow-y: auto;
        padding-bottom: 10rem;
    }

    .creative-filters {
        grid-area: filters;
        display: flex;
        flex-direction: column;
        overflow-x: hidden;
        overflow-y: auto;
        height: calc(var(--app-height) - 2 * $controls-height - $header-height);
    }

    .controls {
        grid-area: controls;
        display: flex;

        @include media-breakpoint-down(desktop) {
            gap: var(--nui-space-200);
        }
    }

    .versions {
        @include media-breakpoint-down(desktop) {
            height: 100%;
            justify-self: end;
        }
    }

    .test-buttons {
        display: flex;
        margin-left: 10px;
        width: 35%;
    }

    .test-button {
        padding: 5px;
        background-color: var(--studio-color-grey-93);
        margin: 0 2px;
        cursor: pointer;

        &:active {
            opacity: 0.3;
        }
    }

    .translations-button {
        --font-size: 11px;
        margin-left: 10px;

        &.showcase {
            margin-right: 10px;
        }
    }

    ::ng-deep {
        @include media-breakpoint-down(desktop) {
            .manage-view {
                ui-dialog-master .header,
                .ui-notification .text {
                    font-size: $mobile-font-size !important;
                }

                .ui-notification ui-svg-icon {
                    --font-size: #{$mobile-icon-size} !important;
                }
            }
        }
    }

    ns-notifications-component {
        margin-right: 8px;
    }
}
