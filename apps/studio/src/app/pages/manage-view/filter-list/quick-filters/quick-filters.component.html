@let _collapsed = collapsed();
<div
    id="interaction-quick-filters"
    class="quick-filters">
    <div
        class="header"
        [class.collapsed]="_collapsed">
        @let someFiltered = !isAllFiltered() && isSomeFiltered();
        <ui-svg-icon
            (click)="toggleAll()"
            id="interaction-sizes-filter"
            data-test-id="quick-filters-reset"
            class="filter-icon"
            [uiTooltip]="headerTooltip()"
            [uiTooltipHideArrow]="true"
            [class.all-filtered]="isAllFiltered()"
            [class.some-filtered]="someFiltered"
            size="sm"
            icon="visibility-visible"
            [nuiIcon]="someFiltered ? 'visibility_off' : 'visibility'" />

        <ui-label
            size="xs"
            type="secondary"
            weight="bold"
            >Quick filters</ui-label
        >

        <div class="temporary-container-waiting-for-icon"></div>
        <ui-svg-icon
            (click)="collapse()"
            class="icon collapse"
            size="sm"
            [icon]="_collapsed ? 'arrow-up' : 'arrow-down'"
            [nuiIcon]="_collapsed ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" />
    </div>
    @if (!_collapsed) {
        @let _collections = collections();
        <div class="filters-section ui-scrollbar">
            @for (collection of _collections; track collection.id) {
                <div
                    class="collection"
                    (click)="toggleCollection(collection)"
                    [class.unfiltered]="!collection.selected"
                    [class.selected]="collection.selected"
                    data-test-id="quick-filters-interaction-item">
                    <div class="collection-copy">
                        <ui-svg-icon
                            class="visibility-icon"
                            icon="visibility-visible"
                            nuiIcon="visibility"
                            size="sm"
                            [class.selected]="collection.selected" />
                        <div class="collection-name">
                            <ui-label size="xs">{{ collection.name }}</ui-label>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
