:where(:root[data-uinew]) :host {
    .header {
        display: flex;
        gap: var(--nui-space-100);
        padding-left: var(--nui-space-100);
        padding-right: var(--nui-space-100);
        margin-bottom: var(--nui-space-100);

        .filter-icon {
            cursor: pointer;
            color: var(--nui-button-icon-secondary);
        }

        .collapse {
            cursor: pointer;
            margin-left: auto;
            color: var(--nui-button-icon-secondary);
        }
    }

    .status-section {
        display: flex;
        flex-direction: column;
        gap: var(--nui-space-100);

        .list-item {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--nui-list-item-simple-space-gap);
            height: var(--nui-list-item-simple-height);
            padding: var(--nui-list-item-simple-space-padding-vertical)
                var(--nui-list-item-simple-space-padding-horizontal);
            border-radius: var(--nui-list-item-radius);
            font-size: var(--nui-label-regular-font-size);

            &:hover {
                background: var(--nui-list-item-simple-fill-hover);

                .status-visibility-icon {
                    opacity: 1;
                }
            }

            &.selected {
                background: var(--nui-list-item-simple-fill-selected);
            }

            .status-visibility-icon {
                color: var(--nui-list-item-simple-icon-left-hover);
                opacity: 0;

                &.selected {
                    opacity: 1;
                }
            }

            .status-icon {
                font-size: var(--nui-icon-width);
            }

            .count {
                margin-left: auto;
            }
        }
    }
}
