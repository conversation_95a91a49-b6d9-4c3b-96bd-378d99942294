<div
    class="status-list"
    [class.collapsed]="collapsed">
    <div
        class="header"
        [class.collapsed]="collapsed">
        <ui-svg-icon
            class="filter-icon"
            icon="visibility-visible"
            data-test-id="status-filter-reset"
            size="sm"
            [nuiIcon]="hasStatusesFilter() ? 'visibility_off' : 'visibility'"
            (click)="clearStatus()"></ui-svg-icon>

        <ui-label
            class="title"
            size="xs"
            type="secondary"
            weight="bold"
            >Status</ui-label
        >
        <div class="temporary-container-waiting-for-icon"></div>

        <ui-svg-icon
            (click)="collapse()"
            class="icon collapse"
            size="sm"
            [icon]="collapsed ? 'arrow-up' : 'arrow-down'"
            [nuiIcon]="collapsed ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"></ui-svg-icon>
    </div>
    @if (!collapsed) {
        <div class="status-section">
            <div
                class="no-status list-item"
                (click)="filterStatus(ApprovalStatus.NoStatus)"
                [class.selected]="isActive(ApprovalStatus.NoStatus)">
                <ui-svg-icon
                    class="status-visibility-icon"
                    [class.selected]="isActive(ApprovalStatus.NoStatus)"
                    [class.not-selected]="!isActive(ApprovalStatus.NoStatus)"
                    icon="visibility-visible"
                    nuiIcon="visibility"
                    size="sm"></ui-svg-icon>
                <ui-svg-icon
                    class="no-status status-icon"
                    icon="status-dot"
                    nuiIcon="status-no-status-colored"></ui-svg-icon>
                No status <span class="count"> {{ statusCounters[ApprovalStatus.NoStatus] }}</span>
            </div>

            <div
                class="not-approved list-item"
                (click)="filterStatus(ApprovalStatus.NotApproved)"
                [class.selected]="isActive(ApprovalStatus.NotApproved)">
                <ui-svg-icon
                    class="status-visibility-icon"
                    [class.selected]="isActive(ApprovalStatus.NotApproved)"
                    [class.not-selected]="!isActive(ApprovalStatus.NotApproved)"
                    icon="visibility-visible"
                    nuiIcon="visibility"
                    size="sm"></ui-svg-icon>
                <ui-svg-icon
                    class="not-approved status-icon"
                    icon="status-dot"
                    nuiIcon="status-not-approved-colored"></ui-svg-icon>
                Not approved
                <span class="count">{{ statusCounters[ApprovalStatus.NotApproved] }}</span>
            </div>

            <div
                class="progress list-item"
                (click)="filterStatus(ApprovalStatus.InProgress)"
                [class.selected]="isActive(ApprovalStatus.InProgress)">
                <ui-svg-icon
                    class="status-visibility-icon"
                    [class.selected]="isActive(ApprovalStatus.InProgress)"
                    [class.not-selected]="!isActive(ApprovalStatus.InProgress)"
                    icon="visibility-visible"
                    nuiIcon="visibility"
                    size="sm"></ui-svg-icon>
                <ui-svg-icon
                    class="progress status-icon"
                    icon="status-dot"
                    nuiIcon="status-in-progress-colored"></ui-svg-icon>
                In progress
                <span class="count">{{ statusCounters[ApprovalStatus.InProgress] }}</span>
            </div>

            <div
                class="review list-item"
                (click)="filterStatus(ApprovalStatus.ForReview)"
                [class.selected]="isActive(ApprovalStatus.ForReview)">
                <ui-svg-icon
                    class="status-visibility-icon"
                    [class.selected]="isActive(ApprovalStatus.ForReview)"
                    [class.not-selected]="!isActive(ApprovalStatus.ForReview)"
                    icon="visibility-visible"
                    nuiIcon="visibility"
                    size="sm"></ui-svg-icon>
                <ui-svg-icon
                    class="review status-icon"
                    icon="status-dot"
                    nuiIcon="status-for-review-colored"></ui-svg-icon>
                For review
                <span class="count">{{ statusCounters[ApprovalStatus.ForReview] }}</span>
            </div>

            <div
                class="approved list-item"
                (click)="filterStatus(ApprovalStatus.Approved)"
                [class.selected]="isActive(ApprovalStatus.Approved)">
                <ui-svg-icon
                    class="status-visibility-icon"
                    [class.selected]="isActive(ApprovalStatus.Approved)"
                    [class.not-selected]="!isActive(ApprovalStatus.Approved)"
                    icon="visibility-visible"
                    nuiIcon="visibility"
                    size="sm"></ui-svg-icon>
                <ui-svg-icon
                    class="approved status-icon"
                    icon="status-dot"
                    nuiIcon="status-approved-colored"></ui-svg-icon>
                Approved <span class="count"> {{ statusCounters[ApprovalStatus.Approved] }}</span>
            </div>
        </div>
    }
</div>
