:where(:root[data-uinew]) :host {
    .header {
        display: flex;
        gap: var(--nui-space-100);
        padding-left: var(--nui-space-100);
        padding-right: var(--nui-space-100);
        margin-bottom: var(--nui-space-100);

        .filter-icon {
            cursor: pointer;
            color: var(--nui-button-icon-secondary);
        }

        .collapse {
            cursor: pointer;
            margin-left: auto;
            color: var(--nui-button-icon-secondary);
        }
    }

    .size-section {
        display: flex;
        flex-direction: column;
        gap: var(--nui-space-100);

        .size {
            cursor: pointer;
            display: flex;
            align-items: center;
            // gap: var(--nui-list-item-simple-space-gap);
            // height: var(--nui-list-item-simple-height);
            // padding: var(--nui-list-item-simple-space-padding-vertical)
            //     var(--nui-list-item-simple-space-padding-horizontal);
            // border-radius: var(--nui-list-item-radius);

            // mobile
            display: flex;
            height: var(--nui-list-item-simple-height, 32px);
            padding: var(--nui-list-item-simple-space-padding-vertical, 4px)
                var(--nui-list-item-simple-space-padding-horizontal, 12px);
            align-items: center;
            gap: var(--nui-list-item-simple-space-gap, 8px);
            flex: 1 0 0;
            &.selected {
                background: var(--nui-list-item-simple-fill-selected);
            }

            &:hover {
                background: var(--nui-list-item-simple-fill-hover);
                .size-visibility-icon {
                    opacity: 1;
                }
            }

            .size-visibility-icon {
                color: var(--nui-list-item-simple-icon-left-hover);
                opacity: 0;

                &.selected {
                    opacity: 1;
                }
            }

            .size-copy {
                display: grid;
                grid-template-columns: var(--nui-icon-font-icon-size) auto;
                align-items: center;
                grid-gap: var(--nui-space-100);

                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                .size {
                    padding: 0;
                    overflow: hidden;
                    .size-name {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: var(--nui-text-secondary);
                    }
                }
            }
        }
    }
}
