@let _inShowcaseMode = inShowcaseMode();
@let _shouldShowHorizontalSizeList = shouldShowHorizontalSizeList();
@let _creativeset = creativeset();

@if (isNewUI()) {
    <manage-menu
        #manageMenu
        [hasDesigns]="hasDesigns()" />
    <ui-header
        class="header"
        [type]="'transparent'"
        [showLogo]="false">
        <ng-container left>
            <div class="left-side-container">
                <topbar-context-menu [target]="manageMenu.dropdown" />
                <ui-breadcrumbs
                    type="secondary"
                    size="sm">
                    <ui-breadcrumb
                        type="secondary"
                        size="sm">
                        <ui-label
                            size="sm"
                            leadingIcon="dashboard">
                            <span
                                [uiTooltip]="_creativeset!.name"
                                truncateSpan
                                [spanText]="_creativeset!.name"></span>
                        </ui-label>
                    </ui-breadcrumb>
                </ui-breadcrumbs>

                @if (hasConnectedUsers()) {
                    <current-users />
                }
                @if (isEmployee() && _inShowcaseMode === false) {
                    <feature-toggle
                        uiTooltip="Features in development. Use at your own risk!"
                        uiTooltipPosition="bottom" />
                    <toggle-environment />
                }
            </div>
        </ng-container>
        <ng-container middle>
            <div class="middle-side-container">
                <ng-container *media="Breakpoint.DesktopUp">
                    <ng-container *ngTemplateOutlet="versionPickerTemplate"></ng-container>
                </ng-container>
                <ng-container *media="Breakpoint.DesktopDown">
                    <ng-container *ngTemplateOutlet="topbarContentTemplate"></ng-container>
                </ng-container>
            </div>
        </ng-container>
        <ng-container
            right
            *media="Breakpoint.DesktopUp">
            <ng-container *ngTemplateOutlet="rightSideContent"></ng-container>
        </ng-container>
    </ui-header>
    <ng-template #rightSideContent>
        <div class="right-side-container">
            @if (!_inShowcaseMode) {
                <ns-notifications-component />
            }

            @if (_creativeset) {
                <comments-overview
                    [creativesetId]="_creativeset.id"
                    [creativesetName]="_creativeset.name" />
            }

            <div class="header-actions">
                <!--   remove method call when data structure allows it-->
                @if (hasDesigns()) {
                    <campaign-actions
                        *permissions="['Default', 'CampaignActions']"
                        data-test-id="campaign-actions" />
                }

                <ui-button
                    *permissions="['TranslationPage', 'TranslateVersions']; operation: 'OR'"
                    id="interaction-open-translation-page"
                    class="translations-button"
                    routerLink="./translate"
                    [class.showcase]="_inShowcaseMode"
                    queryParamsHandling="merge"
                    type="solid-secondary"
                    text="Content & Styling"
                    nuiSvgIcon="translate"
                    size="sm"
                    svgIcon="none" />

                @if (_inShowcaseMode) {
                    <mark-done-button />
                }
            </div>

            <help-menu />

            <ng-container *media="Breakpoint.DesktopUp">
                @if (!_inShowcaseMode) {
                    <a
                        href="{{ exitLink }}"
                        target="_self"
                        class="exit-link">
                        <ui-button
                            nuiType="plain-primary"
                            nuiSvgIcon="close" />
                    </a>
                }
            </ng-container>
        </div>
    </ng-template>
} @else {
    <ui-header
        class="header"
        [full]="true"
        [showLogo]="false">
        <studio-topbar>
            <!--    centering version picker through collapsable state of translation panel -->
            <ng-container left>
                <ng-container *ngTemplateOutlet="topbarContentTemplate"></ng-container>
            </ng-container>
            <ng-container center>
                <ng-container *media="Breakpoint.DesktopUp">
                    <ng-container *ngTemplateOutlet="versionPickerTemplate"></ng-container>
                </ng-container>
                <ng-container *media="Breakpoint.DesktopDown">
                    <ng-container *ngTemplateOutlet="topbarContentTemplate"></ng-container>
                </ng-container>
            </ng-container>
            <ng-container right>
                @if (isEmployee() && _inShowcaseMode === false) {
                    <feature-toggle
                        uiTooltip="Features in development. Use at your own risk!"
                        uiTooltipPosition="bottom" />
                    <toggle-environment />
                }

                @if (!_inShowcaseMode) {
                    <ns-notifications-component />
                }

                <current-users />

                @if (_creativeset) {
                    <comments-overview
                        [creativesetId]="_creativeset.id"
                        [creativesetName]="_creativeset.name">
                    </comments-overview>
                }

                <!--   remove method call when data structure allows it-->
                @if (hasDesigns()) {
                    <ng-container>
                        <campaign-actions
                            *permissions="['Default', 'CampaignActions']"
                            data-test-id="campaign-actions"></campaign-actions>
                    </ng-container>
                }

                <ui-button
                    *permissions="['TranslationPage', 'TranslateVersions']; operation: 'OR'"
                    id="interaction-open-translation-page"
                    class="translations-button"
                    routerLink="./translate"
                    [class.showcase]="_inShowcaseMode"
                    queryParamsHandling="merge"
                    type="default"
                    text="CONTENT & STYLING"
                    svgIcon="translation">
                </ui-button>

                <mark-done-button></mark-done-button>

                <help-menu class="help-menu" />

                <ng-container *media="Breakpoint.DesktopUp">
                    @if (!_inShowcaseMode) {
                        <a
                            href="{{ exitLink }}"
                            target="_self"
                            class="exit-link">
                            <ui-svg-icon
                                icon="close"
                                nuiIcon="close" />
                        </a>
                    }
                </ng-container>
            </ng-container>
        </studio-topbar>
        @if (_shouldShowHorizontalSizeList) {
            <size-list-horizontal> </size-list-horizontal>
        }
    </ui-header>
}
<ng-template #topbarContentTemplate>
    <topbar-context-menu [target]="manageMenu.dropdown" />
    <manage-menu
        #manageMenu
        [hasDesigns]="hasDesigns()"></manage-menu>
    <div
        class="creativeset-name"
        (dblclick)="showButtons = !showButtons">
        @if (_creativeset) {
            <span
                [uiTooltip]="_creativeset.name"
                truncateSpan
                [spanText]="_creativeset.name"></span>
        }
    </div>

    @if (showButtons) {
        <div class="test-buttons">
            <div
                class="test-button"
                (click)="openManageView()">
                Manage view
            </div>
        </div>
    }
</ng-template>

<ng-template #versionPickerTemplate>
    <size-picker *media="Breakpoint.DesktopUp" />
    <version-picker
        class="versions"
        [allowManageVersions]="true"
        [showAllVersions]="true">
    </version-picker>
</ng-template>
