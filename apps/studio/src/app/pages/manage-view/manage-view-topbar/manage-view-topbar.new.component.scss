:where(:root[data-uinew]) :host {
    ui-header {
        --grid-template-columns: auto auto auto;
    }

    .left-side-container {
        display: flex;
        align-items: center;
        gap: var(--nui-header-space-gap-breadcrumbs, 16px);
    }

    .middle-side-container {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0px;
        gap: var(--nui-header-space-gap-dropdown);
    }

    .right-side-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--nui-header-space-gap-right, 16px);

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--nui-header-space-gap-actions, 12px);
        }

        .exit-link {
            width: 24px;
            height: 24px;
            text-decoration: none;
        }
    }
}
