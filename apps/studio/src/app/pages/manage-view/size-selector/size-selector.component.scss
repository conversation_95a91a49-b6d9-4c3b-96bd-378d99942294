@use 'mixins' as *;

::ng-deep {
    :not(:root[data-uinew]) .cdk-global-scrollblock {
        overflow: hidden;
    }

    :not(:root[data-uinew]) .ui-dialog-backdrop {
        --background-color: rgba(248, 248, 248, 0.7) !important;
    }

    :not(:root[data-uinew]) .body {
        max-height: 100% !important;
    }

    :not(:root[data-uinew]) .size-selector-dialog {
        ui-dialog-master {
            position: absolute;
            margin: 0;

            @media (orientation: landscape), (orientation: portrait) and (min-width: 768px) {
                right: 0;
                top: 0;
                height: var(--app-height);
                width: 50%;
            }

            @media (orientation: portrait) and (max-width: 767.98px) {
                left: 0;
                bottom: 0;
                height: 50%;
                width: 100%;
            }

            .header:first-child {
                display: none;
                background: red;
            }
        }
    }
}

:not(:root[data-uinew]) .size-selector {
    display: flex;
    justify-content: flex-end;

    & ui-svg-icon {
        margin: 18px;
        --font-size: 1.4rem;
        color: var(--studio-color-text-discrete);

        @include media-breakpoint-down(desktop) {
            --font-size: #{$mobile-icon-size};
        }
    }
}
