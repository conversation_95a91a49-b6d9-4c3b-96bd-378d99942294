import { Component, inject } from '@angular/core';
import { UIDialogService, UIModule, UINewThemeService } from '@bannerflow/ui';
import { SizeSelectorDialogComponent } from '../size-selector-dialog/size-selector-dialog.component';

@Component({
    imports: [UIModule],
    selector: 'size-selector',
    templateUrl: './size-selector.component.html',
    styleUrls: ['./size-selector.component.scss', './size-selector.new.component.scss'],
    host: {
        '[class.new-ui]': 'isNewUI()'
    }
})
export class SizeSelectorComponent {
    private uiDialogService = inject(UIDialogService);
    private uiNewThemeService = inject(UINewThemeService);

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    openFilterList(): void {
        this.uiDialogService.openComponent(SizeSelectorDialogComponent, {
            padding: 0,
            panelClass: ['size-selector-dialog', 'inlined-iframe']
        });
    }
}
