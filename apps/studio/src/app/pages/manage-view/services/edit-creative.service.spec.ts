import { TestBed } from '@angular/core/testing';
import { IDesign } from '@domain/creativeset';
import { ApprovalStatus, ICreative } from '@domain/creativeset/creative';
import { createMockCreativeSize } from '@mocks/creative/creative-size.mock';
import { createMockCreative } from '@mocks/creative/creative.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { CreativesetDataService } from '@studio/common';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { Observable, of } from 'rxjs';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { SizesService } from '../../../shared/sizes/sizes.data.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { EditCreativeService } from './edit-creative.service';
import { TileSelectService } from './tile-select.service';

const mockCreativesetDataService = {
    ...createMockCreativesetDataService(),
    deleteDesignsInCreativeset: jest.fn(() => of(['1'])),
    createDesignsWithExistingSize: jest.fn(() => of({})),
    setApprovalStatus: (
        _approvalStatus: ApprovalStatus,
        creative: ICreative
    ): Observable<ICreative[]> => of([creative])
};

const mockTileSelectService: Partial<TileSelectService> = {
    selection$: of([])
};

const mockSizesService: Partial<SizesService> = {
    update$: of(),
    updateSizeName: (_sizeId: string, name: string, _creativesetId: string) => {
        return of({ id: _sizeId, name });
    }
};

describe('EditCreativeService', () => {
    let editCreativeService: EditCreativeService;
    let mockCreativeWithoutDesign: ICreative;
    let mockCreativeWithDesign: ICreative & { design: IDesign };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                VersionsService,
                {
                    provide: CreativesetDataService,
                    useValue: mockCreativesetDataService
                },
                {
                    provide: TileSelectService,
                    useValue: mockTileSelectService
                },
                {
                    provide: SizesService,
                    useValue: mockSizesService
                }
            ]
        });
        editCreativeService = TestBed.inject(EditCreativeService);
        editCreativeService.init();

        mockCreativeWithDesign = createMockCreative(true);
        mockCreativeWithoutDesign = createMockCreative();
    });

    it('should create service', () => {
        expect(editCreativeService).toBeTruthy();
    });

    describe('activateCreatives', () => {
        const apiCall = mockCreativesetDataService.createDesignsWithExistingSize;
        afterAll(() => {
            apiCall?.mockReset();
        });

        it("shouldn't call createDesignsWithExistingSize when empty array is passed", () => {
            editCreativeService.activateCreatives([]);
            expect(apiCall).not.toHaveBeenCalled();
        });
    });

    describe('deactivateSelectedDesigns', () => {
        afterAll(() => {
            mockCreativesetDataService.deleteDesignsInCreativeset.mockReset();
        });

        it('should call deleteDesignsInCreativeset with the correct creatives', () => {
            editCreativeService.deactivateSelectedDesigns([]);
            expect(mockCreativesetDataService.deleteDesignsInCreativeset).not.toHaveBeenCalled();
        });

        it("shouldn't call delete when creatives have no design", () => {
            editCreativeService.deactivateSelectedDesigns([mockCreativeWithoutDesign]);
            expect(mockCreativesetDataService.deleteDesignsInCreativeset).not.toHaveBeenCalled();
        });

        it('should call delete creatives with one creative', () => {
            editCreativeService.deactivateSelectedDesigns([mockCreativeWithDesign]);
            expect(mockCreativesetDataService.deleteDesignsInCreativeset).toHaveBeenCalledWith([
                mockCreativeWithDesign.design.id
            ]);
        });
    });

    describe('saveSizeName', () => {
        it('should update the size name', async () => {
            const sizeMock = createMockCreativeSize();
            await editCreativeService.saveSizeName(sizeMock, 'foo');
            expect(sizeMock.name).toBe('foo');
        });
    });

    describe('updateApprovalStatus', () => {
        it('should update approval status', async () => {
            const creative = mockCreativeWithDesign;
            await editCreativeService.updateApprovalStatus(ApprovalStatus.Approved, [creative]);
            expect(creative.approvalStatus).toBe(ApprovalStatus.Approved);
        });
    });
});
