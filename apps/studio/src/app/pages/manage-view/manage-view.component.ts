import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    ElementRef,
    HostListener,
    inject,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Title } from '@angular/platform-browser';
import { FFFeatureFlagsService } from '@bannerflow/feature-flags';
import { Logger } from '@bannerflow/sentinel-logger';
import {
    UIConfirmDialogService,
    UIDialogService,
    UIModule,
    UINewThemeService,
    UINotificationService,
    UiCardDirective
} from '@bannerflow/ui';
import { AppView } from '@domain/creative/environment';
import { ICreativeset } from '@domain/creativeset/creativeset';
import { IHotkeyContext } from '@domain/hotkeys/hotkeys.types';
import { CreativesetDataService, UserService } from '@studio/common';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { UserSettingsService } from '@studio/common/user-settings';
import { StudioFeatureFlags } from '@studio/domain/feature-flags';
import { BrowserDefaultHotkeys } from '@studio/hotkeys/hotkeys';
import { HeavyVideoIndicatorComponent } from '@studio/manage-view';
import { getBrowserVersion, isChrome, isMobile, isSafari } from '@studio/utils/ad/browser';
import { Breakpoint } from '@studio/utils/breakpoints';
import {
    isChildOfSelector,
    isElementDescendantOfElement,
    isElementDescendantOfElementWithClass
} from '@studio/utils/dom-utils';
import { SimpleCache } from '@studio/utils/simple-cache';
import { merge } from 'rxjs';
import { filter, switchMap, take } from 'rxjs/operators';
import { DevtoolsComponent } from '../../core/devtools/devtools.component';
import { NavigationGuard } from '../../routes/navigation.guard';
import { AnimationControlComponent } from '../../shared/animation-control/animation-control.component';
import { AnimationControlService } from '../../shared/animation-control/state/animation-control.service';
import { VersionPickerComponent } from '../../shared/components/version-picker/version-picker.component';
import { CreativeListComponent } from '../../shared/creative-list/creative-list.component';
import { CreativeSetShowcaseService } from '../../shared/creativeset-showcase/state/creativeset-showcase.service';
import { MediaDirective } from '../../shared/directives/media.directive';
import { PermissionsDirective } from '../../shared/directives/permissions.directive';
import { DisplayCampaignService } from '../../shared/display-campaign/state/display-campaign.service';
import { FiltersService } from '../../shared/filters/state/filters.service';
import { HotkeyBetterService } from '../../shared/services/hotkeys/hotkey.better.service';
import { PointerStatesService } from '../../shared/services/pointer-state.service';
import { StudioRoutingService } from '../../shared/services/studio-routing.service';
import { SizePickerComponent } from '../../shared/size-picker/size-picker.component';
import { VersionsService } from '../../shared/versions/state/versions.service';
import { getManageViewTitle } from '../page-title-util';
import { MVContextMenuComponent } from './context-menu/context-menu.component';
import { ManageViewContextMenuService } from './context-menu/manage-view-context-menu.service';
import { CreativeListItemMenuComponent } from './creative-list-item-menu/creative-list-item-menu.component';
import { CreativeListItemPreviewComponent } from './creative-list-item-preview/creative-list-item-preview.component';
import { CreativeListItemSelectComponent } from './creative-list-item-select/creative-list-item-select.component';
import { CreativeListItemTitleComponent } from './creative-list-item-title/creative-list-item-title.component';
import { CreativeListItemWeightComponent } from './creative-list-item-weight/creative-list-item-weight.component';
import { FilterListComponent } from './filter-list/filter-list.component';
import { ManageViewTopbarComponent } from './manage-view-topbar/manage-view-topbar.component';
import { EditCreativeService } from './services/edit-creative.service';
import { TileSelectService } from './services/tile-select.service';
import { SizeAddDialogComponent } from './size-add-dialog/size-add-dialog.component';
import { SizeSelectorComponent } from './size-selector/size-selector.component';
import { TranslationPanelComponent } from './translation-panel/translation-panel.component';
import { CreativesetHistoryComponent } from './creativeset-history-panel/creativeset-history-panel.component';
import { CreativesetHistoryService } from './creativeset-history-panel/creativeset-history-panel.service';

@Component({
    imports: [
        AnimationControlComponent,
        CommonModule,
        CreativeListComponent,
        CreativeListItemMenuComponent,
        CreativeListItemPreviewComponent,
        CreativeListItemSelectComponent,
        CreativeListItemTitleComponent,
        CreativeListItemWeightComponent,
        DevtoolsComponent,
        FilterListComponent,
        HeavyVideoIndicatorComponent,
        MVContextMenuComponent,
        ManageViewTopbarComponent,
        MediaDirective,
        PermissionsDirective,
        SizePickerComponent,
        SizeSelectorComponent,
        TranslationPanelComponent,
        UIModule,
        VersionPickerComponent,
        CreativesetHistoryComponent,
        UiCardDirective
    ],
    templateUrl: './manage-view.component.html',
    styleUrls: ['./manage-view.component.scss', './manage-view.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageViewComponent implements OnInit, OnDestroy, AfterViewInit {
    private animationControlService = inject(AnimationControlService);
    private creativeSetDataService = inject(CreativesetDataService);
    private creativesetShowcaseService = inject(CreativeSetShowcaseService);
    private destroyRef = inject(DestroyRef);
    private displayCampaignService = inject(DisplayCampaignService);
    private editCreativeService = inject(EditCreativeService);
    private environmentService = inject(EnvironmentService);
    private ffFeatureFlagService = inject(FFFeatureFlagsService);
    private filtersService = inject(FiltersService);
    private hotkeyBetterService = inject(HotkeyBetterService);
    private manageViewContextMenuService = inject(ManageViewContextMenuService);
    private navigationGuard = inject(NavigationGuard);
    private pointerStatesService = inject(PointerStatesService);
    private studioRoutingService = inject(StudioRoutingService);
    private tileSelectService = inject(TileSelectService);
    private titleService = inject(Title);
    private uiConfirmDialogService = inject(UIConfirmDialogService);
    private uiDialogService = inject(UIDialogService);
    private uiNotificationService = inject(UINotificationService);
    private userService = inject(UserService);
    private userSettingsService = inject(UserSettingsService);
    private versionsService = inject(VersionsService);
    private uiNewThemeService = inject(UINewThemeService);
    private creativesetHistoryService = inject(CreativesetHistoryService);

    @ViewChild('scrollView') scrollView: ElementRef;
    @ViewChild('filterList', { static: true }) filterList: FilterListComponent;

    @HostListener('window:mousedown', ['$event']) w = (event: MouseEvent): void => {
        if (event.button !== 2) {
            if (!isElementDescendantOfElementWithClass(event.target, 'ui-dropdown-list')) {
                this.manageViewContextMenuService.close();
            }
        }
    };

    toggleOtherSizes = (): void => this.applyToggleOtherSizesShortcut();
    deselectAllShortcut = (): void => this.selectShortcut(false);
    selectAllShortcut = (): void => this.selectShortcut(true);
    deleteSelectedShortcut = (): void => this.deleteShortcut();
    copySelected = (): void => this.editCreativeService.copyDesign();
    pasteSelected = (): Promise<void> => this.editCreativeService.pasteDesign();
    navigateToTPShortcut = (): void => this.studioRoutingService.navigateToTP();
    openCreativesetHistoryPanel = (): void => {
        if (this.isCreativesetHistoryEnabled()) {
            this.creativesetHistoryService.showCreativesetHistoryPanel();
        }
    };

    private logger = new Logger('ManageView', true);

    creativeset: ICreativeset;

    private downloadIsPending = false;

    private campaignsList = toSignal(this.displayCampaignService.campaignsList$, { initialValue: [] });

    Breakpoint = Breakpoint;

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    private shouldHideTPanel = toSignal(
        this.ffFeatureFlagService.isEnabled$(StudioFeatureFlags.StudioHideTPanel),
        { initialValue: false }
    );

    private isManageViewNUIEnabled = toSignal(
        this.ffFeatureFlagService.isEnabled$(StudioFeatureFlags.ManageViewNUI),
        {
            initialValue: false
        }
    );

    private isShowingMultipleVersions = toSignal(this.filtersService.isShowingMultipleVersions$, {
        initialValue: false
    });
    private isMobile = toSignal(this.environmentService.isMobile$, { initialValue: false });
    private isShowingAllVersions = toSignal(this.filtersService.isShowingAllVersions$, {
        initialValue: false
    });

    shouldShowTranslationPanel = computed(() => this.computeShouldShowTranslationPanel());
    shouldShowHorizontalSizeList = computed(() => this.computeShouldShowHorizontalSizeList());

    hasActiveFilters = toSignal(this.filtersService.hasActiveFilters$, { initialValue: false });
    inShowcaseMode = toSignal(this.environmentService.inShowcaseMode$, { initialValue: false });

    private filteredCreatives = toSignal(this.filtersService.filteredCreatives$, { initialValue: [] });
    isViewEmpty = computed(() => this.computeIsViewEmpty());

    isCreativesetHistoryEnabled = toSignal(this.creativesetHistoryService.isCreativesetHistoryEnabled$);
    shouldShowCreativesetHistoryPanel = toSignal(
        this.creativesetHistoryService.isCreativesetHistoryPanelShown$,
        { initialValue: false }
    );

    constructor() {
        this.environmentService.setPage(AppView.ManageView);

        this.creativeSetDataService.creativeset$
            .pipe(takeUntilDestroyed())
            .subscribe(creativeset => (this.creativeset = creativeset));

        const sizeIds = this.creativeset.sizes.map(({ id }) => id);
        this.filtersService.initFilters(sizeIds);

        this.setupHotkeyListeners();

        effect(() => {
            const isManageViewNUIEnabled = this.isManageViewNUIEnabled();

            if (!isManageViewNUIEnabled) {
                this.uiNewThemeService.enableNewTheme();
            } else {
                this.uiNewThemeService.disableNewTheme();
            }
        });
    }

    ngOnInit(): void {
        this.userSettingsService.sharedSettings$.pipe(take(1)).subscribe(settings => {
            if (settings.lastLocation !== 'TranslationPage') {
                this.filtersService.selectPreviousVersionSelection();
            }
            this.userSettingsService.setSharedSetting('lastLocation', 'ManageView');
        });

        this.resize();

        this.editCreativeService.init();
        this.navigationGuard.addPristineUnloadCheck(this.isPristine);
        this.navigationGuard.addPristineCheck(this.checkPristineState);

        this.tileSelectService.deselectAllCreatives();

        this.userService.isEmployee$.pipe(take(1), filter(Boolean)).subscribe(() => {
            window.managePage = this;
        });

        this.setManageViewTitle();

        /* combineLatest needs atleast a startvalue from each before it fires for the first time */
        merge(this.editCreativeService.updateView$, this.versionsService.selectedVersions$)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.updateView();
            });

        this.versionsService.selectedVersions$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.tileSelectService.deselectAllCreatives();
            });

        if (!this.isSupportBrowser()) {
            this.uiNotificationService.open(
                `You will get the best experience with the Bannerflow
                     showcase on the latest versions of Safari and Google Chrome browsers. We cannot
                     guarantee it on your current browser.`,
                {
                    placement: 'bottom',
                    type: 'warning'
                }
            );
        }
    }

    ngAfterViewInit(): void {
        if (!this.creativeset.creatives.length) {
            this.openAddSizeDialog();
        }

        if (this.applyLastScrollPosition()) {
            this.scrollView.nativeElement.scrollTo(0, this.studioRoutingService.lastScrollPosition);
        }
    }

    async setupHotkeyListeners(): Promise<void> {
        this.logger.verbose('Setting up hotkeys');
        const hotkeyContext: IHotkeyContext = {
            name: 'ManagePage',
            input: window,
            keyDefaultBehaviourExclusions: Object.values(BrowserDefaultHotkeys)
        };

        if (this.inShowcaseMode()) {
            hotkeyContext.name = 'Showcase';
        }

        this.hotkeyBetterService.pushContext(hotkeyContext);

        this.hotkeyBetterService.on('ToggleOtherSizes', this.toggleOtherSizes);
        this.hotkeyBetterService.on('Deselect', this.deselectAllShortcut);
        this.hotkeyBetterService.on('SelectAll', this.selectAllShortcut);

        if (this.userService.canChangeDesign) {
            this.hotkeyBetterService.on('DeleteSize', this.deleteSelectedShortcut);
        }

        if (await this.userService.hasPermission('StudioTranslationPage')) {
            this.hotkeyBetterService.on('NavigateToTP', this.navigateToTPShortcut);
        }

        this.hotkeyBetterService.on('OpenCreativesetHistoryPanel', this.openCreativesetHistoryPanel);
        this.hotkeyBetterService.on('Copy', this.copySelected);
        this.hotkeyBetterService.on('Paste', this.pasteSelected);
    }

    removeHotkeyListeners(): void {
        this.logger.verbose('Removing hotkeys');
        this.hotkeyBetterService.off('ToggleOtherSizes', this.toggleOtherSizes);
        this.hotkeyBetterService.off('Deselect', this.deselectAllShortcut);
        this.hotkeyBetterService.off('SelectAll', this.selectAllShortcut);
        this.hotkeyBetterService.off('DeleteSize', this.deleteSelectedShortcut);
        this.hotkeyBetterService.off('Copy', this.copySelected);
        this.hotkeyBetterService.off('Paste', this.pasteSelected);
        this.hotkeyBetterService.off('NavigateToTP', this.navigateToTPShortcut);
        this.hotkeyBetterService.off('OpenCreativesetHistoryPanel', this.openCreativesetHistoryPanel);
        this.hotkeyBetterService.popContext();
    }

    private setManageViewTitle(): void {
        this.titleService.setTitle(getManageViewTitle(this.creativeset.name));
    }

    scrollViewMouseEnter = (): void => this.pointerStatesService.managePageScrollViewHovered.next(true);
    scrollViewMouseLeave = (): void =>
        this.pointerStatesService.managePageScrollViewHovered.next(false);

    isPristine = (): boolean => !this.downloadIsPending;

    selectShortcut(select: boolean): void {
        if (select) {
            this.tileSelectService.selectAllCreatives();
        } else {
            this.tileSelectService.deselectAllCreatives();
        }
    }

    ngOnDestroy(): void {
        this.tileSelectService.deselectAllCreatives();
        this.editCreativeService.clearCreativeVisiblityStatus();
        this.navigationGuard.removePristineCheck(this.checkPristineState);
        this.removeHotkeyListeners();
        SimpleCache.clear();
        this.editCreativeService.destroy();
    }

    @HostListener('window:resize') resize(): void {
        const documentElement = document.documentElement;
        documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
    }

    deselect(event: MouseEvent): void {
        const isCreativeListClick = isElementDescendantOfElement(
            this.scrollView.nativeElement,
            event.target
        );
        const isMenuClick = isChildOfSelector(event.target as HTMLElement, 'creative-list-groupheader');
        if (isCreativeListClick && !isMenuClick) {
            this.tileSelectService.deselectAllCreatives();
        }
    }

    openAddSizeDialog(): void {
        // stop animations when opening the dialog
        this.animationControlService.stopAnimations();

        const dialog = this.uiDialogService.openComponent(SizeAddDialogComponent, {
            headerText: 'Add creative sizes',
            panelClass: ['no-padding', 'fullscreen'],
            maxWidth: '100%',
            width: '100%',
            height: '100%',
            escKeyClose: false,
            theme: 'default',
            padding: this.isNewUI() ? 0 : undefined
        });

        dialog
            .afterClose()
            .pipe(
                take(1),
                switchMap(() => this.creativeSetDataService.creativeset$)
            )
            .subscribe(() => {
                const sizeIds = this.creativeSetDataService.creativeset.sizes.map(({ id }) => id);
                this.filtersService.setAllSizes(sizeIds);
                this.tileSelectService.deselectAllCreatives();
                /* we update more than just this view */
                this.editCreativeService.updateView();
            });
    }

    creativeListScroll($event: Event): void {
        this.tileSelectService.creativeListScrolled($event);
    }

    private computeShouldShowTranslationPanel(): boolean {
        if (this.isShowingMultipleVersions()) {
            return false;
        }
        if (this.shouldHideTPanel()) {
            return false;
        }
        if (this.inShowcaseMode()) {
            return this.creativesetShowcaseService.operationsAllowed(['updateVersions']);
        }
        return true;
    }

    private computeShouldShowHorizontalSizeList(): boolean {
        return this.isMobile() && this.isShowingAllVersions();
    }

    private updateView(): void {
        SimpleCache.clear();
        /* refactoring of propertiespanel was outside the scope of this ticket. More explanation inside serivce */
    }

    private isSupportBrowser(): boolean {
        const version = getBrowserVersion();
        const isMobileDevice = isMobile();
        const supportedChrome = isChrome && version >= 107;
        const supportedSafari = isSafari && version >= 16;
        const isSupportedMobileBrowser = isMobileDevice && (supportedChrome || supportedSafari);
        const isDesktop = !isMobileDevice;
        return isDesktop || isSupportedMobileBrowser;
    }

    private computeIsViewEmpty(): boolean {
        return !this.filteredCreatives().length;
    }

    private applyToggleOtherSizesShortcut = (): void => {
        this.filtersService.toggleSelectedSizes();
    };

    private deleteShortcut(): void {
        const selectedCreatives = this.tileSelectService.getSelected();

        const canDelete = !selectedCreatives.some(creative =>
            this.campaignsList().some(
                ({ creativeId, connectedCampaigns }) =>
                    creativeId === creative.id && !!connectedCampaigns.length
            )
        );
        if (!canDelete) {
            return;
        }
        this.editCreativeService.deleteSizes();
    }

    private checkPristineState = async (): Promise<boolean> => {
        if (!this.isPristine()) {
            const result = await this.uiConfirmDialogService.confirm({
                headerText: 'You have unsaved changes',
                text: 'Are you sure you want discard unsaved changes?',
                confirmText: 'Yes'
            });
            return result !== 'cancel';
        }
        return true;
    };

    private applyLastScrollPosition(): boolean {
        if (this.studioRoutingService.lastScrollPosition === 0 || !this.creativeset.creatives.length) {
            return false;
        }

        return true;
    }
}
