import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Logger } from '@bannerflow/sentinel-logger';
import {
    forEachDataElement,
    isImageNode,
    isVideoNode,
    ratioLockSvgElement
} from '@creative/nodes/helpers';
import { getGlobalElementsFromCreativeDataNode } from '@data/deserialization/design-api/sapi-conversion-helpers';
import { ImageLibraryAsset, VideoLibraryAsset } from '@domain/brand/brand-library';
import { ICopyPasteElementSnapshot } from '@domain/copy-paste';
import { IImageElementAsset, IVideoElementAsset } from '@domain/creativeset/element-asset';
import { IPosition } from '@domain/dimension';
import { IFontFamily } from '@domain/font-families';
import { IImageElementDataNode, IMediaElementDataNode, IVideoElementDataNode } from '@domain/nodes';
import { CreativesetDataService, UserService } from '@studio/common';
import { FontFamiliesService } from '@studio/common/font-families';
import { SaveType } from '@studio/domain/components/ai-studio.types';
import { AssetUploadCompleteEvent, EventLoggerService } from '@studio/monitoring/events';
import { getBoundingboxWithinBoundary } from '@studio/utils/geom';
import {
    getAssetOfMediaElement,
    isImageFile,
    isMediaReference,
    isVideoFile
} from '@studio/utils/media';
import { removeFileExtension } from '@studio/utils/url';
import { filter } from 'rxjs';
import { GenAIService } from '../../../../shared/ai-studio/state/gen-ai.service';
import { imageUrlToFile } from '../../../../shared/ai-studio/utils/file.utils';
import {
    AssetUploadService,
    IAssetUploadAfterProgressState,
    IAssetUploadLoadedState,
    IAssetUploadState
} from '../../services/asset-upload.service';
import { CopyPasteService } from '../../services/copy-paste.service';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { ElementChangeType } from '@domain/element-change';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementCreatorService } from '../../services/element-creator.service';
import { HistoryService, IEditorSnapshot } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { StudioWorkspaceComponent } from '../studio-workspace.component';

@Injectable()
export class WorkspaceUploadAssetService {
    private assetUploadService = inject(AssetUploadService);
    private editorStateService = inject(EditorStateService);
    private editorEventService = inject(EditorEventService);
    private eventLoggerService = inject(EventLoggerService);
    private userService = inject(UserService);
    private copyPasteService = inject(CopyPasteService);
    private historyService = inject(HistoryService);
    private mutatorService = inject(MutatorService);
    private elementCreatorService = inject(ElementCreatorService);
    private creativesetDataService = inject(CreativesetDataService);
    private fontFamiliesService = inject(FontFamiliesService);
    private genAIService = inject(GenAIService);

    private assetUploadProcessId: string;
    private workspace: StudioWorkspaceComponent;
    private placeholders = new Map<string, IVideoElementDataNode | IImageElementDataNode>();
    private currAssetPosition: IPosition;
    private logger = new Logger('WorkspaceUploadAssetService');

    private creativeSetFontFamilies: IFontFamily[] = [];

    constructor() {
        this.assetUploadService.uploadProgress$
            .pipe(takeUntilDestroyed())
            .subscribe(this.onAssetUploadStateChange);

        this.fontFamiliesService.creativeSetFontFamilies$
            .pipe(takeUntilDestroyed())
            .subscribe(
                creativesetFontFamilies => (this.creativeSetFontFamilies = creativesetFontFamilies)
            );

        this.genAIService.saveOnCanvasPayload$
            .pipe(
                takeUntilDestroyed(),
                filter(payload => payload?.saveType === SaveType.Duplicate)
            )
            .subscribe(payload => {
                if (!payload) return;
                this.saveGenAIAssetToCanvasAsDuplicate(payload.imageAsset);
            });
    }

    initService(workspace: StudioWorkspaceComponent): void {
        this.workspace = workspace;
    }

    async uploadAssets(files: File[], currAssetPosition: IPosition, isGenAi = false): Promise<void> {
        this.logger.verbose(`Uploading images to workspace`);

        this.currAssetPosition = currAssetPosition;

        const id = await this.assetUploadService.uploadAssets({
            files,
            isDirectCanvasUpload: true,
            isGenAi
        });
        if (id) {
            this.assetUploadProcessId = id;
        }
    }

    private removeFailedUpload(assetId: string): void {
        forEachDataElement(this.editorStateService.creativeDataNode, node => {
            const isMatchingMedia = node.globalElement.properties.find(
                property => isMediaReference(property) && property.value === assetId
            );

            if (!isMatchingMedia) {
                return;
            }

            this.editorStateService.creativeDataNode.removeNodeById_m(node.id);
        });
    }

    private onAssetUploadStateChange = (uploadState: IAssetUploadState): void => {
        if (this.assetUploadProcessId !== uploadState.uploadProcessId) {
            return;
        }
        switch (uploadState.status) {
            case 'LOAD_ASSET_FILE_STARTED':
                this.uploadStarted(uploadState);
                break;
            case 'AFTER_PROGRESS':
                this.uploadFinished(uploadState);
                break;
            case 'COMPLETE':
                this.eventLoggerService.log(new AssetUploadCompleteEvent(), this.logger);
                break;
            case 'FAIL': {
                this.logger.warn(`Asset upload failed`);
                const placeHolder = this.placeholders.get(uploadState.asset.id);
                if (!placeHolder) {
                    return;
                }
                this.mutatorService.removeSelection(placeHolder);
                const asset = getAssetOfMediaElement(placeHolder);
                if (!asset) {
                    return;
                }
                this.removeFailedAssetUpload(asset.id);
                this.workspace.deselectAllElements();
                this.placeholders.delete(placeHolder.id);
                this.removeFailedUpload(asset.id);
                break;
            }
        }
    };

    private async uploadStarted(uploadState: IAssetUploadLoadedState): Promise<void> {
        this.logger.verbose(`Asset[${uploadState.file.name}] upload started`);

        const { asset, url, size, file } = uploadState;
        const { width, height } = size;
        asset.name = removeFileExtension(file.name);

        const positionAndSize = {
            ...getBoundingboxWithinBoundary(size, this.editorStateService.size, this.currAssetPosition)
        };

        if (isImageFile(file)) {
            const imageAsset: IImageElementAsset = {
                id: asset.id,
                url,
                ...size,
                name: asset.name,
                __loading: true
            };

            const imageElement = this.elementCreatorService.createImage({
                ...positionAndSize,
                name: asset.name,
                imageAsset
            });

            this.placeholders.set(asset.id, imageElement);
        } else if (isVideoFile(file)) {
            if (!(await this.userService.hasPermission('StudioVideoLibrary'))) {
                return;
            }

            const videoAsset: IVideoElementAsset = {
                id: asset.id,
                url: url,
                ...size,
                name: asset.name,
                fileSize: asset.fileSize,
                __loading: true
            };

            asset.url = url;
            asset.width = width;
            asset.height = height;
            asset.fileSize = file.size;

            const video = this.elementCreatorService.createVideo({
                ...positionAndSize,
                name: asset.name,
                videoAsset
            });

            this.placeholders.set(asset.id, video);
        }
    }

    private uploadFinished(uploadState: IAssetUploadAfterProgressState): void {
        this.logger.verbose(`Asset[${uploadState.asset.name}] upload finished`);

        const placeholder = this.placeholders.get(uploadState.asset.id);

        if (placeholder) {
            this.updateAsset(placeholder, uploadState);
        } else {
            throw new Error('No placeholder found');
        }
    }

    private updateAsset(
        placeHolder: IMediaElementDataNode,
        uploadState: IAssetUploadAfterProgressState
    ): void {
        const elementAsset = getAssetOfMediaElement(placeHolder);
        if (!elementAsset) {
            throw new Error('No asset found!');
        }
        const oldAssetId = uploadState.asset.id;
        const newAssetId = uploadState.newAsset.id;
        const newUrl = uploadState.newAsset.url;
        let newSize = 0;

        elementAsset.id = newAssetId;
        elementAsset.url = newUrl;
        elementAsset.__loading = false;

        if ('fileSize' in elementAsset) {
            newSize = uploadState.newAsset.fileSize;
            elementAsset.fileSize = newSize;
        }

        this.editorStateService.updateNewElementAsset(oldAssetId, newAssetId, newUrl, newSize);
        this.updateSnapshotsAssetUrl(oldAssetId, newAssetId, newUrl);

        if (isImageNode(placeHolder)) {
            ratioLockSvgElement(newUrl, placeHolder);
            this.creativesetDataService.creativeset.images.push(
                uploadState.newAsset as ImageLibraryAsset
            );
        } else if (isVideoNode(placeHolder)) {
            this.updateVideoThumbnail(placeHolder);
            this.creativesetDataService.creativeset.videos.push(
                uploadState.newAsset as VideoLibraryAsset
            );
        }

        const time = this.workspace.designView.animator?.time || 1;
        this.editorStateService.renderer.setViewElementValues_m(placeHolder, time);

        this.placeholders.delete(placeHolder.id);
    }

    private updateSnapshotsAssetUrl(oldAssetId: string, newAssetId: string, newUrl: string): void {
        this.logger.verbose('Updating snapshot assets!');

        for (const snapshot of this.getSnapshots()) {
            this.updateSnapshotAssetUrl(oldAssetId, newAssetId, newUrl, snapshot);
        }

        this.updateCopyPasteSnapshot();
    }

    private removeFailedAssetUpload(assetId: string): void {
        for (const snapshot of this.getSnapshots()) {
            const elements = getGlobalElementsFromCreativeDataNode(snapshot.creativeDataNode);
            for (const element of elements) {
                for (const property of element.properties) {
                    if (isMediaReference(property) && property.value === assetId) {
                        snapshot.creativeDataNode.removeNodeById_m(element.id);
                    }
                }
            }
        }
        this.updateCopyPasteSnapshot();
    }

    private updateCopyPasteSnapshot(): void {
        const snapshot = this.copyPasteService.copySnapshot;
        if (snapshot) {
            this.copyPasteService.createCopyPasteSnapshot();
        }
    }

    private updateVideoThumbnail(element: IVideoElementDataNode): void {
        this.editorEventService.elements.change(element, element, ElementChangeType.Instant);
    }

    private saveGenAIAssetToCanvasAsDuplicate(imageAsset: IImageElementAsset): void {
        const { url, name } = imageAsset;
        const creativeSize = this.editorStateService.size;

        imageUrlToFile(url, name ?? '').subscribe(async imageFile => {
            await this.uploadAssets(
                [imageFile],
                {
                    x: creativeSize.width / 2,
                    y: creativeSize.height / 2
                },
                true
            );
            this.genAIService.closeAIStudio();
        });
    }

    private updateSnapshotAssetUrl(
        oldAssetId: string,
        newAssetId: string,
        newURL: string,
        snapshot: IEditorSnapshot | ICopyPasteElementSnapshot
    ): void {
        for (const element of snapshot.creativeDataNode.elements) {
            if (isImageNode(element) && element.imageAsset?.id === oldAssetId) {
                element.imageAsset.url = newURL;
                element.imageAsset.id = newAssetId;
            }

            if (isVideoNode(element) && element.videoAsset?.id === oldAssetId) {
                element.videoAsset.url = newURL;
                element.videoAsset.id = newAssetId;
            }

            for (const property of element.globalElement.properties) {
                if (isMediaReference(property) && property.value === oldAssetId) {
                    property.value = newAssetId;
                }
            }
        }
    }

    private getSnapshots(): (IEditorSnapshot | ICopyPasteElementSnapshot)[] {
        const snapshots: (IEditorSnapshot | ICopyPasteElementSnapshot)[] = [
            ...this.historyService.getAllSnapshots()
        ];

        if (this.copyPasteService.copySnapshot?.context === 'element') {
            snapshots.push(this.copyPasteService.copySnapshot);
        }

        return snapshots;
    }
}
