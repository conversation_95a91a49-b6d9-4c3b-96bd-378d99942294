import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideFeatureFlags } from '@bannerflow/feature-flags';
import { RichText } from '@creative/elements/rich-text/rich-text';
import { ElementKind } from '@domain/elements';
import { ITextElementDataNode, ITextViewElement } from '@domain/nodes';
import { IRichTextEditorService } from '@domain/rich-text/rich-text.editor.header';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createDataNodeMock } from '@mocks/element.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { BrandService, CreativesetDataService, FontFamiliesService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { PropertiesService } from '../properties-panel/properties.service';
import { EditorEventService } from './editor-event';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionBoundingBoxService } from './element-selection-bounding-box.service';
import { ElementSelectionService } from './element-selection.service';
import { MutatorService } from './mutator.service';

describe('MutatorService', () => {
    let mutatorService: MutatorService;

    beforeEach(() => {
        defineMatchMedia();
        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideHttpClient(),
                provideHttpClientTesting(),
                provideFeatureFlags({
                    enabled: false
                }),
                provideEnvironment(environment),
                EnvironmentService,
                MutatorService,
                ElementSelectionService,
                CreativesetDataService,
                ElementSelectionBoundingBoxService,
                PropertiesService,
                EditorEventService,
                EditorStateService,
                {
                    provide: BrandLibraryDataService,
                    useValue: {
                        fetchBrandLibrary$: of()
                    }
                },
                {
                    provide: VersionsService,
                    useValue: createVersionServiceMock()
                },
                {
                    provide: BrandService,
                    useValue: {
                        accountSlug$: of('slug'),
                        error$: of(undefined)
                    }
                },
                {
                    provide: FontFamiliesService,
                    useValue: {
                        fontFamilies$: of([])
                    }
                }
            ]
        });
        mutatorService = TestBed.inject(MutatorService);
    });

    describe('setElementPropertyValue', () => {
        describe('text properties', () => {
            let viewElement: ITextViewElement;
            let textDataNodeMock: ITextElementDataNode;
            beforeEach(() => {
                textDataNodeMock = createDataNodeMock({ kind: ElementKind.Text, id: 'id' });
                mutatorService.renderer = createRendererFixture(300, 250, [textDataNodeMock]);
                viewElement = mutatorService.renderer.getViewElementById('id') as ITextViewElement;

                viewElement.__richTextRenderer = {
                    editor_m: {
                        applyStyleToSelection: jest.fn(),
                        inEditMode: false,
                        selection: {
                            selection: {
                                isCollapsed: true
                            }
                        }
                    } as unknown as IRichTextEditorService
                } as RichText;
            });

            it('should apply text styles to text element when in edit mode', () => {
                mutatorService.setElementPropertyValue(textDataNodeMock, 'fontSize', 40);
                expect(textDataNodeMock.fontSize).toEqual(40);
            });

            it('should not apply text styles to text element when in edit mode', () => {
                viewElement.__richTextRenderer!.editor_m!.inEditMode = true;
                mutatorService.setElementPropertyValue(textDataNodeMock, 'fontSize', 40);
                expect(textDataNodeMock.fontSize).toEqual(50);
            });
        });
    });
});
