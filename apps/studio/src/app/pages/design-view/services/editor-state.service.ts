import { inject, Injectable, OnDestroy } from '@angular/core';
import { FFFeatureFlagsService } from '@bannerflow/feature-flags';
import { applyWidgetCodeOnWidgetNodes } from '@creative/elements/widget/utils';
import { bestBasedOnSize } from '@creative/layout/best-design-decider';
import { getDynamicPropertyOfElement, isImageNode, isVideoNode } from '@creative/nodes';
import { cloneCreativeDocument } from '@creative/serialization';
import { deserializeDapiToCreativeDataNode } from '@creative/serialization/design-api/document-deserializer';
import {
    compileDapiCreativeMetadata,
    convertCreativeDataNodeToDesignDto
} from '@creative/serialization/design-api/document-serializer';
import { compileDesignApiElements } from '@data/deserialization/design-api/compile-element';
import { getGlobalElementsFromCreativeDataNode } from '@data/deserialization/design-api/sapi-conversion-helpers';
import { CreativeMetadataDapi } from '@domain/api/design-api.interop';
import { CreativeDto, DesignDto, SizeDto, VersionDto } from '@domain/api/generated/design-api';
import { IRenderer } from '@domain/creative/renderer.header';
import { CreativeSize } from '@domain/creativeset';
import {
    IVersionProperty,
    OneOfVersionableProperties,
    VersionableElementProperty,
    VersionedElementProperty,
    VersionPropertyName
} from '@domain/creativeset/version';
import { ICreativeDataNode, OneOfElementDataNodes } from '@domain/nodes';
import { IEditorState } from '@domain/rich-text';
import { CreativeSetMutationService } from '@studio/common/creativeSet/creativeset-mutation.service';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { FeatureService } from '@studio/common/feature/feature.service';
import { convertVersionToDapiVersionDto } from '@studio/common/utils/versions.utils';
import { StudioFeatureFlags } from '@studio/domain/feature-flags/studio.ff.types';
import { distinctArrayById } from '@studio/utils/array';
import { cloneDeep } from '@studio/utils/clone';
import { NotFoundError } from '@studio/utils/errors/apps-errors';
import { uuidv4 } from '@studio/utils/id';
import { isMediaReference } from '@studio/utils/media';
import { ReplaySubject, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { VersionsService } from '../../../shared/versions/state/versions.service';

@Injectable()
export class EditorStateService implements OnDestroy, IEditorState {
    private creativesetDataService = inject(CreativesetDataService);
    private versionsService = inject(VersionsService);
    private featureFlagService = inject(FFFeatureFlagsService);
    private featureService = inject(FeatureService);
    private creativeSetMutationService = inject(CreativeSetMutationService);
    private _renderer$ = new ReplaySubject<IRenderer>(1);
    renderer$ = this._renderer$.asObservable().pipe(filter(v => typeof v !== 'undefined'));
    size: CreativeSize;
    private sizeDto: SizeDto;
    private creativeDto: CreativeDto;

    private defaultVersion: VersionDto;
    private _defaultVersionProperties: IVersionProperty[];
    get defaultVersionProperties(): IVersionProperty[] {
        return this._defaultVersionProperties;
    }

    private selectedVersion: VersionDto;
    get currentVersion(): VersionDto {
        if (!this.selectedVersion) {
            throw new Error('No version selected!');
        }
        return this.selectedVersion;
    }

    private _creativeDataNode: ICreativeDataNode;
    get creativeDataNode(): ICreativeDataNode {
        return this._creativeDataNode;
    }

    zoom = 1;
    sizeIsActive: boolean;

    private _designDto: DesignDto;
    get designDto(): Readonly<DesignDto> {
        return this._designDto;
    }

    get designId(): string {
        return this._designDto.l_DesId || '';
    }

    private _renderer: IRenderer;
    get renderer(): IRenderer {
        return this._renderer;
    }

    creativeMetadata: CreativeMetadataDapi;

    private _creativeChanged$ = new Subject<void>();
    creativeChanged$ = this._creativeChanged$.asObservable();
    isDapiRenderingEnabled = false;

    private unsubscribe$ = new Subject<void>();

    constructor() {
        this.versionsService.selectedVersion$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(selectedVersion => {
                this.selectedVersion = convertVersionToDapiVersionDto(selectedVersion);
                this.setDapiMutationEntities();
            });

        this.versionsService.defaultVersion$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(defaultVersion => {
                this.defaultVersion = convertVersionToDapiVersionDto(defaultVersion);
            });

        this.versionsService.defaultVersionProperties$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(properties => {
                this._defaultVersionProperties = [...properties];
            });

        this.featureFlagService
            .isEnabled$(StudioFeatureFlags.StudioClientDAPIRendering)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(
                renderingEnabled =>
                    (this.isDapiRenderingEnabled =
                        (renderingEnabled || this.featureService.isFeatureEnabled('design-api-put')) &&
                        !!this.creativesetDataService.pristineDapiCreativeSet)
            );
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    async initCreative(creativeId: string): Promise<void> {
        // If design id is specified find the corresponding design in
        // the creative set, else create a new blank design

        const creatives = this.creativesetDataService.creativeset.creatives;
        const creative = creatives.find(({ id }) => id === creativeId);

        if (!creative) {
            const error = new NotFoundError(`Could not find creative`);
            error.id = creativeId;
            throw error;
        }

        if (this.isDapiRenderingEnabled && this.creativesetDataService.pristineDapiCreativeSet) {
            await this.initCreativeWithDapi(creative.id);
            return;
        }

        const size = creative.size;

        const design = creative.design;
        let originalDocumentId: string | undefined;

        if (design) {
            originalDocumentId = design.document.id;
            this.sizeIsActive = true;
            await this.setEditorState(
                {
                    l_DesId: design.id,
                    name: design.name
                } as DesignDto,
                design.document,
                size
            );
        } else {
            const bestDecider = bestBasedOnSize(size).processBestDesignFromSapiCreatives(
                this.creativesetDataService.creativeset
            );
            const creativeDataNode = bestDecider.getClonedCreativeDataNode();
            const bestCreativeDataNode = bestDecider.getBestCreativeDataNode();

            originalDocumentId = bestCreativeDataNode?.id;
            // Assign a new document id.
            this.sizeIsActive = false;
            await this.setEditorState(bestDecider.getBestSizeDto().design!, creativeDataNode, size);
        }

        if (originalDocumentId) {
            this.versionsService.copyStyleIdsBetweenDocuments({
                targetDocumentId: this.creativeDataNode.id,
                sourceDocumentId: originalDocumentId
            });
        }

        const creativeChanged =
            this.creativeMetadata && this.creativeMetadata.id !== Number(creative.id);

        this.creativeMetadata = compileDapiCreativeMetadata(creative);

        if (creativeChanged) {
            this._creativeChanged$.next();
        }
    }

    getCreativeDataNodes(): ICreativeDataNode[] {
        const designs = this.creativesetDataService.creativeset.designs;

        const creativeDataNodes = distinctArrayById([
            this.creativeDataNode,
            ...designs.map(({ document }) => document)
        ]);

        return cloneDeep(creativeDataNodes);
    }

    async initCreativeWithDapi(creativeId: string): Promise<void> {
        const creativeset = this.creativeSetMutationService.getCreativeSet();
        const { creatives } = creativeset;
        const creative = creatives.find(({ id }) => id === +creativeId);

        if (!creative) {
            const error = new NotFoundError(`Could not find creative.`);
            error.id = creativeId;
            throw error;
        }

        await this.setEditorStateFromDapiCreative(creative);

        const creativeChanged = this.creativeMetadata && this.creativeMetadata.id !== creative.id;

        this.creativeMetadata = compileDapiCreativeMetadata(creative);

        if (creativeChanged) {
            this._creativeChanged$.next();
        }
    }

    private async setEditorStateFromDapiCreative(creative: CreativeDto): Promise<void> {
        const creativeSet = this.creativeSetMutationService.getCreativeSet();
        const sizes = creativeSet?.sizes;
        const size = sizes?.find(({ id }) => id === creative.sizeId);

        if (!size || !creativeSet) {
            const error = new NotFoundError(`Could not find size or dapi creativeset.`);
            error.id = creative.sizeId;
            throw error;
        }

        this.sizeDto = size;
        this.creativeDto = creative;

        this.creativeSetMutationService.setCurrentMutationEntities({
            size,
            creative,
            version: this.currentVersion
        });

        this.creativeSetMutationService.setMutationContext({
            context: 'size',
            entity: size
        });

        const creativeSize = {
            id: size.id.toString(),
            name: size.name,
            width: size.width,
            height: size.height
        };

        const sizeDesign = size.design;

        if (sizeDesign) {
            const apiElements = compileDesignApiElements(creativeSet, creative);
            const creativeDataNode = deserializeDapiToCreativeDataNode(
                { ...size, design: sizeDesign },
                apiElements,
                creativeSet.fonts
            );

            await this.setEditorState(sizeDesign, creativeDataNode, creativeSize);
            this.sizeIsActive = true;
            return;
        }

        const bestDecider =
            bestBasedOnSize(creativeSize).processBestDesignFromDapiCreatives(creativeSet);
        const bestSizeDto = bestDecider.getBestSizeDto();
        this.sizeIsActive = false;

        const creativeDataNode = bestDecider.getClonedCreativeDataNode(creative);

        await this.setEditorState(bestSizeDto.design!, creativeDataNode, creativeSize);

        size.design = convertCreativeDataNodeToDesignDto(creativeDataNode, `design-${uuidv4()}`);

        if (bestSizeDto.design?.l_DocId) {
            this.versionsService.copyStyleIdsBetweenDocuments({
                targetDocumentId: this.creativeDataNode.id,
                sourceDocumentId: bestSizeDto.design.l_DocId
            });
        }
    }

    reset(): void {
        this.zoom = 1;
    }

    async setEditorState(
        designDto: DesignDto,
        creativeDataNode: ICreativeDataNode,
        size?: CreativeSize
    ): Promise<void> {
        this._designDto = designDto;

        this.setCreativeDataNode(
            this.isDapiRenderingEnabled ? creativeDataNode : cloneCreativeDocument(creativeDataNode)
        );

        if (size) {
            this.size = size;
        }

        await this.applyWidgetCodeOnWidgetNodes();
    }

    async applyWidgetCodeOnWidgetNodes(): Promise<void> {
        const elements = getGlobalElementsFromCreativeDataNode(this.creativeDataNode);
        await applyWidgetCodeOnWidgetNodes(this.creativeDataNode, elements);
    }

    updateNewElementAsset(
        oldAssetId: string,
        newAssetId: string,
        newURL: string,
        newSize: number
    ): void {
        for (const element of this.creativeDataNode.elements) {
            if (isImageNode(element) && element.imageAsset?.id === oldAssetId) {
                element.imageAsset.url = newURL;
                element.imageAsset.id = newAssetId;
                element.imageAsset.__loading = false;
            } else if (isVideoNode(element) && element.videoAsset?.id === oldAssetId) {
                element.videoAsset.url = newURL;
                element.videoAsset.id = newAssetId;
                element.videoAsset.__loading = false;
                element.videoAsset.fileSize = newSize;
            }

            for (const property of element.globalElement.properties) {
                if (isMediaReference(property) && property.value === oldAssetId) {
                    property.value = newAssetId;
                }
            }
        }
    }

    setCreativeDataNode(creativeDataNode: ICreativeDataNode): void {
        this._creativeDataNode = creativeDataNode;
    }

    setRenderer(renderer: IRenderer): void {
        this._renderer = renderer;
        this._renderer$.next(renderer);
    }

    propertyAsVersionableProperty(
        property: VersionableElementProperty,
        name: VersionPropertyName | string
    ): VersionedElementProperty {
        const versionPropertyId = uuidv4();
        const epv: IVersionProperty = {
            id: versionPropertyId,
            name,
            value: property.value as OneOfVersionableProperties
        };
        this.versionsService.addVersionProperty(this.defaultVersion.id.toString(), epv);
        property.value = '';
        property.versionPropertyId = versionPropertyId;
        return property as VersionedElementProperty;
    }

    upsertDefaultVersionProperty(versionProperty: IVersionProperty): void {
        this._defaultVersionProperties = [
            ...this._defaultVersionProperties.filter(({ id }) => id !== versionProperty.id),
            versionProperty
        ];
    }

    updateVersionProperty(
        versionId: string,
        versionProperty: IVersionProperty<OneOfVersionableProperties>
    ): void {
        this.versionsService.upsertVersionProperty(versionId, versionProperty);
    }

    isDynamicElement(dataNode: OneOfElementDataNodes): boolean {
        return !!getDynamicPropertyOfElement(dataNode.globalElement);
    }

    setCreativeSize(size: Partial<CreativeSize>): void {
        this.size = {
            ...this.size,
            ...size
        };
    }

    private setDapiMutationEntities(): void {
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const { versions, sizes, creatives } = this.creativeSetMutationService.getCreativeSet();
            const version = versions.find(({ id }) => id === this.currentVersion.id);

            if (!version) {
                throw new Error('Version not found.');
            }

            this.selectedVersion = version;

            if (this.sizeDto && this.creativeDto) {
                const size = sizes.find(({ id }) => id === this.sizeDto.id);
                const creative = creatives.find(({ id }) => id === +this.creativeDto.id);

                if (!creative) {
                    throw new Error('Creative not found.');
                }

                if (!size) {
                    throw new Error('Size not found.');
                }

                this.creativeSetMutationService.setCurrentMutationEntities({
                    size,
                    creative,
                    version
                });
            }
        }
    }
}
