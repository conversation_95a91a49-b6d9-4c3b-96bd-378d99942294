import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { KeyframeService } from '@app/design-view/timeline';
import { SentinelService } from '@bannerflow/sentinel';
import { Logger } from '@bannerflow/sentinel-logger';
import {
    addKeyframeWithState,
    createAnimation,
    getAbsoluteTimeBetween,
    getAnimationsOfType,
    hasAnimationsOfType,
    removeKeyframes,
    sortByTime
} from '@creative/animation.utils';
import { parseColor } from '@creative/color.utils';
import { isVersionedText } from '@creative/elements/rich-text/utils';
import {
    createInlineStyledTextFromText,
    createTextFromInlineStyledText,
    isGroupDataNode,
    isGroupNodeDto,
    isTextDataElement,
    isTextNode,
    isWidgetNode,
    toFlatNodeList
} from '@creative/nodes/helpers';
import { ElementSelection } from '@creative/nodes/selection';
import { getStateById } from '@creative/rendering/states.utils';
import {
    cloneCreativeDocument,
    convertNodeToDto,
    deserializeDesignDocument,
    deserializeFeedString,
    deserializeOneOfNodes,
    deserializeShadows,
    deserializeVersionProperties,
    stringifyCreativeDataNode
} from '@creative/serialization';
import {
    deserializeInlineStyledText,
    serializeInlineStyledText
} from '@creative/serialization/text-serializer';
import { serializeVersion } from '@creative/serialization/versions/version-serializer';
import {
    getGlobalElementsFromCreativeDataNode,
    populateGlobalElementsToDataNodes
} from '@data/deserialization/design-api/sapi-conversion-helpers';
import { IAnimation, IAnimationKeyframe } from '@domain/animation';
import { DocumentDto } from '@domain/api/generated/sapi';
import {
    CopyPasteSnapshot,
    ICopyPasteElementSnapshot,
    ICopyPasteKeyframeSnapshot,
    IDirtyContent,
    IDirtyTextContent,
    ISerializedCopy
} from '@domain/copy-paste';
import { ISerializedVersion, IVersion } from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import { IFontFamily } from '@domain/font-families';
import { OneOfDataNodes, OneOfElementDataNodes, OneOfTextViewElements } from '@domain/nodes';
import { OneOfNodesDto, TextLikeElementDto } from '@domain/serialization';
import { IState } from '@domain/state';
import { IStyleIdMap } from '@domain/text';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { FontFamiliesService } from '@studio/common/font-families/font-families.service';
import { EventLoggerService, StoreSnapshotError } from '@studio/monitoring/events';
import { cloneDeep } from '@studio/utils/clone';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionService } from './element-selection.service';

@Injectable()
export class CopyPasteService {
    copySnapshot?: CopyPasteSnapshot;
    private logger = new Logger('CopyPasteService');
    private editorStateService = inject(EditorStateService);
    private eventLoggerService = inject(EventLoggerService);
    private sentinelService = inject(SentinelService);
    private versionsService = inject(VersionsService);
    private creativesetDataService = inject(CreativesetDataService);
    private fontFamiliesService = inject(FontFamiliesService);
    private elementSelectionService = inject(ElementSelectionService);
    private keyframeService = inject(KeyframeService);

    private versions: IVersion[] = [];
    private selectedVersionId: string;
    private fontFamilies: IFontFamily[] = [];

    constructor() {
        this.versionsService.versions$
            .pipe(takeUntilDestroyed())
            .subscribe(versions => (this.versions = versions));

        this.versionsService.selectedVersion$
            .pipe(takeUntilDestroyed())
            .subscribe(version => (this.selectedVersionId = version.id));

        this.fontFamiliesService.creativeSetFontFamilies$
            .pipe(takeUntilDestroyed())
            .subscribe(creativeSetFontFamilies => {
                this.fontFamilies = creativeSetFontFamilies;
            });
    }

    createCopyPasteSnapshot(): void {
        const context = this.elementSelectionService.latestSelectionType;

        if (context === 'element') {
            this.setElementCopySnapshot();
        }

        if (context === 'keyframe') {
            this.setKeyframeCopySnapshot();
        }
    }

    private setElementCopySnapshot(): void {
        const snapshot = this.createCopyPasteElementSnapshot();
        const { selection, creativeDataNode, fontFamilies } = snapshot;
        const selectionNodes = toFlatNodeList(selection.nodes || []);
        const elements = getGlobalElementsFromCreativeDataNode(creativeDataNode);

        const sanitizedNodes = selectionNodes.map(e => this.sanitizeElementObject(e));
        const dirtyContent: IDirtyTextContent = {} as IDirtyTextContent;
        sanitizedNodes?.forEach(el => {
            if (el.dirtyTextContent) {
                dirtyContent[el.node.id] = el.dirtyTextContent;
            }
        });

        const version = this.versions.find(({ id }) => id === this.selectedVersionId);

        if (!version) {
            throw new Error('Could not get version matching selected version id');
        }

        const cleanedSnapshot: ISerializedCopy = {
            brandId: this.creativesetDataService.brand.id,
            document: stringifyCreativeDataNode(creativeDataNode),
            version: serializeVersion(version, this.sentinelService) as ISerializedVersion,
            selectionNodes: sanitizedNodes?.map(el => el.node),
            dirtyTextContent: dirtyContent,
            elements,
            fontFamilies
        };

        try {
            this.copySnapshot = snapshot;
            window.localStorage.setItem('copiedSnapshot', JSON.stringify(cleanedSnapshot));
        } catch (e) {
            this.eventLoggerService.log(new StoreSnapshotError(e as Error), this.logger);
        }
    }

    createCopyPasteElementSnapshot(): ICopyPasteElementSnapshot {
        return {
            context: 'element',
            creativeDataNode: cloneCreativeDocument(this.editorStateService.creativeDataNode),
            selectedVersionId: this.selectedVersionId,
            selection: new ElementSelection(
                this.elementSelectionService.currentSelection.nodesAsSortedArray()
            ),
            versions: this.versions,
            fontFamilies: this.fontFamilies
        };
    }

    private getCopyableKeyframesFromSelection(
        keyframes: Set<IAnimationKeyframe>
    ): IAnimationKeyframe[] {
        const elements = this.editorStateService.creativeDataNode.elements;
        // Only copy keyframes from animation of type keyframe
        return [...keyframes].filter(keyframe =>
            elements.some(el =>
                el.animations.some(
                    ani =>
                        ani.keyframes.findIndex(kf => kf.id === keyframe.id) > -1 &&
                        ani.type &&
                        ['keyframe'].indexOf(ani.type) > -1
                )
            )
        );
    }

    private setKeyframeCopySnapshot(): void {
        const getCopyableKeyframes = this.getCopyableKeyframesFromSelection(
            this.keyframeService.keyframes
        );
        const selectedKeyframes =
            getCopyableKeyframes.length === this.keyframeService.keyframes.size
                ? getCopyableKeyframes.sort(sortByTime)
                : []; // This makes sure we only copy keyframes when all selected keyframes are copyable
        const selectedStates = this.getStatesFromKeyframes([...selectedKeyframes]);

        this.copySnapshot = {
            context: 'keyframe',
            keyframeSelection: {
                keyframes: selectedKeyframes,
                states: selectedStates
            }
        };
    }

    private getStatesFromKeyframes(keyframes: IAnimationKeyframe[]): IState[] {
        const elements = this.editorStateService.creativeDataNode.elements;
        return keyframes.reduce((accumulator: IState[], value) => {
            elements.forEach(el => {
                const state = getStateById(el, value.stateId);
                if (state) {
                    accumulator.push(state);
                }
            });
            return accumulator;
        }, []);
    }

    private sanitizeElementObject(node: OneOfDataNodes): {
        node: OneOfNodesDto;
        dirtyTextContent?: IDirtyContent;
    } {
        if (node && isTextNode(node)) {
            const viewElement =
                this.editorStateService.renderer.getViewElementById<OneOfTextViewElements>(node.id);

            const content = node.__dirtyContent ? node.__dirtyContent : node.content;

            if (!content.style.font) {
                content.style.font = node.font;
            }

            const dirtyContentStyle = content.style
                ? content.style
                : viewElement?.__richTextRenderer?.text_m.style;

            const dirtyTextContent = {
                __dirtyContent: serializeInlineStyledText(createInlineStyledTextFromText(content)),
                __dirtyContentStyle: dirtyContentStyle
            } as IDirtyContent;

            return Object.assign({}, { node: convertNodeToDto(node), dirtyTextContent });
        } else {
            return Object.assign({}, { node: convertNodeToDto(node) });
        }
    }

    // Cannot paste if different brand and element is feed, text, button or widget.
    // due to brandspecific fonts etc..
    /**
     * Return true if the node is allowed to be pasted on other brand
     */
    copyPasteChecker(parsedCopy: ISerializedCopy, brandId: string): boolean {
        if (parsedCopy.brandId === brandId) {
            return true;
        }

        const documentDto = JSON.parse(parsedCopy.document) as DocumentDto;
        const cannotBeCopied = [ElementKind.Button, ElementKind.Text, ElementKind.Widget];

        if (parsedCopy.selectionNodes) {
            for (const nodeDto of parsedCopy.selectionNodes) {
                const node = deserializeOneOfNodes(nodeDto, documentDto);
                const nodeKind = node.kind;
                const disallowedMediaNode =
                    (nodeKind === ElementKind.Image || nodeKind === ElementKind.Video) && node.feed;
                if (cannotBeCopied.includes(nodeKind) || disallowedMediaNode) {
                    return false;
                }
            }
        }

        return true;
    }

    updateCopyFromStorage(
        parsedCopy: ISerializedCopy,
        selectedVersionId: string
    ): ICopyPasteElementSnapshot {
        const parsedDocument = deserializeDesignDocument(parsedCopy.document);
        const copiedElements = parsedCopy.elements;
        populateGlobalElementsToDataNodes(parsedDocument, copiedElements);
        const documentDto = JSON.parse(parsedCopy.document) as DocumentDto;
        const creativeDocument = cloneCreativeDocument(this.editorStateService.creativeDataNode, true);
        const copiedVersion: ISerializedVersion = parsedCopy.version;
        const copiedDefaultVersion: ISerializedVersion | undefined = parsedCopy.defaultVersion;
        const versions: IVersion[] = cloneDeep(this.versions);
        const mappedDirtyContents = parsedCopy.dirtyTextContent;
        const selectionNodes: OneOfDataNodes[] = [];

        for (const node of parsedCopy.selectionNodes || []) {
            const selectionNode = deserializeOneOfNodes(node, documentDto);
            const documentElement = parsedDocument.findNodeById_m(node.id, true);
            const creativeElement = copiedElements.find(({ id }) => id === node.id);

            if (documentElement) {
                if (isTextNode(documentElement) && isTextDataElement(selectionNode)) {
                    const textElementDto = node as TextLikeElementDto;
                    const font = documentElement.font;
                    documentElement.font = font;
                    const dirtyContent = mappedDirtyContents[selectionNode.id];

                    if (dirtyContent) {
                        const inlineStyledText = deserializeInlineStyledText(
                            dirtyContent?.__dirtyContent || ''
                        );
                        let dirtyContentStyle;

                        if (dirtyContent?.__dirtyContentStyle?.textColor) {
                            dirtyContentStyle = Object.assign({}, dirtyContent.__dirtyContentStyle, {
                                textColor: parseColor(dirtyContent.__dirtyContentStyle.textColor)
                            });
                        } else {
                            dirtyContentStyle = dirtyContent?.__dirtyContentStyle;
                        }

                        if (dirtyContent?.__dirtyContentStyle?.textShadows) {
                            for (const textShadow of dirtyContent.__dirtyContentStyle.textShadows) {
                                textShadow.color = parseColor(textShadow.color);
                            }
                        }

                        documentElement.__dirtyContent = createTextFromInlineStyledText(
                            inlineStyledText,
                            dirtyContentStyle
                        );
                    }

                    if (textElementDto.textShadows) {
                        for (let i = 0; i < textElementDto.textShadows.length; i++) {
                            documentElement.textShadows = selectionNode.textShadows || [];
                            documentElement.textShadows[i] = {
                                ...textElementDto.textShadows[i],
                                color: parseColor(textElementDto.textShadows[i].color)
                            };
                        }
                    }
                }

                // update dataelement with properties css, html ,js etc ..
                if (isWidgetNode(documentElement)) {
                    const copiedElement = copiedElements.find(({ id }) => id === node.id);
                    copiedElement!.properties.forEach(
                        property => (documentElement[property.name] = property.value)
                    );

                    const allVersionProperties = [
                        ...parsedCopy.version.properties,
                        ...(parsedCopy.defaultVersion?.properties || [])
                    ];

                    allVersionProperties.forEach(versionProperty => {
                        documentElement.customProperties = documentElement.customProperties.map(
                            customProperty => {
                                if (customProperty.versionPropertyId === versionProperty.id) {
                                    if (customProperty.unit === 'feed') {
                                        customProperty.value = deserializeFeedString(
                                            versionProperty.value
                                        );
                                    } else {
                                        customProperty.value = {
                                            text: JSON.parse(`${versionProperty.value}`).text
                                        };
                                    }
                                }
                                return customProperty;
                            }
                        );
                    });
                }

                if (!isGroupNodeDto(node) && !isGroupDataNode(documentElement)) {
                    if (node.fill) {
                        documentElement.fill = parseColor(node.fill);
                    }

                    if (node.shadows) {
                        documentElement.shadows = deserializeShadows(node.shadows);
                    }
                }

                if (creativeElement!.name) {
                    documentElement.name = creativeElement!.name;
                }

                selectionNodes.push(documentElement);
                if (!documentElement.__parentNode) {
                    creativeDocument.addNode_m(documentElement);
                }
            }
        }

        // find current version
        const currentVersion = versions.find(({ id }) => id === selectedVersionId);

        if (!currentVersion) {
            throw new Error('Selected version not found.');
        }

        for (const copiedElement of copiedElements) {
            for (const property of copiedElement.properties) {
                const propertyId = property.versionPropertyId;
                const versionProperties = deserializeVersionProperties(copiedVersion.properties);

                let copiedVersionProperty = versionProperties.find(({ id }) => id === propertyId);
                if (!copiedVersionProperty && copiedDefaultVersion) {
                    const defaultVersionProperties = deserializeVersionProperties(
                        copiedDefaultVersion.properties
                    );
                    copiedVersionProperty = defaultVersionProperties.find(
                        ({ id }) => id === propertyId
                    );
                }

                if (!copiedVersionProperty) {
                    continue;
                }

                if (isVersionedText(copiedVersionProperty)) {
                    // updating versionproperty with new destination creativedoc id
                    const destinationCreativeDataId = creativeDocument.id;
                    for (const style of copiedVersionProperty.value.styles) {
                        if (Object.keys(style.styleIds).length > 0) {
                            const updatedMap: IStyleIdMap = {};
                            Object.values(style.styleIds).forEach(function (value: string): void {
                                updatedMap[destinationCreativeDataId] = value;
                            });
                            style.styleIds = updatedMap;
                        }
                    }
                }

                // add copied versionproperty to current version.
                currentVersion.properties.push(copiedVersionProperty);
            }
        }

        const selection = new ElementSelection(selectionNodes);

        const snapshot: ICopyPasteElementSnapshot = {
            context: 'element',
            creativeDataNode: parsedDocument,
            selection: selection,
            versions: versions,
            selectedVersionId: selectedVersionId,
            fontFamilies: parsedCopy.fontFamilies
        };

        this.copySnapshot = Object.freeze(snapshot);

        return snapshot;
    }

    pasteKeyframes(
        snapshot: ICopyPasteKeyframeSnapshot,
        targetDataElements: ReadonlyArray<OneOfElementDataNodes>,
        time: number
    ): IAnimationKeyframe[] {
        const { keyframes, states } = cloneDeep(snapshot.keyframeSelection!);

        return targetDataElements.reduce((memo: IAnimationKeyframe[], element) => {
            let animation: IAnimation;

            if (!hasAnimationsOfType(element, 'keyframe')) {
                animation = createAnimation(element);
                element.animations.push(animation);
            } else {
                animation = getAnimationsOfType(element, 'keyframe')[0];
            }

            const timeRelativeToElement = time - element.time;

            const updatedKeyframes = keyframes.reduce(
                (keyframesMemo: IAnimationKeyframe[], kf, index) => {
                    const state = states.find(({ id }) => id === kf.stateId) || {};
                    const newTime =
                        index > 0
                            ? timeRelativeToElement + getAbsoluteTimeBetween(keyframes[0], kf)
                            : timeRelativeToElement;

                    const keyframe = addKeyframeWithState(element, animation, {
                        ...state,
                        ...kf,
                        time: newTime
                    })?.keyframe;

                    if (keyframe) {
                        /**
                         * If the keyframe time is adjusted and is less than the given time then
                         * we remove it as it means that it was adjusted and the copied
                         * structure is no longer the same, most likely due to element overflow.
                         */
                        if (keyframe.time < newTime) {
                            removeKeyframes(element, [keyframe]);
                            return [...keyframesMemo];
                        }

                        return [...keyframesMemo, keyframe];
                    }

                    return keyframesMemo;
                },
                []
            );

            return [...memo, ...updatedKeyframes];
        }, []);
    }
}
