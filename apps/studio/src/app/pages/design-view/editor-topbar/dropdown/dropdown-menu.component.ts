import {
    AfterViewInit,
    Component,
    inject,
    OnD<PERSON>roy,
    OnInit,
    Renderer2,
    ViewChild
} from '@angular/core';
import {
    UIButtonComponent,
    UIDropdownComponent,
    UIDropdownInputComponent,
    UIDropdownItemComponent,
    UINewThemeService
} from '@bannerflow/ui';
import { GridColor, GridSize } from '@domain/grid';
import { UserSettingsService } from '@studio/common/user-settings';
import { GuidelineColor } from '@studio/domain/workspace/guideline.models';
import { getHotkeysAsKeyValueList } from '@studio/hotkeys/hotkeys';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil, withLatestFrom } from 'rxjs/operators';
import { DesignViewComponent } from '../../design-view.component';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { HistoryService } from '../../services/history.service';

@Component({
    selector: 'dropdown-menu',
    templateUrl: './dropdown-menu.component.html',
    styleUrls: ['./dropdown-menu.component.scss'],
    standalone: false
})
export class DropdownMenuComponent implements OnInit, AfterViewInit, OnDestroy {
    private uiNewThemeService = inject(UINewThemeService);

    @ViewChild('customGrid') customGrid: UIDropdownInputComponent;
    @ViewChild('dropdown', { static: true }) dropdown: UIDropdownComponent;
    @ViewChild('saveButton', { static: true }) saveButton: UIButtonComponent;
    @ViewChild('toggleGrid', { static: true }) toggleGridComponent:
        | UIDropdownItemComponent
        | UIDropdownInputComponent;

    GridSize = GridSize;
    GridColor = GridColor;
    GuidelineColor = GuidelineColor;
    height: number;
    dimOutsideCanvas$ = this.userSettingsService.dimOutsideCanvas$;
    outlineVisible$ = this.userSettingsService.outlineVisible$;
    guidelineVisible$ = this.userSettingsService.guidelineVisible$;
    snapping$ = this.userSettingsService.snapping$;
    guidelineColor$ = this.userSettingsService.guidelineColor$;
    grid$: Observable<GridSize>;
    displayGrid$ = this.userSettingsService.displayGrid$;
    gridColor$ = this.userSettingsService.gridColor$;
    useDefaultAnimations$ = this.userSettingsService.animation$.pipe(
        map(animation => animation.useDefaultAnimations)
    );
    previousGuidelinesState = true;
    activeGridComponent: UIDropdownItemComponent | UIDropdownInputComponent;
    activeColorComponent: UIDropdownItemComponent | UIDropdownInputComponent;
    keyboardShortcuts = getHotkeysAsKeyValueList(['Editor', 'Workspace']);
    disableUndo = true;
    disableRedo = true;
    gridSizes: { [key in number]: { size: GridSize } };

    private unsubscribe$ = new Subject<void>();

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    dropdownSize: 'sm' | 'md' = this.isNewUI() ? 'sm' : 'md';
    dropdownWidth: string = this.isNewUI() ? '180' : 'auto';

    constructor(
        public editor: DesignViewComponent,
        private renderer: Renderer2,
        public historyService: HistoryService,
        public userSettingsService: UserSettingsService,
        private editorEventService: EditorEventService
    ) {
        this.historyService.onChange$.pipe(takeUntil(this.unsubscribe$)).subscribe(history => {
            this.disableUndo = history.undos.length === 0;
            this.disableRedo = history.redos.length === 0;
        });
    }

    ngOnInit(): void {
        this.editor.notifyIsPlaying$
            .pipe(
                withLatestFrom(this.userSettingsService.guidelineVisible$),
                takeUntil(this.unsubscribe$)
            )
            .subscribe(([isPlaying, isVisible]) => {
                const gizmoDrawer = this.editor.workspace.gizmoDrawer;
                gizmoDrawer.drawGuideline = isPlaying ? false : isVisible;
                gizmoDrawer.draw();
            });
    }

    ngAfterViewInit(): void {
        this.editorEventService.workspaceInit$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(() => this.initGrid());
        this.height = window.innerHeight;
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    private initGrid(): void {
        this.gridSizes = {
            [GridSize.Grid10]: { size: GridSize.Grid10 },
            [GridSize.Grid20]: { size: GridSize.Grid20 },
            [GridSize.Grid50]: { size: GridSize.Grid50 },
            [GridSize.Grid100]: { size: GridSize.Grid100 },
            [this.getFacebookGridSize()]: {
                size: GridSize.GridFacebook
            }
        };

        this.grid$ = this.userSettingsService.grid$.pipe(
            map(grid => {
                if (!grid) {
                    return GridSize.None;
                }

                const matchingGridSize = this.gridSizes[grid.width];

                if (matchingGridSize) {
                    return this.gridSizes[grid.width].size;
                }

                return GridSize.None;
            })
        );
    }

    private getFacebookGridSize(): number {
        return this.editor.workspace.canvasSize.width / 5;
    }

    leaveSubmenu(el: UIDropdownItemComponent): void {
        this.renderer.removeClass(el._getHostElement(), 'active-dropdown-item');
    }

    openSubmenu(el: UIDropdownItemComponent): void {
        this.renderer.addClass(el._getHostElement(), 'active-dropdown-item');
    }

    exit(): void {
        this.editor.exit();
    }

    undo(): void {
        this.editor.undo();
    }

    redo(): void {
        this.editor.redo();
    }

    openFontManager(): void {
        this.editor.openFontManager();
    }

    toggleOverlay(showOverlay: boolean): void {
        this.userSettingsService.setDesignViewSetting('dimOutsideCanvas', showOverlay);
    }

    toggleOutlines(showOutlines: boolean): void {
        this.userSettingsService.setDesignViewSetting('outlineVisible', showOutlines);
    }

    toggleSnapping(enableSnapping: boolean): void {
        this.userSettingsService.setDesignViewSetting('snapping', enableSnapping);
    }

    toggleGuidelines(showGuidlines: boolean): void {
        this.userSettingsService.setDesignViewSetting('guidelineVisible', showGuidlines);
    }

    setGuidelineColor(guidelineColor: GuidelineColor): void {
        this.userSettingsService.setDesignViewSetting('guidelineColor', guidelineColor);
    }

    setDefaultAnimation(useDefault: boolean): void {
        this.userSettingsService.setAnimationSetting('useDefaultAnimations', useDefault);
    }

    setGrid(grid: GridSize): void {
        const size = grid === GridSize.GridFacebook ? this.getFacebookGridSize() : grid;

        if (typeof size === 'number') {
            this.setGridSize(size);
            this.clearCustomGridInput();
        }
    }

    setCustomGrid(value?: number | string): void {
        let gridSize: number | undefined;

        if (value && typeof value === 'string') {
            gridSize = Number(value);
        }

        if (gridSize === 0) {
            this.toggleGrid(false);
            this.clearCustomGridInput();
            return;
        }

        this.setGridSize(gridSize);
    }

    setGridColor(color: GridColor): void {
        this.userSettingsService.setDesignViewSetting('gridColor', color);
    }

    toggleGrid(enableGrid: boolean): void {
        this.userSettingsService.setDesignViewSetting('displayGrid', enableGrid);
    }

    private setGridSize(size?: number): void {
        const pixels = size ? { width: size, height: size } : undefined;
        this.userSettingsService.setDesignViewSetting('grid', pixels);
        this.userSettingsService.setDesignViewSetting('displayGrid', true);
    }

    private clearCustomGridInput(): void {
        this.customGrid._getHostElement().querySelector('input')!.value = '';
    }
}
