import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    forwardRef,
    inject,
    Inject,
    Input,
    OnD<PERSON>roy,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import { Logger } from '@bannerflow/sentinel-logger';
import { UINotificationService } from '@bannerflow/ui';
import { Color } from '@creative/color';
import {
    DEFAULT_WIDGET_CSS,
    DEFAULT_WIDGET_HTML,
    DEFAULT_WIDGET_JS,
    getWidgetCodeOfElement
} from '@creative/elements/widget/utils';
import { CreativeDataNode } from '@creative/nodes/base-data-node';
import { destroyDiContainer, diForceScope, diInject } from '@di/di';
import { Token } from '@di/di.token';
import { StudioAdData } from '@domain/ad/studio-ad';
import { IBrandLibraryElement, INewBrandLibraryElement } from '@domain/brand/brand-library';
import { IAnimator } from '@domain/creative/animator.header';
import { widgetApiDeclaration } from '@domain/creative/elements/widget/declarations/widget.declaration';
import { WidgetCode } from '@domain/creative/elements/widget/widget-renderer.header';
import { AppView, ICreativeConfig } from '@domain/creative/environment';
import { IRenderer } from '@domain/creative/renderer.header';
import { IVersion } from '@domain/creativeset/version';
import { IPosition, ISize } from '@domain/dimension';
import { ElementKind } from '@domain/elements';
import { IFontFamily } from '@domain/font-families';
import { IHotkeyContext } from '@domain/hotkeys/hotkeys.types';
import { ICreativeDataNode } from '@domain/nodes';
import { IWidgetCustomProperty, IWidgetElementDataNode, IWidgetElementProperty } from '@domain/widget';
import { CreativesetDataService, FontFamiliesService } from '@studio/common';
import { CreativeSetMutationService } from '@studio/common/creativeSet/creativeset-mutation.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { BrowserDefaultHotkeys } from '@studio/hotkeys/hotkeys';
import { createBrandlibraryElement, isNewBrandlibraryElement } from '@studio/utils/element.utils';
import { uuidv4 } from '@studio/utils/id';
import { sleep } from '@studio/utils/promises';
import { emmetHTML } from 'emmet-monaco-es';
import { BehaviorSubject, firstValueFrom, Subject } from 'rxjs';
import { filter, take, takeUntil } from 'rxjs/operators';
import { registerStudioCreativeDependencies } from '../../../../studio-app.dependencies';
import { HotkeyBetterService } from '../../../shared/services/hotkeys/hotkey.better.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { EditElementComponent, File } from '../edit-element';
import { PropertiesService } from '../properties-panel/properties.service';
import { EditorEventService } from '../services/editor-event/editor-event.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementCreatorService } from '../services/element-creator.service';
import { ElementSelectionBoundingBoxService } from '../services/element-selection-bounding-box.service';
import { ElementSelectionService } from '../services/element-selection.service';
import { MutatorService } from '../services/mutator.service';
import { StudioWorkspaceService } from '../workspace/services/studio-workspace.service';
import { StudioWorkspaceComponent } from '../workspace/studio-workspace.component';
import { infiniteLoopDetectorCode } from './infiniteLoopDetector';
import { validateCss, validateHtml, validateTs } from './widget-code-validation';

const EXTRA_LIB_NAME = 'extralib-widget-api';
const DI_CONTAINER_SCOPE = 'WidgetRenderer';

@Component({
    selector: 'widget-editor',
    templateUrl: 'widget-editor.component.html',
    styleUrls: ['widget-editor.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [CreativeSetMutationService],
    standalone: false
})
export class WidgetEditorComponent implements OnInit, OnDestroy {
    @ViewChild('workspace') workspace: StudioWorkspaceComponent;
    @ViewChild('editor', { static: true }) editor: ElementRef;
    @ViewChild('codePreview', { static: true }) codePreview: ElementRef<HTMLElement>;
    @Input() element: Readonly<IBrandLibraryElement | INewBrandLibraryElement>;
    @Input() customProperties: IWidgetCustomProperty[];
    @Output() saveElement = new EventEmitter();
    @Output() editorLoaded = new EventEmitter();
    @Output() toggleDetailsPanel = new EventEmitter<boolean>();
    @Output() codeModelChanged = new EventEmitter();

    previewEnabled = true;
    creativeDataNode: ICreativeDataNode;
    detailsShowing = true;
    hotkeysExclusions: string[];
    touched = false;
    editorViews: ICodeViews = {
        ts: {
            model: undefined,
            state: undefined
        },
        css: {
            model: undefined,
            state: undefined
        },
        html: {
            model: undefined,
            state: undefined
        }
    };
    previewLoading$ = new BehaviorSubject<boolean>(true);
    editorLoading$ = new BehaviorSubject<boolean>(true);

    private initialPreviewLoad = true;
    private previewTimeout?: number;
    private destroyed = false;
    private monacoEditor: typeof monaco.editor;
    private codeEditor: monaco.editor.IStandaloneCodeEditor;
    private extraLibs?: monaco.IDisposable;
    private widgetCustomProperties: IWidgetCustomProperty[];
    private exposedWidgetApi: string;
    private unstrictValidation = false;
    private jsOutput: string;
    private currentEditorView: ICodeViewData;
    private resizeStartPosition = 0;
    private resizeStartWidth = 0;

    /* Workspace settings */
    private canvasSize: ISize;
    private renderer: IRenderer;

    private widget: IWidgetElementDataNode;
    private animator: IAnimator;
    private initialProperties: IWidgetCustomProperty[];
    private codeValidationPromise: PromiseLike<void>;
    private fontFamilies: IFontFamily[] = [];
    private unsubscribe$ = new Subject<void>();
    private selectedVersion: IVersion;
    private propertiesToClean: string[] = [];
    private monacoDisposables: monaco.IDisposable[] = [];

    private logger = new Logger('WidgetEditorComponent');
    private tsWorker: (...uris: monaco.Uri[]) => Promise<monaco.languages.typescript.TypeScriptWorker>;
    private monacoEmmetDispose: () => void;
    private changeDetectorRef = inject(ChangeDetectorRef);
    private hasCodeEvalError = false;

    constructor(
        @Inject(forwardRef(() => EditElementComponent)) private editElement: EditElementComponent,
        /**
         * This is a unique editorState and not shared with the
         * "real" design-view
         */
        private editorStateService: EditorStateService,
        private hotkeyService: HotkeyBetterService,
        private uiNotificationService: UINotificationService,
        private creativesetDataService: CreativesetDataService,
        private editorEvent: EditorEventService,
        private mutatorService: MutatorService,
        private propertiesService: PropertiesService,
        private elementSelectionService: ElementSelectionService,
        private elementSelectionBoundingBoxService: ElementSelectionBoundingBoxService,
        private fontFamiliesService: FontFamiliesService,
        private elementCreatorService: ElementCreatorService,
        private versionsService: VersionsService,
        private environmentService: EnvironmentService,
        private workspaceService: StudioWorkspaceService
    ) {
        /**
         * The following declarations should match with their equivalent interface
         */
        this.exposedWidgetApi = `${widgetApiDeclaration}
            interface Select {
                name: string;
                key: string;
            }
            interface FeedData {
                status: string;
                data: {
                    [key: string]: { value: string | number; targetUrl?: string | null; }
                }[];
                intervalInSeconds: number; // Interval duration represented in seconds;
            }
            interface Feed {
                /*
                * Returns a promise which resolves with {FeedData}
                */
                load: () => Promise<FeedData>;
                id: string;
                data?: FeedData;
                change: (callback: (feedData: FeedData) => void) => void;
            }
            interface ICustomEvent {
                name: string;
                id?: string;
                label?: string;
                value?: number;
            };
            interface ICustomProgressEvent extends ICustomEvent {
                value: number;
            }
            type IFeedDataKeyValueItem = FeedData['data'][number]`;

        this.versionsService.selectedVersion$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(selectedVersion => {
                this.selectedVersion = selectedVersion!;
            });
    }

    async ngOnInit(): Promise<void> {
        this.hotkeysExclusions = ['T', 'B', 'R', 'E', 'W', 'I', 'C'];

        this.mutatorService.widgetEditorPreview = true;

        const context: IHotkeyContext = {
            name: 'WidgetEditor',
            input: window,
            keyDefaultBehaviourExclusions: Object.values(BrowserDefaultHotkeys)
        };
        this.hotkeyService.pushContext(context);
        this.hotkeyService.on('SaveWidget', this.saveWidget);
        this.widgetCustomProperties = this.customProperties;

        this.editElement.codeViewChange
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(file => this.changeCodeView(file));

        this.fontFamiliesService.fontFamilies$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(fontFamilies => (this.fontFamilies = fontFamilies));

        await this.initializePreviewWorkspaceSettings();

        this.editorEvent.elements.immediateChange$.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
            this.renderer.setAllViewElementsValues_m(1);
            this.workspace.gizmoDrawer.draw();
        });

        // Make it hot, otherwise errors occurs when selecting the widget
        this.elementSelectionBoundingBoxService.boundingBox$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
    }

    private loadMonaco(): void {
        const monacoBaseUrl = './assets/monaco-editor/min/vs';

        const onGotAmdLoader = (): void => {
            // Load monaco
            (window as any).require.config({ paths: { vs: monacoBaseUrl } });
            (window as any).require(['vs/editor/editor.main'], () => {
                if (this.destroyed) {
                    return;
                }
                this.initMonaco();
            });
        };

        /**
         * Load AMD loader if necessary
         * This is needed in order to both load Monaco in ng7 and we also get the benefit of
         * the monaco editor being lazyloaded, excluding it from inital bundle.
         * The namespace is imported globally in global.d.ts
         */
        if (!(window as any).require) {
            const monacoStyles = document.createElement('style') as HTMLLinkElement;
            monacoStyles.href = `${monacoBaseUrl}/editor.main.css`;
            monacoStyles.rel = 'stylesheet';
            monacoStyles.type = 'text/css';
            document.head.appendChild(monacoStyles);

            const loaderScript = document.createElement('script');
            loaderScript.type = 'text/javascript';
            loaderScript.src = `${monacoBaseUrl}/loader.js`;
            loaderScript.addEventListener('load', onGotAmdLoader);
            document.body.appendChild(loaderScript);
        } else {
            onGotAmdLoader();
        }
    }

    private initMonaco = async (): Promise<void> => {
        // Validation settings
        monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
            noSemanticValidation: false,
            noSyntaxValidation: false,
            diagnosticCodesToIgnore: [6200]
        });

        // Compiler options
        monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
            target: monaco.languages.typescript.ScriptTarget.ES2016,
            allowNonTsExtensions: true
        });

        monaco.languages.css.cssDefaults.setOptions({
            validate: true,
            lint: {
                duplicateProperties: 'ignore'
            }
        });

        this.monacoEditor = monaco.editor;

        this.monacoEditor.defineTheme('StudioTheme', {
            base: 'vs-dark',
            inherit: true,
            rules: [{ token: 'identifier', foreground: 'DCDCAA' }],
            colors: {
                'editor.background': '#1e1e1e',
                'editor.foreground': '#dcdcdc'
            }
        });

        const extraLibsDisposable = monaco.languages.typescript.typescriptDefaults.onDidExtraLibsChange(
            () => {
                // Triggered by `monaco.editor.createModel` and `monaco.languages.typescript.typescriptDefaults.addExtraLib`;
                if (this.validateCode()) {
                    this.renderPreview();
                }
            }
        );

        this.monacoDisposables.push(extraLibsDisposable);

        this.codeEditor = this.monacoEditor.create(this.editor.nativeElement, {
            language: 'typescript',
            theme: 'StudioTheme',
            fontSize: 16,
            wordWrap: 'on'
        });

        this.editorViews.html.model = this.monacoEditor.createModel('', 'html');
        this.editorViews.css.model = this.monacoEditor.createModel('', 'css');
        this.editorViews.ts.model = this.monacoEditor.createModel('', 'typescript');

        const modelContentChangeDisposable = this.codeEditor.onDidChangeModelContent(() => {
            if (this.validateCode()) {
                this.renderPreview();
            }
        });
        this.monacoDisposables.push(modelContentChangeDisposable);

        this.monacoEmmetDispose = emmetHTML(monaco as any);

        this.codeEditor.addAction({
            id: '1',
            label: 'Widget Documentation',
            precondition: undefined,
            keybindingContext: undefined,
            contextMenuGroupId: '1_links',
            contextMenuOrder: 1,
            run: (): void => {
                window.open(
                    'https://support.bannerflow.com/en/articles/3313404-creative-studio-custom-widgets',
                    '_blank'
                );
            }
        });

        const onDiChangeMarkersDisposable = this.monacoEditor.onDidChangeMarkers(() => {
            this.onMarkersChanged();
            this.renderPreview();
        });
        this.monacoDisposables.push(onDiChangeMarkersDisposable);

        this.codeEditor.addAction({
            id: 'Save',
            label: 'Save',
            precondition: undefined,
            keybindingContext: undefined,
            contextMenuGroupId: '',
            contextMenuOrder: 1,
            keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
            run: (): void => {
                this.saveWidget();
            }
        });

        window.addEventListener('resize', this.onWindowResize);

        this.editorLoading$.next(false);

        (window as any).displayErrorFromWidget = (error: string): void => {
            this.hasCodeEvalError = true;
            this.uiNotificationService.open(error, { type: 'error', autoCloseDelay: 5000 });
        };

        this.tsWorker = await monaco.languages.typescript.getTypeScriptWorker();

        setTimeout(async () => {
            if (this.destroyed) {
                return;
            }
            this.codeEditor.layout();
            this.setExtraLibs(this.widgetCustomProperties);
            await this.initializeCodeViews();
        });
    };

    private setModelView(): void {
        this.codeEditor.setModel(this.editorViews.html.model!);
        this.codeEditor.restoreViewState(this.editorViews.html.state!);
        this.currentEditorView = this.editorViews.html;
    }

    async renderPreview(): Promise<void> {
        this.previewLoading$.next(true);
        const ts = this.editorViews.ts.model!.getLinesContent().join('\n');
        let ignoreSemanticValidation = false;
        if (ts.indexOf('@ts-ignore') > -1) {
            ignoreSemanticValidation = true;
        }

        if (ignoreSemanticValidation !== this.unstrictValidation) {
            monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                noSemanticValidation: ignoreSemanticValidation,
                noSyntaxValidation: false
            });
            this.unstrictValidation = ignoreSemanticValidation;
        }

        this.jsOutput = await this.getJavascript();

        if (!this.previewEnabled) {
            return;
        }

        /**
         * Debounce the preview rendering with 1 second
         * Needed for performance and because it takes ~0.5-1 second for the worker to set error markers
         */
        if (this.previewTimeout) {
            clearTimeout(this.previewTimeout);
        }

        const loadPreview = async (): Promise<void> => {
            this.uiNotificationService.close();
            await firstValueFrom(this.editorLoading$.pipe(filter(loading => !loading)));

            await this.addWidget();
            this.previewLoading$.next(false);
            this.initialPreviewLoad = false;
        };

        if (this.initialPreviewLoad) {
            await loadPreview();
        } else {
            this.previewTimeout = window.setTimeout(loadPreview, 1000);
        }
    }

    private async initializePreviewWorkspaceSettings(): Promise<void> {
        const previewWidth = this.codePreview.nativeElement.clientWidth;
        const previewHeight = this.codePreview.nativeElement.clientHeight;

        this.canvasSize = {
            width: previewWidth,
            height: previewHeight
        };

        this.editorStateService.setCreativeSize(this.canvasSize);

        const creativeFill = new Color();
        creativeFill.alpha = 0;

        await this.editorStateService.setEditorState(
            {
                name: '',
                fill: 'rgba(255, 255, 255, 1)',
                gifExport: {
                    frames: [],
                    show: false
                },
                guidelines: [],
                loops: 0,
                preloadImage: { format: 'jpg', frames: [], quality: 100 }
            },
            new CreativeDataNode({
                ...this.canvasSize,
                fill: creativeFill,
                id: uuidv4()
            })
        );

        this.creativeDataNode = this.editorStateService.creativeDataNode;

        const adData: StudioAdData = {
            version: {
                id: this.selectedVersion.id,
                name: this.selectedVersion.name,
                localization: {
                    id: '0',
                    cultureName: 'gb',
                    cultureCode: 'en',
                    name: 'English'
                }
            },
            size: {
                id: '0',
                ...this.canvasSize
            }
        };

        const env: ICreativeConfig = {
            ...this.environmentService.creativeConfig,
            appView: AppView.DesignView
        };

        diForceScope(DI_CONTAINER_SCOPE);
        registerStudioCreativeDependencies({
            adData,
            brandId: this.creativesetDataService.brand.id,
            env,
            renderer: {
                document: this.editorStateService.creativeDataNode,
                options: {
                    fontFamilies: this.fontFamilies
                }
            }
        });

        this.renderer = diInject(Token.RENDERER);
        this.animator = diInject(Token.ANIMATOR);

        this.mutatorService.workspaceFocused = true;

        this.editorStateService.setRenderer(this.renderer);

        this.elementSelectionService.clearSelection();
        this.elementSelectionService.change$.subscribe(selection => {
            this.propertiesService.dataElementChange$.next(selection.element);
        });
        this.editorEvent.workspaceViewInit$.pipe(take(1)).subscribe(() => {
            this.workspace.renderer = this.renderer;
            this.workspace.transform.workspace = this.workspace;
            this.workspace.setZoom(1);
            this.workspace.gizmoDrawer.drawOutline = true;
            this.workspace.gizmoDrawer.drawOverflow = false;

            this.workspace.initializeCanvas();
            this.workspace.updateBoundingRect();
            this.loadMonaco();
        });

        this.editorEvent.workspaceInit();
        this.changeDetectorRef.detectChanges();
    }

    private async addWidget(): Promise<void> {
        this.hasCodeEvalError = false;

        if (this.widget) {
            this.renderer.destroyElement_m(this.widget);
            this.workspace.deselectAllElements();
        }

        let codeValidationPromiseResolver: (value: void | PromiseLike<void>) => void;
        let codeValidationPromiseRejector: (value: string | PromiseLike<string>) => void;
        this.codeValidationPromise = new Promise<void>((resolve, reject) => {
            codeValidationPromiseResolver = resolve;
            codeValidationPromiseRejector = reject;
        });

        /* Prerender widget on the workspace */
        const ratio = 1;
        const boundingBox = {
            rotation: this.widget ? this.widget.rotationZ || 0 : 0,
            x: this.canvasSize.width / 2,
            y: this.canvasSize.height / 2,
            width: this.widget ? this.widget.width : 200,
            height: this.widget ? this.widget.height : 100
        };

        const html = this.editorViews.html.model!.getLinesContent().join('\n');
        const css = this.editorViews.css.model!.getLinesContent().join('\n');
        const js = this.prepareWidgetJavascript();

        const widgetElement = createBrandlibraryElement({
            id: uuidv4(),
            type: ElementKind.Widget
        });

        const width = boundingBox.width * ratio;
        const height = boundingBox.height * ratio;
        const x = this.canvasSize.width / 2 - width / 2;
        const y = this.canvasSize.height / 2 - height / 2;

        this.widget = await this.elementCreatorService.createWidget(
            {
                kind: ElementKind.Widget,
                html,
                css,
                js,
                name: this.element.name,
                x,
                y,
                width,
                height,
                parentId: this.element && this.element.id,
                locked: false,
                hidden: false,
                duration: 4,
                time: 0,
                mirrorX: false,
                mirrorY: false,
                rotationX: 0,
                rotationY: 0,
                rotationZ: 0,
                originX: 0.5,
                originY: 0.5,
                scaleX: 1,
                scaleY: 1,
                opacity: 1,
                states: [],
                actions: [],
                animations: [],
                customProperties: this.widgetCustomProperties
            },
            { element: widgetElement, inWidgetEditor: true }
        );

        await this.widget.__widget?.isInitialized_m;

        this.selectElement();
        this.updateWorkspaceSize();

        this.propertiesToClean = this.widget.customProperties
            .filter(customProperty => customProperty.versionPropertyId)
            .map(customProperty => customProperty.versionPropertyId!);

        if (this.initialPreviewLoad) {
            setTimeout(() => this.editorLoaded.emit());
        }

        if (this.codeHasCustomMarkers()) {
            this.showCodeValidationNotification();
            codeValidationPromiseRejector!('Code is not properly utilizing Widget.isLoadedPromise');
        }

        codeValidationPromiseResolver!();
        this.changeDetectorRef.detectChanges();
    }

    private showCodeValidationNotification(): void {
        this.uiNotificationService.open(
            'External resources without utilizing `Widget.isLoadedPromise` detected. Problematic code is outlined, read more by hovering your mouse cursor on the marked lines.',
            {
                autoCloseDelay: 10000,
                type: 'error',
                placement: 'top'
            }
        );
    }

    selectElement(): void {
        this.workspace.selectElement(this.widget);

        const position: IPosition = {
            x: this.widget.x,
            y: this.widget.y
        };
        this.mutatorService.setPosition(this.widget, position);
        this.workspace.gizmoDrawer.draw();
    }

    private prepareWidgetJavascript(): string {
        return `
        ${infiniteLoopDetectorCode}
        var widgetCode = infiniteLoopDetector.wrap(\`
        ${this.jsOutput.replace(/\\/g, '\\\\').replace(/`/gi, '\\`').replace(/\$\{/gi, '\\${')}
        //# sourceURL=${this.editorViews.ts.model!.uri.toString()}; \`);
        var errorFunction = window.errorFunction;
        try {
            eval(widgetCode);
        }
        catch(e) {
            if (e.type === 'InfiniteLoopError') {
                var msg = 'A potentially infinite loop was detected and its execution has been cancelled. Please look carefully in your code for ocurrences that could cause this <NAME_EMAIL>.';
                console.error(msg);
            }
            else {
                console.error(e);
                errorFunction("An error in your widget's code has occurred, please check the developer console and review your code.");
            }
        }`.replace(/ {12}/g, '' /* Normalize indenting */);
    }

    private getJavascript = async (): Promise<string> => {
        try {
            const typeScriptWorkerClient = await this.tsWorker(this.editorViews.ts.model!.uri);
            const { outputFiles } = await typeScriptWorkerClient.getEmitOutput(
                this.editorViews.ts.model?.uri.toString() || ''
            );

            return outputFiles[0].text;
        } catch (e) {
            this.logger.log(e);
            return '';
        }
    };

    private onWindowResize = (): void => {
        this.codeEditor.layout();
        this.workspace.centerCanvas();
        this.updateWorkspaceSize();
    };

    async initializeCodeViews(): Promise<void> {
        const defaultCode: Omit<WidgetCode, 'js'> = {
            html: DEFAULT_WIDGET_HTML,
            css: DEFAULT_WIDGET_CSS,
            ts: DEFAULT_WIDGET_JS
        };
        const widgetCode = isNewBrandlibraryElement(this.element)
            ? defaultCode
            : await getWidgetCodeOfElement(this.element);
        this.setCodeModels(widgetCode);
    }

    setCodeModels({ html, ts, css }: Omit<WidgetCode, 'js'>): void {
        this.editorViews.html.model!.setValue(html);
        this.editorViews.ts.model!.setValue(ts);
        this.editorViews.css.model!.setValue(css);

        this.setModelView();
    }

    saveWidget = async (): Promise<void> => {
        try {
            await this.codeValidationPromise;
            this.saveElement.emit(true);
        } catch (e) {
            this.showCodeValidationNotification();
            console.error(e);
        }
    };

    async getWidgetCodeState(): Promise<WidgetCode> {
        await firstValueFrom(this.previewLoading$.pipe(filter(v => v === false)));
        this.jsOutput = await this.getJavascript();
        if (this.hasCodeEvalError) {
            throw new Error('Could not save widget state. Widget code errors on runtime.');
        }
        await sleep(1000);

        if (this.hasCodeValidationErrors() || this.editorViews.css.model?.isDisposed()) {
            throw new Error('Could not save widget state.');
        }

        return {
            html: this.getHtmlCode(),
            css: this.getCssCode(),
            js: this.jsOutput,
            ts: this.getTsCode()
        };
    }

    private getHtmlCode(): string {
        return this.editorViews.html.model?.getLinesContent().join('\n') || '';
    }

    private getCssCode(): string {
        return this.editorViews.css.model?.getLinesContent().join('\n') || '';
    }

    private getTsCode(): string {
        return this.editorViews.ts.model?.getLinesContent().join('\n') || '';
    }

    private hasCodeValidationErrors(): boolean {
        const markers = this.monacoEditor.getModelMarkers({});

        const errors = markers.filter(
            marker =>
                marker.severity > 1 &&
                marker.resource.path.indexOf(EXTRA_LIB_NAME) === -1 &&
                marker.owner !== 'custom'
        );

        if (errors.length > 0) {
            const errorLogs = errors.map(
                ({ code, message, startLineNumber }) =>
                    `(${code}) Error at line ${startLineNumber}. ${message}`
            );
            this.logger.group(`${new Date().toLocaleString()} - Widget errors`);
            for (const error of errorLogs) {
                // eslint-disable-next-line no-console
                console.error(error);
            }
            this.logger.groupEnd();

            return true;
        }

        return false;
    }

    /** Will trigger onDidChangeMarkers if markers are being set (empty array does nothing)
     * Returns `true` if all code is valid
     */
    private validateCode(): boolean {
        return this.validateHtml() && this.validateCss() && this.validateTs();
    }

    private validateHtml(): boolean {
        const model = this.editorViews.html.model!;
        const markers = validateHtml(model);

        this.monacoEditor.setModelMarkers(model, 'custom', markers);

        return markers.length === 0;
    }

    private validateCss(): boolean {
        const model = this.editorViews.css.model!;
        const markers = validateCss(model);

        this.monacoEditor.setModelMarkers(model, 'custom', markers);

        return markers.length === 0;
    }

    private validateTs(): boolean {
        if (!this.widget) {
            return true;
        }

        const model = this.editorViews.ts.model!;
        const isLoadedPromiseExists = !!this.widget.__widget?.isLoadedPromise;
        const markers = validateTs(model, this.widget.customProperties, isLoadedPromiseExists);

        this.monacoEditor.setModelMarkers(model, 'custom', markers);

        return markers.length === 0;
    }

    private codeHasCustomMarkers(): boolean {
        const markers = this.monacoEditor.getModelMarkers({});

        return markers.some(
            marker => marker.severity === monaco.MarkerSeverity.Error && marker.owner === 'custom'
        );
    }

    private onMarkersChanged(): void {
        if (this.hasCodeValidationErrors()) {
            this.uiNotificationService.close();
            this.uiNotificationService.open(
                `Update of the preview was stopped due to errors found in the widget code. Debug logs with related errors will be shown in your browser's developer console.`,
                { type: 'error', autoCloseDelay: 5000 }
            );
        }
    }

    changeCodeView = (file: File): void => {
        this.changeCodeModel(this.editorViews[file.id]);
    };

    private changeCodeModel(editorData: ICodeViewData): void {
        this.currentEditorView = editorData;
        this.codeEditor.setModel(editorData.model!);
        this.codeEditor.restoreViewState(editorData.state!);
    }

    updatePropertyDeclaration(properties: IWidgetCustomProperty[]): void {
        if (typeof this.widgetCustomProperties === 'undefined') {
            this.initialProperties = properties;
        }

        this.checkForTouchedProperties(properties);
        this.widgetCustomProperties = properties;

        this.setExtraLibs(properties);
    }

    private checkForTouchedProperties(properties: IWidgetCustomProperty[]): void {
        if (JSON.stringify(properties) !== JSON.stringify(this.initialProperties)) {
            this.touched = true;
        }
    }

    private setExtraLibs(properties: IWidgetCustomProperty[]): void {
        let propertyInterface = 'interface IProperties {\n';
        for (const property of properties) {
            const type = property.name;
            switch (property.unit) {
                case 'number':
                    propertyInterface += `${type}: number;`;
                    break;
                case 'boolean':
                    propertyInterface += `${type}: boolean;`;
                    break;
                case 'font':
                    propertyInterface += `${type}: IFontProperty;`;
                    break;
                case 'feed':
                    propertyInterface += `${type}: Feed;`;
                    break;
                default:
                    propertyInterface += `${type}: string;`;
                    break;
            }
            propertyInterface += '\n';
        }
        propertyInterface += '}\n';

        // Clean the already existing libs
        if (this.extraLibs) {
            this.extraLibs.dispose();
        }

        this.extraLibs = monaco.languages.typescript.typescriptDefaults.addExtraLib(
            this.exposedWidgetApi + propertyInterface,
            EXTRA_LIB_NAME
        );
    }

    insertProperty(property: IWidgetElementProperty): void {
        const line = this.codeEditor.getPosition();
        let changedView = false;
        if (this.currentEditorView.model !== this.editorViews['ts'].model) {
            this.changeCodeModel(this.editorViews['ts']);
            changedView = true;
        }
        const range = changedView
            ? new monaco.Range(1, 1, 1, 1)
            : new monaco.Range(line!.lineNumber, line!.column, line!.lineNumber, line!.column);
        const id = { major: 1, minor: 1 };
        const text = `Widget.properties.${property.name}${changedView ? '\n' : ''}`;
        const op = { identifier: id, range: range, text: text, forceMoveMarkers: true };
        this.codeEditor.executeEdits('custom-properties', [op]);
        const tsFile = this.editElement.fileTabs.find(file => file.id === 'ts');
        if (!tsFile) {
            this.logger.error('Could not find TypeScript file');
            return;
        }
        this.editElement.codeViewChange.next(tsFile);
    }

    async togglePreview(): Promise<void> {
        this.previewEnabled = !this.previewEnabled;
        if (this.previewEnabled) {
            await this.renderPreview();
        }
        this.updateCodeSize();
    }

    toggleDetails(): void {
        this.detailsShowing = !this.detailsShowing;
        this.toggleDetailsPanel.emit(this.detailsShowing);
    }

    updateCodeSize(): void {
        setTimeout(() => this.codeEditor.layout());
    }

    private updateWorkspaceSize(): void {
        const previewWidth = this.codePreview.nativeElement.clientWidth;
        const previewHeight = this.codePreview.nativeElement.clientHeight;

        this.canvasSize = {
            width: previewWidth,
            height: previewHeight
        };

        this.workspace.setZoom(1);
        this.editorStateService.setCreativeSize(this.canvasSize);
        this.workspace.resize(this.canvasSize, this.widget);
        this.workspace.gizmoDrawer.setCanvasSize();
        this.workspace.centerCanvas();

        if (this.widget.width > this.canvasSize.width) {
            this.mutatorService.setSize(this.widget, {
                width: this.canvasSize.width,
                height: this.widget.height
            });
        }
        if (this.widget.height > this.canvasSize.height) {
            this.mutatorService.setSize(this.widget, {
                width: this.widget.width,
                height: this.canvasSize.height
            });
        }

        this.workspace.updateBoundingRect();
        this.workspace.gizmoDrawer.draw();
    }

    onStartResizePreview(event: MouseEvent): void {
        this.resizeStartPosition = event.pageX;
        this.resizeStartWidth = parseInt(
            window.getComputedStyle(this.codePreview.nativeElement, null).getPropertyValue('width'),
            10
        );
        document.addEventListener('mousemove', this.onResizePreview);
        document.addEventListener('mouseup', this.onStopResizePreview);
    }

    onResizePreview = (event: MouseEvent): void => {
        this.codePreview.nativeElement.style.width = `${
            this.resizeStartWidth + (this.resizeStartPosition - event.pageX)
        }px`;
        this.codeEditor.layout();
        this.updateWorkspaceSize();
    };

    onStopResizePreview = (): void => {
        document.removeEventListener('mousemove', this.onResizePreview);
        document.removeEventListener('mouseup', this.onStopResizePreview);
        this.updateWorkspaceSize();
    };

    disposeModels(): void {
        for (const view in this.editorViews) {
            if (this.editorViews[view].model) {
                this.editorViews[view].model!.dispose();
            }
        }
    }

    ngOnDestroy(): void {
        if (this.propertiesToClean.length) {
            this.versionsService.removeVersionPropertiesByIds(this.propertiesToClean, true);
        }

        if (this.previewTimeout) {
            clearTimeout(this.previewTimeout);
        }

        destroyDiContainer(DI_CONTAINER_SCOPE);
        diForceScope('root');

        this.workspace.deselectAllElements();
        this.hotkeyService.off('SaveWidget', this.saveWidget);
        this.hotkeyService.popContext();
        window.removeEventListener('resize', this.onWindowResize);
        this.uiNotificationService.close();
        this.monacoDisposables.forEach(d => d.dispose());
        this.monacoEmmetDispose && this.monacoEmmetDispose();
        this.codeEditor && this.codeEditor.dispose();
        this.extraLibs && this.extraLibs.dispose();
        this.disposeModels();
        this.renderer.destroy();
        this.animator.destroy();
        this.destroyed = true;

        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }
}

type ICodeEditorViewState = monaco.editor.ICodeEditorViewState;
type ITextModel = monaco.editor.ITextModel;

interface ICodeViews {
    ts: ICodeViewData;
    html: ICodeViewData;
    css: ICodeViewData;
}

interface ICodeViewData {
    state?: ICodeEditorViewState;
    model?: ITextModel;
}

export class WidgetEditorError extends Error {
    name = 'WidgetEditorError';
}
