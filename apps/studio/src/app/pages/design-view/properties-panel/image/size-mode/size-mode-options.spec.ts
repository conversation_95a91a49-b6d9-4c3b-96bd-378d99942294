import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UIModule } from '@bannerflow/ui';
import { createCreativeDataNodeMock } from '@creative/nodes/__tests__/mocks/data-node.mock';
import { ElementKind } from '@domain/elements';
import { ImageSizeMode } from '@domain/image';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock, createElementMock, createElementPropertyMock } from '@mocks/element.mock';
import { createMockPropertiesService } from '@mocks/services/properties-service.mock';
import { defineMatchMedia } from '../../../../../shared/mocks/matchMedia.mock';
import { ElementChangeType } from '@domain/element-change';
import { EditorStateService } from '../../../services/editor-state.service';
import { ElementSelectionService } from '../../../services/element-selection.service';
import { MutatorService } from '../../../services/mutator.service';
import { PropertiesService } from '../../properties.service';
import { SizeModeOptionsComponent } from './size-mode-options.component';

describe('SizeModeOptionsComponent', () => {
    let fixture: ComponentFixture<SizeModeOptionsComponent>;
    let selectionService: ElementSelectionService;
    let propertiesService: PropertiesService;
    let editorStateService: EditorStateService;
    let mutatorService: MutatorService;

    const imageElement = createElementMock(ElementKind.Image, {
        id: '1',
        properties: [createElementPropertyMock({ name: 'dynamicContent' })]
    });
    const image = createDataNodeMock({ kind: ElementKind.Image, id: '1', globalElement: imageElement });

    beforeEach(() => {
        defineMatchMedia();
        TestBed.configureTestingModule({
            declarations: [SizeModeOptionsComponent],
            imports: [UIModule],
            providers: [
                { provide: ChangeDetectorRef, useValue: {} },
                {
                    provide: PropertiesService,
                    useValue: createMockPropertiesService()
                },
                {
                    provide: EditorStateService,
                    useValue: createMockEditorStateService({
                        creativeDataNode: createCreativeDataNodeMock({ elements: [image] }),
                        isDynamicElement: (): boolean => false
                    })
                },
                { provide: ElementSelectionService, useValue: new ElementSelectionService() },
                { provide: MutatorService, useValue: { setImageSettings: jest.fn() } }
            ]
        }).compileComponents();

        editorStateService = TestBed.inject(EditorStateService);
        selectionService = TestBed.inject(ElementSelectionService);
        propertiesService = TestBed.inject(PropertiesService);
        mutatorService = TestBed.inject(MutatorService);

        fixture = TestBed.createComponent(SizeModeOptionsComponent);
        selectionService.clearSelection();
        fixture.detectChanges();
    });

    it('should center image if it has dynamic content', () => {
        jest.spyOn(editorStateService, 'isDynamicElement').mockReturnValue(true);

        image.imageSettings.sizeMode = ImageSizeMode.Fill;

        selectionService.addSelection(image);
        propertiesService.dataElementChange$.next(image);

        expect(mutatorService.setImageSettings).toHaveBeenCalledWith(
            image,
            { x: 0.5, y: 0.5 },
            ElementChangeType.Skip
        );
    });

    it('should not center image if it does not have dynamic content', () => {
        jest.spyOn(editorStateService, 'isDynamicElement').mockReturnValue(false);

        image.imageSettings.sizeMode = ImageSizeMode.Fill;

        selectionService.addSelection(image);
        propertiesService.dataElementChange$.next(image);

        expect(mutatorService.setImageSettings).not.toHaveBeenCalled();
    });
});
