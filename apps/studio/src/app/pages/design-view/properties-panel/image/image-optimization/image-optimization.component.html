@if (isImageOptimized || mixedIO) {
    <div class="divider"></div>
}
<div class="setting">
    @if (isEmployee$ | async) {
        <div class="setting-row auto">
            <div class="setting-label">Image optimization</div>
            <div class="setting-value end">
                <ui-toggle-switch
                    id="interaction-image-optimization-toggle"
                    [interim]="mixedIO"
                    [selected]="isImageOptimized"
                    (selectedChange)="setImageOptimization($event)"></ui-toggle-switch>
            </div>
        </div>
        @if (!isImageOptimized && !mixedIO) {
            @if (isNewUI()) {
                <ui-label
                    type="secondary"
                    class="hint-text"
                    size="sm">
                    <ui-svg-icon
                        icon="none"
                        size="xs"
                        nuiIcon="warning" />
                    Without optimization, this image will increase the weight of your creative, which in
                    turn can lead to ads being blocked or not approved. We recommend using optimization
                    if you have strict creative weight limits.
                </ui-label>
            } @else {
                <div class="hint-text">
                    Without optimization, this image will increase the weight of your creative, which in
                    turn can lead to ads being blocked or not approved. We recommend using optimization
                    if you have strict creative weight limits.
                </div>
            }
        }
    }

    @if (isImageOptimized || mixedIO) {
        <!-- High DPI toggle -->
        <div class="setting-row auto">
            <div class="setting-label">
                2x pixel ratio
                <ui-svg-icon
                    class="info"
                    icon="question-mark"
                    size="xs"
                    nuiIcon="help"
                    [uiTooltipMaxWidth]="'200px'"
                    [uiTooltip]="
                        'Higher pixel ratio will make images look sharper on high resolution screens. Please note that it will also increase the weight of your creative.'
                    "></ui-svg-icon>
            </div>
            <div class="setting-value end">
                <ui-toggle-switch
                    id="interaction-image-high-dpi-toggle"
                    [interim]="mixedHighDpi"
                    [selected]="highDpi"
                    (selectedChange)="setHighDpi($event)"></ui-toggle-switch>
            </div>
        </div>

        <quality-options
            [mixedQualities]="mixedQualities"
            [quality]="(imageQuality$ | async) || 0"
            (qualityChanged)="setImageQuality($event)"></quality-options>
    }
</div>
