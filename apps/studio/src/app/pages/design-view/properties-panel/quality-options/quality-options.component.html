<div class="setting-row wide quality-section">
    <div class="setting-label">
        Quality
        @if (!isVideo) {
            <ui-svg-icon
                class="info"
                size="xs"
                nuiIcon="help"
                icon="question-mark"
                [uiTooltipMaxWidth]="'200px'"
                [uiTooltip]="
                    '85% is recommended. Changing quality will affect the weight of the creative.'
                "></ui-svg-icon>
        }
    </div>
    <div class="setting-value wide col-2 slider">
        <ui-range
            size="xs"
            [min]="1"
            [max]="100"
            [value]="quality || 0"
            (mouseUp)="notifyChange()"
            (valueChange)="updateQuality($event)">
        </ui-range>

        <ui-number-input
            size="xs"
            type="secondary"
            [attr.data-test-id]="isVideo ? 'video-optimization-input' : 'image-optimization-input'"
            unitLabel="%"
            [allowEmpty]="mixedQualities"
            [keyboardEmit]="true"
            [arrowButtons]="!isNewUI()"
            [max]="100"
            [min]="1"
            [placeholder]="mixedQualities ? '-' : opacityPlaceholder"
            [value]="quality"
            (valueChange)="updateQuality($event, true)">
        </ui-number-input>
    </div>
</div>
