import {
    Component,
    Input,
    EventEmitter,
    Output,
    OnChanges,
    ChangeDetectionStrategy,
    inject
} from '@angular/core';
import { PropertiesService } from '../properties.service';
import { UINewThemeService } from '@bannerflow/ui';

@Component({
    selector: 'quality-options',
    templateUrl: './quality-options.component.html',
    styleUrls: ['../common.scss', '../common.new.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class QualityOptionsComponent implements OnChanges {
    private uiNewThemeService = inject(UINewThemeService);

    @Input() quality: number | undefined;
    @Input() mixedQualities: boolean;
    @Input() isVideo: boolean;
    @Output() qualityChanged = new EventEmitter<number>();

    opacityPlaceholder: string;
    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor(private propertiesService: PropertiesService) {
        this.opacityPlaceholder = this.propertiesService.getPlaceholderValue('opacity').toString();
    }

    ngOnChanges(): void {
        if (this.mixedQualities) {
            this.quality = undefined;
        }
    }

    updateQuality(quality: number, notify = false): void {
        this.quality = quality;
        this.mixedQualities = false;

        if (notify) {
            this.notifyChange();
        }
    }

    notifyChange(): void {
        this.qualityChanged.emit(this.quality);
    }
}
