:where(:root:not([data-uinew])) :host {
    .breakdown-wrapper {
        display: flex;
        flex-direction: column;

        > div {
            margin-bottom: 1.6rem;
        }

        ::ng-deep {
            ui-button {
                width: 100%;
                font-size: 1.2rem;

                > div {
                    height: 26px;
                }
            }
        }

        .saveText {
            text-align: center;
        }

        .hidden {
            display: none;
        }
    }

    .circle-wrapper {
        display: flex;
        justify-content: center;
        height: 120px;
        position: relative;

        canvas {
            position: absolute;
        }

        .circle-text {
            color: var(--studio-color-text-second);
            z-index: 99;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            > span {
                margin-bottom: 10px;
            }

            .totalSize {
                color: var(--studio-color-black);
                font-size: 1.6rem;
            }
        }
    }

    .breakdown-container {
        .breakdown-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;

            > div {
                align-items: center;
                display: flex;
            }
        }

        .breakdown-list {
            color: var(--studio-color-text-second);

            > div {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1rem;

                > span {
                    max-width: 70%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }

    .toggleIcon {
        margin-left: 5px;
        cursor: pointer;
    }

    .dot {
        height: 5px;
        width: 5px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }

    .icon {
        margin-right: 5px;
        color: var(--studio-color-grey-86);
    }

    .yellow {
        background-color: var(--studio-color-additional-yellow);
    }

    .red {
        background-color: var(--studio-color-additional-salmon);
    }

    .green {
        background-color: var(--studio-color-additional-aqua);
    }

    .blue {
        background-color: var(--studio-color-additional-sky);
    }

    .purple {
        background-color: var(--studio-color-additional-purple);
    }
}
