:where(:root[data-uinew]) :host {
    .breakdown-wrapper {
        margin-top: var(--nui-space-100);
        margin-bottom: var(--nui-space-200);

        ui-button {
            --width: 100%;
        }

        .saveText {
            text-align: center;
        }

        .hidden {
            display: none;
        }
    }

    .circle-wrapper {
        display: flex;
        justify-content: center;
        height: 153px;
        position: relative;

        canvas {
            position: absolute;
        }

        .circle-text {
            color: var(--nui-text-secondary);
            z-index: 99;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            > div {
                align-items: center;
                display: flex;
                gap: var(--nui-space-050);
            }

            .totalSize {
                color: var(--nui-text-primary);
                font-size: var(--nui-heading-font-size);
                font-weight: var(--nui-font-weight-15000);
                letter-spacing: var(--nui-heading-letter-spacing);
                line-height: var(--nui-heading-line-height);
                margin-bottom: var(--nui-space-100);
            }
        }
    }

    .breakdown-container {
        > div {
            margin-bottom: var(--nui-space-300);
        }
        .breakdown-text {
            display: flex;
            justify-content: space-between;
            font-weight: var(--nui-label-bold-font-weight);

            > div {
                align-items: center;
                display: flex;

                &:first-child {
                    flex-direction: row-reverse;
                }
                &:nth-child(2) {
                    gap: var(--nui-space-100);
                }
            }
        }

        .breakdown-list {
            margin-top: var(--nui-space-200);

            color: var(--nui-text-secondary);

            > div {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1rem;

                > span {
                    max-width: 70%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }

    .toggleIcon {
        cursor: pointer;
    }

    .dot {
        height: 5px;
        width: 5px;
        border-radius: 50%;
        display: inline-block;
    }

    .yellow {
        background-color: #ebb70e;
    }

    .red {
        background-color: #e2a3a4;
    }

    .green {
        background-color: #7ec2b9;
    }

    .blue {
        background-color: #93c1e7;
    }

    .purple {
        background-color: #817baf;
    }

    .help-icon {
        --color: var(--nui-icon-secondary);
    }
}
