import { Component, forwardRef, inject, Inject, Input } from '@angular/core';
import { IGuideline } from '@domain/workspace';
import { DesignViewComponent } from '../../design-view.component';
import { UINewThemeService } from '@bannerflow/ui';

@Component({
    selector: 'guideline-properties',
    templateUrl: 'guideline-properties.component.html',
    styleUrls: ['../common.scss', '../common.new.scss', './guideline-properties.component.scss'],
    standalone: false
})
export class GuidelinePropertiesComponent {
    private uiNewThemeService = inject(UINewThemeService);

    @Input() guideline: IGuideline;

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    get x(): number {
        return Math.round(this.guideline.position.x);
    }
    set x(n: number) {
        this.guideline.position.x = Math.round(n);
    }
    get y(): number {
        return Math.round(this.guideline.position.y);
    }
    set y(n: number) {
        this.guideline.position.y = Math.round(n);
    }

    constructor(
        @Inject(forwardRef(() => DesignViewComponent))
        private editor: DesignViewComponent
    ) {}

    updatePosition(): void {
        this.editor.workspace.gizmoDrawer.draw();
    }
}
