:where(:root:not([data-uinew])) :host {
    section {
        border-top: solid 1px var(--studio-color-border-second);
    }

    .setting {
        margin: 0;
    }

    .property-input {
        max-width: 60px;

        .rotate-text {
            font-size: 10px;
            opacity: var(--default-disabled-opacity);
            color: var(--studio-color-primary);
            cursor: pointer;
            text-align: end;
        }

        &.slider {
            max-width: 81px;
        }

        &.end {
            justify-self: end;
        }
    }

    .setting-value {
        &.size {
            grid-template-columns: 54px auto 54px;
            grid-gap: 4px;
        }

        .ratio-lock {
            color: red;
            cursor: pointer;

            .icon {
                font-size: 14px;
            }

            &.active {
                color: var(--studio-color-blue);
            }
        }
    }
}
