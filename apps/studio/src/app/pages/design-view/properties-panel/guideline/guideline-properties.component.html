<studio-ui-section
    headline="Guideline"
    [isNewUI]="isNewUI()">
    <div class="setting padding-top">
        <!-- Position -->
        <div class="setting-row">
            <div class="setting-label">Position</div>
            <div
                class="setting-value"
                [class.col-2]="!isNewUI()"
                [class.number-setting]="isNewUI()">
                <div class="property-input">
                    @if (guideline.type === 'vertical') {
                        <ui-number-input
                            size="xs"
                            type="secondary"
                            [(value)]="x"
                            (valueChange)="updatePosition()"
                            [arrowButtons]="!isNewUI()"
                            [allowEmpty]="false"
                            [disableUndo]="true"
                            (submit)="updatePosition()"
                            unitLabel="X"></ui-number-input>
                    }
                </div>
                <div class="property-input">
                    @if (guideline.type === 'horizontal') {
                        <ui-number-input
                            size="xs"
                            type="secondary"
                            [(value)]="y"
                            (valueChange)="updatePosition()"
                            [arrowButtons]="!isNewUI()"
                            [allowEmpty]="false"
                            [disableUndo]="true"
                            (submit)="updatePosition()"
                            unitLabel="Y"></ui-number-input>
                    }
                </div>
            </div>
        </div>
    </div>
</studio-ui-section>
