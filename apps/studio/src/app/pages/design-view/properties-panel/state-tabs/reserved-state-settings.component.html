<studio-ui-section
    headline="State in transition"
    [clickableHeader]="false"
    class="no-padding"
    [isNewUI]="isNewUI()"
    [divider]="true">
    <div class="setting state-in">
        <div class="setting-row">
            <div class="setting-label">Duration</div>
            <div
                class="setting-value col-2 wide"
                [class.slider]="isNewUI()">
                <div
                    class="property-input slider"
                    [class.slider]="!isNewUI()">
                    <ui-range
                        size="xs"
                        [(value)]="inDuration"
                        (valueChange)="onDurationChange('in')"
                        (mouseup)="onDurationChange('in', true)"
                        [min]="0"
                        [max]="10"
                        [step]="0.01"></ui-range>
                </div>
                <div class="property-input">
                    <ui-number-input
                        size="xs"
                        type="secondary"
                        [arrowButtons]="!isNewUI()"
                        [(value)]="inDuration"
                        (valueChange)="onDurationChange('in', true)"
                        [keyboardEmit]="true"
                        [min]="0"
                        [max]="10"
                        [step]="0.1"
                        [allowEmpty]="false"
                        [disableUndo]="true"></ui-number-input>
                </div>
            </div>
        </div>
        <div class="setting-row">
            <div class="setting-label">Easing</div>
            <div class="setting-value select">
                <div class="property-input">
                    <ui-select
                        size="xs"
                        type="secondary"
                        [(selected)]="inEasing"
                        (selectedChange)="onEasingChange('in')"
                        [useTargetWidth]="true">
                        @for (timingFunction of timingFunctions | keyvalue; track timingFunction.key) {
                            <ui-option [value]="timingFunction.key"
                                >{{ timingFunction.value.name }}
                            </ui-option>
                        }
                    </ui-select>
                </div>
            </div>
        </div>
    </div>
</studio-ui-section>
<studio-ui-section
    headline="State out transition"
    [clickableHeader]="false"
    class="no-padding"
    [isNewUI]="isNewUI()"
    [divider]="true">
    <div class="setting state-out">
        <div class="setting-row">
            <div class="setting-label">Duration</div>
            <div
                class="setting-value col-2 wide"
                [class.slider]="isNewUI()">
                <div
                    class="property-input slider"
                    [class.slider]="!isNewUI()">
                    <ui-range
                        size="xs"
                        [(value)]="outDuration"
                        (valueChange)="onDurationChange('out')"
                        (mouseup)="onDurationChange('out', true)"
                        [min]="0"
                        [max]="10"></ui-range>
                </div>
                <div class="property-input">
                    <ui-number-input
                        size="xs"
                        type="secondary"
                        [arrowButtons]="!isNewUI()"
                        [(value)]="outDuration"
                        (valueChange)="onDurationChange('out', true)"
                        [keyboardEmit]="true"
                        [min]="0"
                        [max]="10"
                        [step]="0.1"
                        [allowEmpty]="false"
                        [disableUndo]="true"></ui-number-input>
                </div>
            </div>
        </div>
        <div class="setting-row">
            <div class="setting-label">Easing</div>
            <div class="setting-value select">
                <div class="property-input">
                    <ui-select
                        size="xs"
                        type="secondary"
                        [(selected)]="outEasing"
                        (selectedChange)="onEasingChange('out')"
                        [useTargetWidth]="true">
                        @for (timingFunction of timingFunctions | keyvalue; track timingFunction.key) {
                            <ui-option [value]="timingFunction.key"
                                >{{ timingFunction.value.name }}
                            </ui-option>
                        }
                    </ui-select>
                </div>
            </div>
        </div>
    </div>
</studio-ui-section>
