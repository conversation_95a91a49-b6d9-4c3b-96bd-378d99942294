:where(:root:not([data-uinew])) :host {
    --state-tab-padding: 1rem;

    position: relative;
    overflow: hidden;
    display: block;
    z-index: 999;
    background: var(--studio-color-grey-98);
    width: 22rem;
    padding-bottom: 3px;

    ::ng-deep {
        .input .input {
            text-shadow: inherit;

            &:hover {
                background: transparent !important;
            }

            &:focus {
                background: var(--studio-color-surface) !important;
            }

            &[readonly] {
                color: inherit;
                pointer-events: none;
                background-color: transparent;
            }
        }
    }

    .disabled {
        .element-name {
            max-width: 100%;
            width: 100%;
        }

        .default-state {
            width: 100%;
        }
    }

    .element-name {
        max-width: 65px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .margin-left-light {
        margin-left: 0.4rem;
    }

    .default-state {
        display: flex;
        cursor: pointer;

        .icon {
            margin-right: 8px;
        }
    }

    .toggle-action {
        color: var(--studio-color-blue);
        font-size: 10px;
        text-align: right;
        justify-self: end;
        cursor: pointer;
        white-space: nowrap;
    }

    .selected-state {
        padding: 0 0.4rem;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .tabs-wrapper {
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        height: 100%;

        .tabs {
            display: flex;
            flex-direction: column;
            height: 100%;

            .tab {
                color: var(--studio-color-text);
                align-items: center;
                padding: 0.3rem var(--state-tab-padding);
                display: flex;
                cursor: pointer;
                margin: 0 0;

                &.selected {
                    text-shadow: 0 0 0.6px;

                    .has-linked-action,
                    .placeholder,
                    .input {
                        color: var(--studio-color-text);
                        text-shadow: 0 0 0.6px;
                    }
                }

                &.pristine {
                    color: var(--studio-color-text-second);
                }

                &.add {
                    color: var(--studio-color-text-second);
                }

                &:hover {
                    color: var(--studio-color-text);
                    background: var(--studio-color-background);

                    .delete {
                        display: block;
                    }
                }

                .has-linked-action {
                    color: var(--studio-color-text-second);
                }

                .placeholder {
                    flex: 0 1 auto;
                    padding: 0.7rem;
                    line-height: calc(22px - (0.7rem * 2));
                    border: 1px solid transparent;

                    & ~ .icon {
                        font-size: 14px;

                        &.action {
                            margin-right: 1px;
                            margin-left: auto;
                            text-align: right;
                        }
                    }

                    &:focus {
                        background-color: var(--studio-color-surface);
                        box-shadow: 0 0 0 -1px var(--studio-color-border-second);
                    }
                }

                &.custom {
                    &.active {
                        width: auto;
                        height: auto;
                        position: static;
                        visibility: visible;
                    }

                    .input {
                        flex: 0 1 auto;
                        border: solid 1px var(--studio-color-border-second);
                    }
                }

                .delete {
                    display: none;
                    font-size: 14px;
                    color: var(--studio-color-text-second);
                    margin-right: 1px;
                    margin-left: auto;

                    &:hover {
                        color: var(--studio-color-text);
                    }
                }
            }
        }
    }

    .resize {
        transition: all 0.2s;
        border-bottom: 1px solid var(--studio-color-border-second);
        border-top: 1px solid var(--studio-color-border-second);
        height: 3px;
        width: calc(100% + (var(--state-tab-padding) * 2));

        &:hover {
            border-top-color: var(--studio-color-transparent-primary-04);
        }
    }
}
