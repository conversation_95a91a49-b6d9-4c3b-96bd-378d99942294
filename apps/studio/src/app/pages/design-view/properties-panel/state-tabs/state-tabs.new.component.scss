:where(:root[data-uinew]) :host {
    position: relative;
    display: block;
    z-index: 999;
    width: 100%;
    border-bottom: 1px solid var(--nui-border-neutral-secondary-bold);
    background-color: var(--nui-surface-neutral-subtlest);

    studio-ui-section {
        --header-padding: var(--nui-space-300);
    }
    .tabs-wrapper {
        width: 100%;
        border-top: 1px solid var(--nui-border-neutral-subtle);
        .tabs {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            .tab {
                align-items: center;
                display: flex;
                width: 100%;
                justify-content: space-between;
                padding: var(--nui-space-100) var(--nui-space-100) var(--nui-space-100)
                    var(--nui-space-700);
                --color: var(--nui-text-primary);
                height: 2.8rem;
                .delete {
                    display: none;
                }
                ui-input {
                    max-width: 100%;
                }
                ui-label {
                    margin-left: var(--nui-space-100);
                }
                &.pristine {
                    --color: var(--nui-text-secondary);
                }
                &:hover:not(.add) {
                    background-color: var(--nui-fill-brand-secondary-subtler);
                    .delete {
                        display: block;
                    }
                }
                &.selected {
                    padding-left: var(--nui-space-100);
                    --color: var(--nui-text-primary);

                    &.custom-state {
                        .custom-state-name-input {
                            cursor: text;
                        }
                        ui-input {
                            --input-color: var(--nui-text-primary);
                        }
                    }
                    &.focused ui-input {
                        pointer-events: auto;
                    }
                }
                &.add {
                    padding: var(--nui-space-100) var(--nui-space-200) var(--nui-space-100)
                        var(--nui-space-200);
                    height: 4rem;
                }
                &.custom-state {
                    .custom-state-name {
                        display: flex;
                        align-items: center;
                        min-width: 3rem;

                        .custom-state-name-input {
                            min-width: 2rem;
                        }
                    }
                    &.selected {
                        padding-left: var(--nui-space-200);
                    }
                    ui-input {
                        pointer-events: none;
                    }
                }
                &.custom {
                    justify-content: flex-start;
                }

                ui-input {
                    --input-color: var(--nui-text-secondary);
                }
            }
        }
        .divider {
            border-top: 1px solid var(--nui-divider-fill-primary-default);
        }
    }
}
