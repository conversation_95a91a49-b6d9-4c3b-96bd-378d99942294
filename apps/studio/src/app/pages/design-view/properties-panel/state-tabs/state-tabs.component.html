<studio-ui-section
    [headline]="dataElement ? dataElement.name : ''"
    [headlineIcon]="icon"
    [nuiHeadlineIcon]="nuiIcon"
    [collapsable]="collapsable"
    [collapsed]="collapsed && !stateInputVisible"
    [clickableHeader]="!disabled && collapsable"
    [headlineTooltipDisabled]="!isNewUI()"
    [inactive]="false"
    [empty]="disabled"
    #sectionWrapper
    [isNewUI]="isNewUI()"
    [customAction]="disabled ? undefined : collapsable ? hasState : hasNoState"
    [actions]="
        isNewUI() && !collapsable
            ? [
                  {
                      id: 'add-state-button',
                      nuiIcon: 'add',
                      text: 'Add State',
                      dropdownTarget: dropdown,
                      hidden: disabled
                  }
              ]
            : []
    "
    [inset]="!collapsable"
    class="no-padding">
    @if (!disabled) {
        <div class="tabs-wrapper ui-scrollbar">
            <div class="tabs">
                @if (isNewUI()) {
                    <div
                        id="state-add-button"
                        class="tab add">
                        <ui-label
                            type="secondary"
                            size="sm"
                            >States</ui-label
                        >
                        <ui-button
                            type="ghost-primary"
                            size="xs"
                            [uiDropdownTarget]="dropdown"
                            nuiSvgIcon="add"
                            text="Add state" />
                    </div>
                } @else {
                    <div
                        id="state-add-button"
                        class="tab add"
                        #addStateButton
                        (click)="triggerAddStateButton($event)">
                        <ui-icon
                            icon="checkmark"
                            [style.visibility]="'hidden'"></ui-icon>
                        <div class="placeholder">States</div>
                        <ui-svg-icon
                            id="state-add-custom-button"
                            class="icon action"
                            icon="plus-small"
                            [uiDropdownTarget]="dropdown"
                            [style.width]="addStateIconWidth + 'px'"
                            (click)="addCustomState($event, true)"></ui-svg-icon>
                    </div>
                }

                <div
                    id="state-select-default"
                    class="tab"
                    [class.selected]="!selectedState"
                    (click)="deselect()">
                    @if (isNewUI()) {
                        <ui-label
                            [type]="!selectedState ? 'primary' : 'secondary'"
                            [weight]="!selectedState ? 'bold' : 'regular'"
                            [leadingIcon]="!selectedState ? 'check' : undefined"
                            size="sm">
                            Default
                        </ui-label>
                    } @else {
                        <ui-icon
                            icon="checkmark"
                            [style.visibility]="!selectedState ? 'visible' : 'hidden'"></ui-icon>
                        <div class="placeholder">Default</div>
                    }
                </div>
                @for (state of reservedStates; track state.id) {
                    <div
                        class="tab reserved-state"
                        [id]="'state-select-' + state.name"
                        [attr.data-test-id]="'state-tab-' + state.name"
                        [class.selected]="state === selectedState"
                        [class.pristine]="stateIsPristine(state)"
                        (click)="selectState(state.id)">
                        @if (isNewUI()) {
                            <ui-label
                                [weight]="state === selectedState ? 'bold' : 'regular'"
                                [leadingIcon]="state === selectedState ? 'check' : undefined"
                                [trailingIcon]="stateHasAction(state) ? 'bolt' : undefined"
                                size="sm">
                                {{ state.name }}
                            </ui-label>
                            <ui-button
                                id="state-delete"
                                data-test-id="state-delete"
                                class="delete"
                                type="plain-primary-destructive"
                                size="sm"
                                nuiSvgIcon="delete"
                                (click)="deleteState(state)" />
                        } @else {
                            <ui-icon
                                icon="checkmark"
                                [style.visibility]="state === selectedState ? 'visible' : 'hidden'">
                            </ui-icon>
                            <div class="placeholder">{{ state.name }}</div>
                            @if (stateHasAction(state)) {
                                <ui-svg-icon
                                    class="has-linked-action icon"
                                    icon="de-action"></ui-svg-icon>
                            }
                            <ui-svg-icon
                                id="state-delete"
                                data-test-id="state-delete"
                                class="delete"
                                icon="delete"
                                (click)="deleteState(state)"></ui-svg-icon>
                        }
                    </div>
                }
                @for (state of customStates; track state; let indexOfElement = $index) {
                    <div
                        class="tab custom-state"
                        [id]="'state-select-custom-' + indexOfElement"
                        [class.selected]="state === selectedState"
                        [class.pristine]="stateIsPristine(state)"
                        [class.divider]="indexOfElement === 0"
                        [class.focused]="focusedOnStateName"
                        (click)="selectState(state.id)">
                        @if (isNewUI()) {
                            <div
                                class="custom-state-name"
                                (click)="focusedOnStateName = true">
                                @if (state === selectedState) {
                                    <ui-svg-icon
                                        size="sm"
                                        icon="none"
                                        nuiIcon="check" />
                                }
                                <div class="custom-state-name-input">
                                    <ui-input
                                        size="xs"
                                        nuiType="transparent"
                                        [discrete]="true"
                                        #stateName
                                        [value]="state.name"
                                        [blurOnSubmit]="true"
                                        [dynamicWidth]="true"
                                        (submit)="saveStateName(stateName.value!)"
                                        (blur)="onBlur()" />
                                </div>
                                @if (stateHasAction(state)) {
                                    <ui-svg-icon
                                        size="sm"
                                        icon="none"
                                        nuiIcon="bolt" />
                                }
                            </div>
                            <ui-button
                                id="state-delete"
                                data-test-id="state-delete"
                                class="delete"
                                type="plain-primary-destructive"
                                size="sm"
                                nuiSvgIcon="delete"
                                (click)="deleteState(state)" />
                        } @else {
                            <ui-icon
                                icon="checkmark"
                                [style.visibility]="
                                    state === selectedState ? 'visible' : 'hidden'
                                "></ui-icon>
                            <ui-input
                                #stateName
                                [value]="state.name"
                                [disabled]="state !== selectedState"
                                [blurOnSubmit]="true"
                                [superDiscrete]="true"
                                [dynamicWidth]="true"
                                (submit)="saveStateName(stateName.value!)"
                                (blur)="onBlur()"></ui-input>
                            <ui-button
                                icon="checkmark"
                                class="margin-left-light"
                                (click)="saveStateName(stateName.value!)"
                                [style.visibility]="
                                    stateName.value === state.name ? 'hidden' : 'visible'
                                "></ui-button>
                            @if (stateHasAction(state)) {
                                <ui-svg-icon
                                    class="has-linked-action icon"
                                    icon="de-action"></ui-svg-icon>
                            }
                            <ui-svg-icon
                                class="delete"
                                icon="delete"
                                data-test-id="state-delete"
                                (click)="deleteState(state)"></ui-svg-icon>
                        }
                    </div>
                }
                @if (stateInputVisible) {
                    <div
                        class="tab custom"
                        [class.active]="stateInputVisible">
                        @if (isNewUI()) {
                            <ui-input
                                #customStateInput
                                size="xs"
                                nuiKind="transparent"
                                [discrete]="true"
                                (keydown.Escape)="cancelInput($event)"
                                (submit)="addState(customStateInput.value || '')" />
                            <ui-button
                                type="plain-primary"
                                size="sm"
                                nuiSvgIcon="check"
                                (click)="addState(customStateInput.value || '')" />
                        } @else {
                            <ui-icon
                                icon="checkmark"
                                [style.visibility]="'hidden'"></ui-icon>
                            <ui-input
                                #customStateInput
                                [superDiscrete]="true"
                                (keydown.Escape)="cancelInput($event)"
                                (submit)="addState(customStateInput.value || '')">
                            </ui-input>
                            <ui-button
                                icon="checkmark"
                                class="margin-left-light"
                                (click)="addState(customStateInput.value || '')"></ui-button>
                        }
                    </div>
                }
            </div>
        </div>
    }
</studio-ui-section>
@if (!disabled && !sectionWrapper.collapsed) {
    <div
        class="resize bottom-line resize-90"
        (mousedown)="beginResize($event)"></div>
}

<ui-dropdown
    size="sm"
    #dropdown
    [offset]="placeDropdown()"
    [width]="isNewUI() ? '125' : 'auto'"
    type="menu"
    [disabled]="stateInputVisible || (hasReservedPressed && hasReservedHover)">
    <ui-dropdown-item
        id="add-hover-state"
        (click)="addReservedActionState('hover')"
        [disabled]="hasReservedHover">
        Add Hover State
    </ui-dropdown-item>
    <ui-dropdown-item
        id="add-pressed-state"
        (click)="addReservedActionState('pressed')"
        [disabled]="hasReservedPressed">
        Add Pressed State
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    <ui-dropdown-item
        id="add-custom-state"
        (click)="addCustomState($event)">
        Add Custom State
    </ui-dropdown-item>
</ui-dropdown>

<ng-template #hasState>
    @if (isNewUI()) {
        @let stateName = selectedState ? selectedState.name : 'Default';
        <ui-label
            [uiTooltip]="stateName"
            size="sm"
            >{{ stateName }}</ui-label
        >
    } @else {
        <span
            [uiTooltip]="selectedState ? (selectedState.name ? selectedState.name : '') : 'Default'"
            class="selected-state disabled"
            >{{ selectedState ? selectedState.name : 'Default' }}</span
        >
    }
</ng-template>

<ng-template #hasNoState>
    @if (!isNewUI()) {
        @if (!dataElement.hidden) {
            <span
                class="toggle-action"
                id="add-state"
                [uiDropdownTarget]="dropdown"
                [style.width]="customActionWidth + 'px'"
                >Add State</span
            >
        }
    }
</ng-template>
