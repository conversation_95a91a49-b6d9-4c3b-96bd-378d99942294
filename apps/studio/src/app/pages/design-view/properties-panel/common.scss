:where(:root:not([data-uinew])) :host {
    ::ng-deep .color-picker-outlet {
        margin: var(--section-padding) calc(-1 * var(--section-padding))
            calc(-1 * var(--section-padding)) calc(-1 * var(--section-padding));
        display: block;

        &:not(:last-child) {
            margin-bottom: var(--section-padding);
        }

        &:empty {
            display: none;
        }
    }

    .color-picker {
        display: block;
        margin: var(--section-padding) calc(-1 * var(--section-padding))
            calc(-1 * var(--section-padding)) calc(-1 * var(--section-padding));

        &:not(:last-child) {
            margin-bottom: var(--section-padding);
        }

        &:empty {
            display: none;
        }
    }

    .remove-button,
    .settings-button,
    .menu-button {
        cursor: pointer;
        justify-self: end;
        color: var(--studio-color-grey-84);

        &.active {
            color: var(--studio-color-grey-21);
        }
    }

    .setting {
        margin: 1rem;

        asset-property {
            margin-bottom: 1rem;
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    .divider {
        border-top: 1px solid var(--studio-color-border-second);
    }

    .setting-row {
        display: grid;
        grid-template-columns: 4.8rem auto;
        min-width: 100%;
        max-width: 100%;
        grid-gap: 1rem;
        align-items: center;
        margin-bottom: 1rem;
        overflow: hidden;
        min-height: 22px;

        &.reverse {
            grid-template-columns: 1fr auto;
        }

        .ui-select {
            background-color: var(--studio-color-white);
        }

        .select {
            .ui-select {
                width: 100%;

                ::ng-deep {
                    > .button {
                        min-width: unset;
                    }
                }
            }
        }

        &.narrow {
            grid-gap: 5px;
        }

        &.color,
        &.auto {
            grid-template-columns: repeat(2, auto);
        }

        &.wide {
            grid-gap: 1rem;
            grid-template-columns: 60px 1fr;
        }

        &.with-icon,
        &.long-label {
            grid-gap: 1rem;
            grid-template-columns: auto 1fr;
        }

        &.alternate {
            grid-gap: 1rem;
            grid-template-columns: 60px 1fr;
        }

        &.collapsed {
            grid-template-columns: 1fr 23px;
        }

        &.disabled {
            pointer-events: none;
        }

        &.inactive,
        &.disabled {
            opacity: 0.3;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 1;
            }
        }
    }

    .setting-value {
        display: grid;
        grid-gap: 1rem;
        align-items: center;

        &.end {
            justify-content: end;
        }

        &.col-1 {
            grid-template-columns: auto;
            grid-gap: 0;
        }

        &.margin-bottom {
            margin-bottom: 2px;
        }

        &.col-2 {
            grid-template-columns: repeat(2, auto);

            &.auto {
                grid-template-columns: repeat(2, auto);
            }

            &.size {
                grid-template-columns: 5.4rem auto;
                grid-gap: 1rem;
            }

            &.wide {
                grid-template-columns: 75px auto;
            }

            &.rotation {
                grid-template-columns: 6rem auto;
                grid-gap: 2rem;
            }

            .button-group {
                height: 22px;
                width: 54px;
            }
        }

        &.col-3 {
            grid-template-columns: repeat(3, auto);

            &.shadow {
                grid-template-columns: 4.4rem 10px auto 7px auto;
                grid-gap: unset;
            }

            &.slider {
                grid-template-columns: 4.4rem auto 4.4rem;
            }

            &.filter {
                grid-template-columns: auto 45px 1rem;
            }
        }

        &.col-4 {
            grid-gap: 4px;
            grid-template-columns: repeat(4, auto);

            &.input {
                grid-template-columns: 4.4rem 1rem 7.6rem 1rem 4rem 0.7rem auto;
                grid-gap: unset;
            }

            &.slider {
                grid-gap: unset;
                grid-template-columns: 4.4rem 1rem 7.6rem 1rem 4rem 0.7rem auto;
            }
        }
    }

    .setting-label {
        display: flex;
        align-items: center;

        .info {
            color: var(--studio-color-text-third);
            font-size: 1em;
            margin-left: 6px;
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: var(--studio-color-text-second);
            }
        }
    }

    .property-input {
        display: flex;

        &.col-4 {
            display: grid;
            grid-template-columns: repeat(4, auto);
        }

        &.col-3 {
            display: grid;
            grid-template-columns: repeat(3, 22px);
        }
    }

    .input-group-text {
        font-size: 10px;
        color: var(--studio-color-text-second);
    }

    .input-group {
        .ui-number-input {
            & ::ng-deep {
                .input {
                    border-right: unset;
                }
            }
        }

        .ui-number-input-no-radius {
            & ::ng-deep {
                .input {
                    border-right: unset;
                    border-radius: unset;
                }
            }
        }
    }

    .margin-bottom {
        margin-bottom: 1rem;
    }

    .empty-state {
        ::ng-deep * {
            color: var(--studio-color-text-second) !important;
        }

        button-toggle-group-item,
        button-toggle,
        &ui-select,
        &.img {
            opacity: 0.6;
        }
    }
}
