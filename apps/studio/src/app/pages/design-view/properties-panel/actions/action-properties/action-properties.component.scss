:where(:root:not([data-uinew])) :host {
    ::ng-deep {
        .actions {
            padding-bottom: 1rem;

            .action {
                &:last-child {
                    border-bottom: var(--ui-border);
                    border-color: var(--studio-color-border-second);
                }
            }
        }
    }

    .action {
        display: grid;
        align-items: center;

        .icon {
            text-align: right;
            color: var(--studio-color-text-second);
        }
    }
}
:where(:root[data-uinew]) :host {
    .actions {
        margin-top: var(--nui-space-050);
        > div {
            display: block;
            padding-bottom: var(--nui-space-300);
            padding-top: var(--nui-space-400);
            border-bottom: 1px solid var(--nui-divider-fill-primary-default);
            &:last-child {
                border-bottom: none;
                padding-bottom: var(--nui-space-200);
            }
            &:first-child {
                padding-top: 0;
            }
        }
    }
}
