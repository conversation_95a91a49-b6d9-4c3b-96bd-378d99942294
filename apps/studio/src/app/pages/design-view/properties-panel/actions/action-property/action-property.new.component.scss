:where(:root[data-uinew]) :host {
    width: 100%;
    display: flex;
    flex-direction: column;

    .setting {
        display: flex;
        width: 100%;
        height: 3.2rem;
        align-items: center;

        .action-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .action-buttons {
                display: flex;
                flex: 0;
            }
            .action-label {
                display: flex;
                align-items: center;
                gap: var(--nui-space-050);
                min-width: 0;

                .action-name {
                    flex: 0;
                    white-space: nowrap;
                }
                .state-name {
                    flex: 1;
                    min-width: 0;
                }
            }
        }
    }
    &:not(.disabled) {
        .action-name {
            --color: var(--nui-text-brand);
        }
    }
    &.disabled .setting {
        ui-label,
        .action-name {
            --color: var(--nui-text-disabled);
        }
        ui-svg-icon {
            --color: var(--nui-icon-disabled);
        }
    }
    .action-settings {
        padding: var(--nui-space-200) var(--nui-space-200) var(--nui-space-100) var(--nui-space-200);
        .divider {
            margin-top: var(--nui-space-200);
            margin-bottom: var(--nui-space-200);
        }
    }
    section-expand {
        margin-top: var(--nui-space-100);
        margin-bottom: var(--nui-space-200);
    }
}
