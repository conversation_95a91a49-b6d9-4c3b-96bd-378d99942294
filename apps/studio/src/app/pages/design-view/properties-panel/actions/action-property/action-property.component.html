<div class="setting">
    <div
        class="action-row"
        [class.collapsed]="actionSettingsOpened === false">
        <div class="action-label action-state">
            <ui-svg-icon
                size="xs"
                [icon]="actionData.disabled ? 'action-disabled' : 'de-action'"
                [nuiIcon]="actionData.disabled ? 'flash_off' : 'bolt'"></ui-svg-icon>
            @if (isNewUI()) {
                <ui-label
                    class="action-name"
                    size="sm"
                    weight="bold"
                    >{{ selectedAction.name }}</ui-label
                >
            } @else {
                <span
                    class="action-name"
                    (click)="onEditAction()"
                    >{{ selectedAction.name }}</span
                >
            }
            <ui-svg-icon
                class="direction-right"
                nuiIcon="east"
                size="xs"
                icon="direction-right" />
            @if (isNewUI()) {
                <div class="state-name">
                    <ui-label
                        size="sm"
                        weight="bold"
                        [truncate]="true"
                        [uiTooltip]="actionData.operations[0].value"
                        [uiTooltipPosition]="'left'"
                        [uiTooltipDisabled]="
                            selectedAction.value !== ActionOperationMethod.OpenUrl ||
                            actionSettingsOpened
                        ">
                        @if (selectedAction.value === ActionOperationMethod.OpenUrl) {
                            {{ actionData.operations[0].value }}
                        } @else {
                            {{ selectedTarget.name }}
                        }
                    </ui-label>
                </div>
            } @else {
                @if (selectedTarget && selectedAction.value !== ActionOperationMethod.OpenUrl) {
                    <span class="state-name">
                        {{ selectedTarget.name }}
                    </span>
                }
                @if (selectedAction.value === ActionOperationMethod.OpenUrl) {
                    <span
                        class="state-name"
                        [uiTooltip]="actionData.operations[0].value"
                        [uiTooltipPosition]="'left'"
                        [uiTooltipDisabled]="actionSettingsOpened">
                        {{ actionData.operations[0].value }}
                    </span>
                }
            }
        </div>
        @if (isNewUI()) {
            <div class="action-buttons">
                <ui-button
                    type="ghost-secondary"
                    nuiSvgIcon="more_vert"
                    size="xs"
                    (click)="$event.stopPropagation()"
                    [uiDropdownTarget]="actionMenu" />
                <ui-button
                    type="ghost-secondary"
                    [disabled]="!!actionData.disabled"
                    [nuiSvgIcon]="actionSettingsOpened ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
                    size="xs"
                    (click)="onEditAction()" />
            </div>
        } @else {
            <ui-svg-icon
                class="menu-button"
                icon="kebab"
                size="sm"
                [uiDropdownTarget]="actionMenu" />
        }
    </div>
</div>

@if (actionSettingsOpened) {
    <section-expand
        #actionExpander
        [arrowPosition]="isNewUI() ? '10%' : '7%'"
        [expanded]="true"
        (animationStateChanged)="closeAction($event)">
        <div class="action-settings">
            <div class="setting-row alternate">
                <div class="setting-label">Trigger</div>
                <div
                    class="setting-value select"
                    [class.col-1]="isNewUI()"
                    [class.wide]="isNewUI()">
                    <div class="property-input">
                        <ui-select
                            size="xs"
                            type="secondary"
                            [(selected)]="selectedTrigger"
                            (selectedChange)="onTriggerChange()"
                            [disabled]="disabled"
                            placeholder="Select a trigger"
                            [useTargetWidth]="true">
                            @for (trigger of triggers; track trigger.name) {
                                <ui-option [value]="trigger">
                                    {{ trigger.name }}
                                </ui-option>
                            }
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="setting-row alternate">
                <div class="setting-label">Action</div>
                <div
                    class="setting-value select"
                    [class.col-1]="isNewUI()"
                    [class.wide]="isNewUI()">
                    <div class="property-input">
                        <ui-select
                            size="xs"
                            type="secondary"
                            data-test-id="action-select-action"
                            [(selected)]="selectedAction"
                            (selectedChange)="onActionMethodChange()"
                            [disabled]="disabled"
                            placeholder="Select an action"
                            [useTargetWidth]="true">
                            @for (action of selectableActions; track $index) {
                                <ui-option
                                    [selected]="action === selectedAction"
                                    [value]="action">
                                    {{ action.name }}
                                </ui-option>
                            }
                        </ui-select>
                    </div>
                </div>
            </div>
            @if (isStateMethod) {
                <div class="setting-row alternate">
                    <div class="setting-label">Target</div>
                    <div
                        class="setting-value select"
                        [class.col-1]="isNewUI()"
                        [class.wide]="isNewUI()">
                        <div class="property-input">
                            <ui-select
                                size="xs"
                                type="secondary"
                                data-test-id="action-select-target"
                                [(selected)]="selectedTarget"
                                (selectedChange)="onStateTargetChange()"
                                [disabled]="disabled"
                                placeholder="No elements created"
                                [useTargetWidth]="true">
                                @for (target of targets; track $index) {
                                    <ui-option
                                        [selected]="target.id === selectedTarget.id"
                                        [value]="target">
                                        {{ target.name }}
                                    </ui-option>
                                }
                            </ui-select>
                        </div>
                    </div>
                </div>
                @if (selectedAction.value !== ActionOperationMethod.ClearStates) {
                    <div class="setting-row alternate">
                        <div class="setting-label">State</div>
                        <div
                            class="setting-value select"
                            [class.col-1]="isNewUI()"
                            [class.wide]="isNewUI()">
                            <div class="property-input">
                                @if (states.length === 0 && isNewUI()) {
                                    <ui-label
                                        size="xs"
                                        type="secondary">
                                        No states on target
                                    </ui-label>
                                }
                                @if (states.length > 0 || !isNewUI()) {
                                    <ui-select
                                        size="xs"
                                        type="secondary"
                                        [(selected)]="selectedState"
                                        (selectedChange)="onStateValueChange()"
                                        [disabled]="disabled"
                                        placeholder="No states on target"
                                        [useTargetWidth]="true">
                                        @for (state of states; track state.id) {
                                            <ui-option
                                                [selected]="state.id === selectedState"
                                                [value]="state">
                                                {{ state.name }}
                                            </ui-option>
                                        }
                                    </ui-select>
                                }
                            </div>
                        </div>
                    </div>
                }
                @if (isClickPreventionEligible(selectedTrigger.value)) {
                    <div class="setting-row reverse">
                        <div class="setting-label">Trigger target URL on click</div>
                        <div class="setting-value">
                            <div class="property-input">
                                <ui-toggle-switch
                                    [(selected)]="allowClickThrough"
                                    (selectedChange)="onPreventClicksChange()">
                                </ui-toggle-switch>
                            </div>
                        </div>
                    </div>
                }
                @if (isNewUI()) {
                    <ui-dropdown-divider class="divider" />
                } @else {
                    <div class="divider"></div>
                }
                <div class="setting-row">
                    <div class="setting-label">Duration</div>
                    <div
                        class="setting-value col-2 wide"
                        [class.slider]="isNewUI()">
                        <div
                            class="property-input slider"
                            [class.slider]="!isNewUI()">
                            <ui-range
                                size="xs"
                                [(value)]="duration"
                                [disabled]="disabled"
                                (valueChange)="onDurationChange()"
                                (mouseup)="onDurationChange(true)"
                                [min]="0"
                                [max]="10"
                                [step]="0.01"></ui-range>
                        </div>
                        <div
                            class="property-input"
                            data-test-id="animation-duration">
                            <ui-number-input
                                size="xs"
                                type="secondary"
                                [(value)]="duration"
                                data-test-id="animation-duration-input"
                                (valueChange)="onDurationChange(true)"
                                [disabled]="disabled"
                                [keyboardEmit]="true"
                                [arrowButtons]="!isNewUI()"
                                [min]="0"
                                [max]="10"
                                [step]="0.1"
                                [allowEmpty]="false"
                                [disableUndo]="true"></ui-number-input>
                        </div>
                    </div>
                </div>
                <div class="setting-row alternate">
                    <div class="setting-label">Easing</div>
                    <div class="setting-value select easing-setting col-1 wide">
                        <ui-select
                            size="xs"
                            type="secondary"
                            [selected]="timingFunction"
                            (selectedChange)="setEasing($event)"
                            placeholder="None"
                            [useTargetWidth]="true">
                            @for (
                                timingFunction of timingFunctions | keyvalue;
                                track timingFunction.key
                            ) {
                                <ui-option [value]="timingFunction.key">{{
                                    timingFunction.value.name
                                }}</ui-option>
                            }
                        </ui-select>
                    </div>
                </div>
            }
            @if (selectedAction.value === ActionOperationMethod.OpenUrl) {
                <div class="setting-row alternate">
                    <div class="setting-label">
                        URL
                        <ui-svg-icon
                            size="xs"
                            icon="question-mark-s"
                            nuiIcon="help"
                            [uiTooltip]="urlActionTooltip"
                            [uiTooltipPosition]="'left'"
                            [uiTooltipInteractive]="true"
                            [uiTooltipMaxWidth]="'260px'">
                        </ui-svg-icon>
                    </div>
                    <div class="setting-value">
                        <div class="property-input">
                            <ui-input
                                size="xs"
                                #urlInput
                                [validation]="urlValidation!"
                                [value]="actionData.operations[0].value"
                                (blur)="onUrlInputBlur()"
                                (valueChange)="onUrlChange($event)">
                            </ui-input>
                        </div>
                    </div>
                </div>
            }
        </div>
    </section-expand>
}

<ui-dropdown
    size="sm"
    #actionMenu
    width="150"
    [offset]="{ x: 18, y: 4 }"
    [positions]="[
        { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'top' },
        { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'bottom' }
    ]"
    type="menu">
    @if (!actionData.disabled && !isNewUI()) {
        <ui-dropdown-item (click)="onEditAction()">
            {{ actionSettingsOpened ? 'Close' : 'Edit action' }}
        </ui-dropdown-item>
    }
    <ui-dropdown-item
        [svgIcon]="actionData.disabled ? 'de-action' : 'action-disabled'"
        [nuiIcon]="actionData.disabled ? 'bolt' : 'flash_off'"
        (click)="onToggleDisableAction()">
        {{ actionData.disabled ? 'Enable action' : 'Disable action' }}
    </ui-dropdown-item>
    <ui-dropdown-divider />
    <ui-dropdown-item
        class="divider"
        svgIcon="delete"
        nuiIcon="delete"
        [destructive]="true"
        (click)="onDeleteAction()">
        Delete action
    </ui-dropdown-item>
</ui-dropdown>
