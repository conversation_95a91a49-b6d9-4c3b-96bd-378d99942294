<studio-ui-section
    id="add-action-header"
    headline="Actions"
    [clickableHeader]="!isNewUI() || !!actions.length"
    (headerClick)="!isNewUI() && triggerButton($event)"
    [empty]="!actions.length"
    class="no-padding"
    [customAction]="!isNewUI() ? addActionTemplate : undefined"
    [actions]="
        isNewUI()
            ? [
                  {
                      id: 'add-action-button',
                      icon: 'plus-small',
                      nuiIcon: 'add',
                      nuiType: 'ghost-secondary',
                      dropdownTarget: dropdown,
                      hidden: false
                  }
              ]
            : []
    "
    [isNewUI]="isNewUI()"
    [divider]="true">
    <div class="actions">
        @for (actionType of sortedActionKeys; track actionType) {
            <div>
                @if (sortedActions[actionType] && sortedActions[actionType]!.length > 0) {
                    @if (isNewUI()) {
                        <ui-label
                            size="xs"
                            class="setting">
                            On {{ actionName(actionType) | lowercase }}
                        </ui-label>
                    } @else {
                        <div class="setting">On {{ actionName(actionType) | lowercase }}</div>
                    }
                    @for (
                        elementAction of sortedActions[actionType];
                        track elementAction;
                        let i = $index
                    ) {
                        <action-property
                            #actionElement
                            [actionId]="elementAction.id"
                            [actionSettingsOpened]="newActionId === elementAction.id"
                            (editAction)="onEditAction($event)"
                            (actionDeleted)="onDeleteAction($event)"></action-property>
                    }
                }
            </div>
        }
    </div>
</studio-ui-section>
<ui-dropdown
    size="sm"
    [width]="isNewUI() ? '160' : 'auto'"
    #dropdown
    id="action-add-dropdown"
    [offset]="placeDropdown()"
    [positions]="[
        { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'top' },
        { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'bottom' }
    ]"
    type="menu">
    @for (trigger of actionTriggers; track trigger.name) {
        <ui-dropdown-item
            [id]="'interaction-action-' + trigger.value"
            (click)="addAction(trigger)">
            On {{ trigger.name }}
        </ui-dropdown-item>
    }
</ui-dropdown>

<ng-template #addActionTemplate>
    <ng-container>
        <div
            #customDropdown
            class="action"
            [uiDropdownTarget]="dropdown">
            <ui-svg-icon
                [style.width]="addActionIconWidth + 'px'"
                class="icon"
                icon="plus-small">
            </ui-svg-icon>
        </div>
    </ng-container>
</ng-template>
