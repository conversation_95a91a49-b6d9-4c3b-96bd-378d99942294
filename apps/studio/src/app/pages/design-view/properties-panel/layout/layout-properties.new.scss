:where(:root[data-uinew]) :host {
    .body {
        padding: var(--nui-space-300) var(--nui-space-300) 0 var(--nui-space-300);
        border-bottom: 1px solid var(--nui-border-neutral-subtle);
        & > div:last-child {
            margin-bottom: var(--nui-space-300);
        }
        &.divider .setting-row:first-child {
            margin-top: 0;
        }
    }
    .position-settings .setting-value {
        margin-bottom: var(--nui-space-200);
        &.col-2 {
            ui-number-input {
                width: auto;
            }
        }
        .ratio-lock-placeholder {
            width: 1.6rem;
        }
    }
    .separate-radius-container {
        display: flex;
        gap: var(--nui-space-200);
        justify-content: flex-end;
        &:last-child {
            margin-bottom: var(--nui-space-200);
        }

        .setting-value {
            justify-content: center;
        }
        > div {
            flex-basis: 4.5rem;
        }
    }
    .ratio-lock {
        display: flex;
        cursor: pointer;
        &:hover ui-svg-icon {
            --color: var(--nui-button-fill-primary);
        }
        &.active ui-svg-icon {
            --color: var(--nui-button-fill-primary);
        }
    }
    .rotation-container {
        display: flex;
        align-items: center;
        gap: var(--nui-space-200);
    }
    .use-pixel-values-row {
        height: 2.4rem;
        margin-top: 0;
        margin-bottom: var(--nui-space-200);
        .setting-value {
            margin-bottom: 0;
        }
    }
}
