import { ChangeDetectorRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { UIModule } from '@bannerflow/ui';
import { CreativeDataNode } from '@creative/nodes';
import { RadiusType } from '@domain/style';
import { createDataNodeMock } from '@mocks/element.mock';
import { UserSettingsService } from '@studio/common/user-settings';
import { EMPTY, of } from 'rxjs';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { DesignViewComponent } from '../../design-view.component';
import { IsElementsPipe } from '../../pipes/is-element.pipe';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { ElementChangeType } from '@domain/element-change';
import { EditorStateService } from '../../services/editor-state.service';
import { HistoryService } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { GizmoDrawSchedulerService } from '../../workspace/gizmo-draw-scheduler';
import { PropertiesService } from '../properties.service';
import { LayoutPropertiesComponent } from './layout-properties.component';

const mockPropertiesService = {
    observeDataElementOrStateChange: (): any => EMPTY,
    inStateView: false,
    stateValueIsUndefined: (): boolean => false
};

const rootNode = {
    elements: [{ id: '2' }, { id: '1' }]
} as CreativeDataNode;

const elementMock = createDataNodeMock({
    id: '1',
    x: 45,
    time: 0.1,
    duration: 0.2,
    __rootNode: rootNode
});

const mockUserSettingsService = {
    animation$: of({
        usePixelValues: { '1fsdf': true }
    })
};

describe('LayoutProperties', () => {
    let component: LayoutPropertiesComponent;
    let fixture: ComponentFixture<LayoutPropertiesComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [LayoutPropertiesComponent, IsElementsPipe],
            imports: [UIModule],
            providers: [
                {
                    provide: DesignViewComponent,
                    useValue: {
                        workspace: {
                            gizmoDrawer: {}
                        }
                    }
                },
                { provide: GizmoDrawSchedulerService, useValue: {} },
                { provide: EditorStateService, useValue: {} },
                { provide: ChangeDetectorRef, useValue: {} },
                {
                    provide: MutatorService,
                    useValue: {
                        setPosition: (): void => {},
                        setElementPropertyValue: (): boolean => false,
                        setScale: (): void => {},
                        setSize: (): void => {}
                    }
                },
                {
                    provide: UserSettingsService,
                    useValue: mockUserSettingsService
                },
                {
                    provide: HistoryService,
                    useValue: {
                        onChange$: EMPTY
                    }
                },
                { provide: EditorStateService, useValue: {} },
                {
                    provide: PropertiesService,
                    useValue: mockPropertiesService
                },
                {
                    provide: EditorEventService,
                    useValue: {
                        elements: {
                            immediateChange$: EMPTY
                        }
                    }
                },
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
        fixture = TestBed.createComponent(LayoutPropertiesComponent);
        component = fixture.componentInstance;

        component.isPropertiesHidden = false;
        component.usePixelValues = true;
        component.elements$ = of([elementMock]);
        component.stateOrElement = {
            radius: {
                type: RadiusType.Joint,
                topLeft: 0,
                topRight: 0,
                bottomLeft: 0,
                bottomRight: 0
            }
        };

        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    describe('Update Layout Properties', () => {
        it('should call updatePosition with updated x values', () => {
            const spyUpdatePosition = jest.spyOn(component, 'setPosition');

            const debugInputElement = fixture.debugElement.query(By.css('#x-input'));
            debugInputElement.triggerEventHandler('valueChange', 56);

            expect(spyUpdatePosition).toHaveBeenCalledWith(56, 'x');
        });

        it('should call updatePosition with updated y values', () => {
            const spyUpdatePosition = jest.spyOn(component, 'setPosition');

            const debugInputElement = fixture.debugElement.query(By.css('#y-input'));
            debugInputElement.triggerEventHandler('valueChange', 58);

            expect(spyUpdatePosition).toHaveBeenCalledWith(58, 'y');
        });

        it('should call updateSize with updated width values', () => {
            const spyUpdateSize = jest.spyOn(component, 'setSize');

            const debugInputElement = fixture.debugElement.query(By.css('#width-input'));
            debugInputElement.triggerEventHandler('valueChange', 122);

            expect(spyUpdateSize).toHaveBeenCalledWith(122, 'width');
        });

        it('should call updateSize with updated height values', () => {
            const spyUpdateSize = jest.spyOn(component, 'setSize');

            const debugInputElement = fixture.debugElement.query(By.css('#height-input'));
            debugInputElement.triggerEventHandler('valueChange', 432);

            expect(spyUpdateSize).toHaveBeenCalledWith(432, 'height');
        });

        it('should call updateRotationX with updated rotationX values', () => {
            const spyUpdateRotationX = jest.spyOn(component, 'setRotationX');

            const debugInputElement = fixture.debugElement.query(By.css('#rotationX-input'));
            debugInputElement.triggerEventHandler('valueChange', 432);

            expect(spyUpdateRotationX).toHaveBeenCalledWith(432);
        });

        it('should call updateRotationY with updated rotationY values', () => {
            const spyUpdatePosition = jest.spyOn(component, 'setRotationY');

            const debugInputElement = fixture.debugElement.query(By.css('#rotationY-input'));
            debugInputElement.triggerEventHandler('valueChange', 732);

            expect(spyUpdatePosition).toHaveBeenCalledWith(732);
        });

        it('should call updateScaleX with updated scaleX values', () => {
            component.inStateView = true;
            component.usePixelValues = false;
            fixture.detectChanges();

            const spyUpdateScaleX = jest.spyOn(component, 'setScale');

            const debugInputElement = fixture.debugElement.query(By.css('#scale-x-input'));
            debugInputElement.triggerEventHandler('valueChange', 132);

            expect(spyUpdateScaleX).toHaveBeenCalledWith('scaleX');
        });

        it('should call updateScaleY with updated scaleY values', () => {
            component.inStateView = true;
            component.usePixelValues = false;
            fixture.detectChanges();

            const spyUpdateScaleY = jest.spyOn(component, 'setScale');

            const debugInputElement = fixture.debugElement.query(By.css('#scale-y-input'));
            debugInputElement.triggerEventHandler('valueChange', 132);

            expect(spyUpdateScaleY).toHaveBeenCalledWith('scaleY');
        });

        it('should call setFullRadius with correct values', () => {
            const spyUpdateRadius = jest.spyOn(component, 'setFullRadius');
            const debugInputElement = fixture.debugElement.query(By.css('#radius-input'));
            debugInputElement.triggerEventHandler('valueChange', 132);

            expect(spyUpdateRadius).toHaveBeenCalledWith(132, ElementChangeType.Burst);
        });

        it('should call setRadiusType with correct values', () => {
            const spyUpdateRadiusType = jest.spyOn(component, 'setRadiusType');
            const debugInputElement = fixture.debugElement.query(By.css('#radius-type-input'));
            debugInputElement.triggerEventHandler('valueChange', RadiusType.Separate);

            expect(spyUpdateRadiusType).toHaveBeenCalledWith(RadiusType.Separate);
        });

        it('should call setRadius with correct values', () => {
            const spyUpdateRadius = jest.spyOn(component, 'setRadius');
            component.radiusType = RadiusType.Separate;
            fixture.detectChanges();

            const debugInputElementTop = fixture.debugElement.query(By.css('#radius-input-top-left'));
            debugInputElementTop.triggerEventHandler('valueChange', 132);

            expect(spyUpdateRadius).toHaveBeenCalledWith(132, ElementChangeType.Burst, 'topLeft');
        });

        it('should call setOpacity with correct values', () => {
            const spyUpdateOpacity = jest.spyOn(component, 'setOpacity');

            const debugInputElementTop = fixture.debugElement.query(By.css('#opacity-input'));
            debugInputElementTop.triggerEventHandler('valueChange', 132);

            expect(spyUpdateOpacity).toHaveBeenCalledWith(132, ElementChangeType.Burst);
        });
    });
});
