:where(:root:not([data-uinew])) :host {
    .position-settings {
        .property-input {
            max-width: none;
        }

        .mb-1 {
            margin-bottom: 1rem;
        }

        .mr-1 {
            margin-right: 1rem;
        }
    }

    .rotation-container {
        display: flex;
        margin-bottom: 1rem;

        .property-input:not(:last-child) {
            ::ng-deep {
                .input {
                    border-right: 0;
                }
            }
        }
    }

    .property-input {
        max-width: 60px;
        position: relative;

        .rotate-text {
            font-size: 10px;
            color: var(--studio-color-primary);
            cursor: pointer;
            text-align: end;
        }

        &.slider {
            max-width: 81px;
            min-width: 60px;
        }

        &.end {
            justify-self: end;
        }

        &:hover {
            .property-icon {
                opacity: 0;
            }
        }
    }

    .property-icon {
        display: block;
        position: absolute;
        right: 0.1rem;
        top: 50%;
        color: var(--studio-color-text-disabled);
        transform: translate(0, -50%);
        pointer-events: none;
    }

    .setting-value {
        justify-content: space-between;

        &.size {
            grid-template-columns: 54px auto 54px;
            grid-gap: 4px;
        }

        &.rotation-3d {
            display: flex;

            ::ng-deep {
                .property-input {
                    &:first-child {
                        .input {
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                        }
                    }

                    &:nth-child(2) {
                        .input {
                            border-radius: 0;
                            border-right: 0;
                            border-left: 0;

                            &:focus {
                                border-color: var(--studio-color-focus);
                            }
                        }
                    }

                    &:nth-child(3) {
                        .input {
                            border-top-left-radius: 0;
                            border-bottom-left-radius: 0;
                        }
                    }
                }
            }
        }

        &.update-rotation-y {
            ::ng-deep {
                .property-input {
                    &:nth-child(1) {
                        .input {
                            border-right-color: var(--studio-color-focus);
                        }
                    }

                    &:nth-child(2) {
                        border: 0;
                    }

                    &:nth-child(3) {
                        .input {
                            border-left-color: var(--studio-color-focus);
                        }
                    }
                }
            }
        }

        .ratio-lock {
            color: var(--studio-color-text-second);
            cursor: pointer;

            .icon {
                font-size: 14px;
            }

            &.active {
                color: var(--studio-color-blue);
            }
        }

        .ratio-lock-placeholder {
            width: 14px;
        }
    }

    .separate-radius-container {
        display: flex;
        margin: 8px 0;
        .setting-value {
            width: 100%;
            display: flex;
            justify-content: center;
        }
        .icon {
            color: var(--ui-color-text-second);
        }
    }

    .align-end {
        width: 65%;
        margin-left: auto;
    }
}
