:where(:root:not([data-uinew])) :host {
    & ::ng-deep .setting-value {
        .ui-select {
            &::ng-deep {
                .text {
                    padding: 0 20px 0 1rem;
                }
            }
        }

        &.type-value {
            grid-template-columns: auto 14px !important;
            grid-gap: 8px !important;

            .remove-button {
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }

    & ::ng-deep .angle-setting {
        display: flex !important;

        ui-button-group {
            flex: 1;
            min-width: 70%;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        ui-number-input {
            flex: 1;

            & ::ng-deep .input {
                border-left: 0;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }

    .placeholder-text {
        color: var(--studio-color-text-second);
        margin-left: 10px;
    }
}
