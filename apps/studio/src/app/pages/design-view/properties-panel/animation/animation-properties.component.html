<!-- In/out animation -->
<studio-ui-section
    class="no-padding"
    headline="{{ this.type | capitalize }} animation"
    [clickableHeader]="(animation$ | async) === undefined && !isNewUI()"
    [empty]="(animation$ | async) === undefined && !animationsMixed"
    (headerClick)="addAnimation()"
    [isNewUI]="isNewUI()"
    [divider]="true"
    [actions]="
        (animation$ | async) === undefined || animationsMixed
            ? [
                  {
                      id: 'add-' + type + '-animation-button',
                      icon: 'plus-small',
                      nuiIcon: 'add',
                      nuiType: 'ghost-secondary',
                      action: addAnimation,
                      hidden: false
                  }
              ]
            : []
    ">
    @if (!animationsMixed) {
        @if (animation$ | async; as animation) {
            <div class="body">
                <div class="setting">
                    <div class="setting-row wide">
                        <div
                            class="setting-label"
                            [uiTooltip]="tooltips[this.type]">
                            Duration
                        </div>
                        <div
                            class="setting-value col-2 wide"
                            [class.slider]="isNewUI()">
                            <div
                                class="property-input"
                                [class.slider]="!isNewUI()">
                                <ui-range
                                    size="xs"
                                    [value]="animation.duration"
                                    (valueChange)="setDuration($event, animation)"
                                    (mouseUp)="
                                        setDuration(
                                            animation.duration,
                                            animation,
                                            ElementChangeType.Force
                                        )
                                    "
                                    [min]="minDuration"
                                    [max]="maxDuration"
                                    [step]="0.01"></ui-range>
                            </div>
                            <div class="property-input">
                                <ui-number-input
                                    size="xs"
                                    type="secondary"
                                    [value]="
                                        animation.duration === 0
                                            ? undefined
                                            : +animation.duration.toFixed(2)
                                    "
                                    id="animation-duration"
                                    data-test-id="animation-duration-input"
                                    (valueChange)="
                                        setDuration($event, animation, ElementChangeType.Burst)
                                    "
                                    [arrowButtons]="!isNewUI()"
                                    [keyboardEmit]="true"
                                    [min]="minDuration"
                                    [max]="maxDuration"
                                    [step]="minDuration"
                                    [placeholder]="'-'"
                                    [allowEmpty]="true"
                                    [disableUndo]="true"
                                    [unitLabel]="isNewUI() ? 's' : undefined">
                                </ui-number-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="body divider">
                <div class="setting">
                    <div class="setting-row alternate">
                        <div class="setting-label">Type</div>
                        <div class="setting-value select type-value col-1 wide">
                            <ui-select
                                size="xs"
                                type="secondary"
                                [selected]="animation.name === undefined ? 'Mixed' : animation.id"
                                id="animation-type"
                                (selectedChange)="setAnimationName($event)"
                                [useTargetWidth]="true">
                                @if (animation.name === undefined) {
                                    <ui-option
                                        [value]="'Mixed'"
                                        [selected]="true">
                                        Mixed
                                    </ui-option>
                                    <div class="option-divider"></div>
                                }
                                @for (t of animationTemplates; track t.id) {
                                    <ui-option [value]="t.id">{{ t.name }}</ui-option>
                                }
                            </ui-select>
                            @if (isNewUI()) {
                                <ui-button
                                    size="sm"
                                    type="plain-primary-destructive"
                                    [id]="type + '-animation-remove-btn'"
                                    class="remove-button"
                                    nuiSvgIcon="delete"
                                    (click)="removeAnimation()" />
                            } @else {
                                <div
                                    class="remove-button"
                                    [id]="type + '-animation-remove-btn'"
                                    (click)="removeAnimation()">
                                    <ui-svg-icon icon="delete"></ui-svg-icon>
                                </div>
                            }
                        </div>
                    </div>
                    <!-- Animation settings -->
                    @for (setting of animation.settings | keyvalue; track setting.key) {
                        <div class="setting-row alternate">
                            @if (setting && setting.value && setting.value.name) {
                                <div class="setting-label">{{ setting.value.name }}</div>
                                <!-- Angle property -->
                                @if (animationSettingPropertyToUnitMap[setting.key] === 'angle') {
                                    <div class="setting-value angle-setting col-1 wide">
                                        <ui-button-group
                                            size="xs"
                                            [options]="angleOptions"
                                            [value]="
                                                animationDirection.isMixed
                                                    ? ''
                                                    : setting.value.value + ''
                                            "
                                            (valueChange)="setSettingValue(setting, +$event)">
                                        </ui-button-group>
                                        @if (
                                            animation.id !== 'flip-in' && animation.id !== 'flip-out'
                                        ) {
                                            <ui-number-input
                                                size="xs"
                                                type="secondary"
                                                unitLabel="°"
                                                id="animation-unit-degree"
                                                [allowEmpty]="true"
                                                [placeholder]="'-'"
                                                [step]="1"
                                                [min]="minAnimationDirection"
                                                [max]="maxAnimationDirection"
                                                [arrowButtons]="!isNewUI()"
                                                [value]="
                                                    animationDirection.isMixed
                                                        ? undefined
                                                        : +setting.value.value
                                                "
                                                (valueChange)="setSettingValue(setting, $event)">
                                            </ui-number-input>
                                        }
                                    </div>
                                }
                                <!-- Number property -->
                                @if (animationSettingPropertyToUnitMap[setting.key] === 'number') {
                                    <div class="setting-value number-setting">
                                        <ui-number-input
                                            size="xs"
                                            type="secondary"
                                            unitLabel="px"
                                            id="animation-unit-px"
                                            [allowEmpty]="true"
                                            [placeholder]="'-'"
                                            [step]="1"
                                            [min]="minAnimationDistance"
                                            [max]="maxAnimationDistance"
                                            [arrowButtons]="!isNewUI()"
                                            [value]="
                                                animationDistance.isMixed
                                                    ? undefined
                                                    : +setting.value.value
                                            "
                                            (valueChange)="
                                                setSettingValue(setting, $event)
                                            "></ui-number-input>
                                    </div>
                                }
                            }
                        </div>
                    }
                    <!-- Easing properties-->
                    <div class="setting-row alternate">
                        <div class="setting-label">Easing</div>
                        <div class="setting-value select easing-setting col-1 wide">
                            <ui-select
                                size="xs"
                                type="secondary"
                                [selected]="
                                    animation.timingFunction === undefined
                                        ? 'Mixed'
                                        : animation.timingFunction
                                "
                                id="animation-easing"
                                (selectedChange)="setEasing($event)"
                                [useTargetWidth]="true">
                                @if (animation.timingFunction === undefined) {
                                    <ui-option
                                        [value]="'Mixed'"
                                        [selected]="true">
                                        Mixed
                                    </ui-option>
                                    <div class="option-divider"></div>
                                }
                                @for (
                                    timingFunction of timingFunctions | keyvalue;
                                    track timingFunction.key
                                ) {
                                    <ui-option [value]="timingFunction.key">{{
                                        timingFunction.value.name
                                    }}</ui-option>
                                }
                            </ui-select>
                        </div>
                    </div>
                </div>
            </div>
        }
    } @else {
        @if (isNewUI()) {
            <ui-label
                type="secondary"
                size="sm"
                class="placeholder-text">
                Click + to replace mixed properties</ui-label
            >
        } @else {
            <p
                class="placeholder-text"
                id="animation-placeholder">
                Click + to replace mixed properties
            </p>
        }
    }
</studio-ui-section>
