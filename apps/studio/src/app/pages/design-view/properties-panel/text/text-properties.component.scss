:where(:root:not([data-uinew])) :host {
    ::ng-deep {
        .property-input {
            .ui-popover {
                .button {
                    min-width: 130px;
                    max-width: 130px;
                }
            }

            .select .ui-select {
                .button {
                    min-width: unset !important;
                }

                &.tiny {
                    .button {
                        min-width: 70px;

                        .text-wrapper {
                            overflow: visible;
                        }
                    }
                }
            }
        }
    }

    .option-header {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        height: 26px;
        align-items: center;
        background: var(--studio-color-white);
        border-top: 1px solid var(--studio-color-border-second);
        border-bottom: 1px solid var(--studio-color-border-second);

        .headline {
            margin-left: 10px;
        }

        .minimize {
            color: var(--studio-color-blue);
            font-size: 10px;
            margin-right: 10px;
            justify-self: end;
            cursor: pointer;
        }
    }

    .container {
        max-width: 130px;
        width: 130px;
    }

    .add {
        width: 100%;
        height: 100%;
        display: grid;
        justify-items: center;
        align-items: center;
        border: 1px solid var(--studio-color-grey-92);
        color: var(--studio-color-text-second);
        border-radius: 0.2rem;
    }

    .setting-value {
        &.col-4 {
            &.shadow {
                grid-gap: 0px;
                grid-template-columns: 42px 42px 42px;
                justify-items: center;
            }

            &.shadow-container {
                grid-gap: 0px;
            }
        }

        &.color {
            width: 4.4rem;
        }
    }

    .property-input {
        &.text-decoration {
            display: flex;

            > button-toggle {
                margin-right: 0.8rem;
            }

            .decoration {
                width: 22px;
            }
        }
    }

    .placeholder-text {
        color: var(--studio-color-text-second);
    }

    .text-padding {
        display: grid;
        grid-template-areas:
            'a a b'
            '. . c';
        grid-template-columns: auto;
        column-gap: 1.6rem;

        .setting-label {
            grid-area: a;
        }

        .setting-value {
            grid-area: b;
        }

        .separate-padding-container {
            display: flex;
            grid-area: c;

            .property-input {
                display: flex;
                flex-direction: column;
                align-items: center;
                row-gap: 0.5rem;
            }
        }
    }
}
