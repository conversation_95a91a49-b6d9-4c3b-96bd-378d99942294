:where(:root[data-uinew]) :host {
    .font-property {
        margin-bottom: var(--nui-space-200);
    }
    .setting-value.size {
        .property-input {
            width: 5.6rem;
        }
        ui-number-input {
            width: 100%;
        }
        .max-rows-input {
            position: relative;
            ui-label {
                position: absolute;
                right: 0;
                z-index: 1;
                width: 100%;
                height: 100%;
                --color: var(--nui-text-primary);
                background-color: var(--nui-forms-fill-secondary-enabled);
                border-radius: var(--nui-forms-radius);
            }
            &:hover {
                ui-label {
                    display: none;
                }
            }
            &.focus {
                ui-label {
                    display: none;
                }
            }
        }
    }
    .setting-value.spacing {
        .property-input {
            width: 6.4rem;
        }
        ui-number-input {
            width: 100%;
        }
    }
    .text-decoration ui-button-group {
        width: 12rem;
    }
    .text-padding {
        display: grid;
        grid-template-areas: 'a a b';
        grid-template-columns: auto;
        row-gap: var(--nui-space-200);
        column-gap: 0;

        &:has(.separate-padding-container) {
            grid-template-areas:
                'a b'
                '. c';
        }
        .setting-label {
            position: relative;
            grid-area: a;
        }

        .setting-value {
            grid-area: b;
        }

        .separate-padding-container {
            display: flex;
            grid-area: c;
            column-gap: var(--nui-space-200);

            .property-input {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }
    }
    .alignment-options button-group {
        width: 8.5rem;
    }
}
