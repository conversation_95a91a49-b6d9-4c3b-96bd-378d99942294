import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    inject,
    Input,
    OnInit,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UIConfirmDialogService, UINewThemeService } from '@bannerflow/ui';
import { Color } from '@creative/color';
import {
    applyCustomPropertyPrefix,
    isAnyWidgetReference,
    mapElementToCustomWidgetProperties
} from '@creative/elements/widget/utils';
import { getElementPropertyByName } from '@creative/nodes';
import { serializeWidgetPropertyValue } from '@creative/serialization';
import { IBrandLibraryElement } from '@domain/brand/brand-library';
import { IColor } from '@domain/color';
import { IVersionProperty, OneOfVersionableProperties } from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import { IWidgetCustomProperty, IWidgetElementDataNode } from '@domain/widget';
import { isVersionedProperty, UserService } from '@studio/common';
import { createElementProperty } from '@studio/utils/element.utils';
import { handleError } from '@studio/utils/errors';
import { sanitizeString } from '@studio/utils/utils';
import { distinctUntilChanged, filter, firstValueFrom, Observable } from 'rxjs';
import { FeedPickerComponent } from '../../../../shared/components';
import { BrandLibraryDataService } from '../../../../shared/media-library/brand-library.data.service';
import { WidgetService } from '../../../../shared/services/widget.service';
import { VersionsService } from '../../../../shared/versions/state/versions.service';
import { isEffectLibraryElement } from '../../media-library/media-library.helpers';
import { ElementChangeType } from '@domain/element-change';
import { EditorStateService } from '../../services/editor-state.service';
import { MutatorService } from '../../services/mutator.service';
import { WidgetPropertiesService } from './widget-properties.service';

@Component({
    selector: 'widget-properties',
    templateUrl: 'widget-properties.component.html',
    styleUrls: ['../common.scss', 'widget-properties.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WidgetPropertiesComponent implements OnInit {
    private brandLibraryDataService = inject(BrandLibraryDataService);
    private changeDetector = inject(ChangeDetectorRef);
    private destroyRef = inject(DestroyRef);
    private editorStateService = inject(EditorStateService);
    private mutatorService = inject(MutatorService);
    private uiConfirmDialogService = inject(UIConfirmDialogService);
    private uiNewThemeService = inject(UINewThemeService);
    private userService = inject(UserService);
    private versionsService = inject(VersionsService);
    private widgetPropertiesService = inject(WidgetPropertiesService);
    private widgetService = inject(WidgetService);

    @Input() preview = false;
    @Input() elements$: Observable<IWidgetElementDataNode[]>;

    @ViewChild('feedPicker') feedPicker: FeedPickerComponent;

    selectedVersionProperties$: Observable<IVersionProperty[]>;
    widgetProperties: IWidgetCustomProperty[] = [];
    isBannerflowLibraryWidget = false;
    isEffectWidget = false;
    isSDAEnabled = false;
    element: IWidgetElementDataNode;

    private mappedProperties: { [key: string]: IWidgetCustomProperty[] } = {};
    private selectedVersionProperties: IVersionProperty<OneOfVersionableProperties>[];
    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor() {
        this.selectedVersionProperties$ = this.versionsService.selectedVersionProperties$;

        this.versionsService.selectedVersionProperties$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(selectedVersionProperties => {
                this.selectedVersionProperties = selectedVersionProperties;
            });

        this.versionsService.selectedVersion$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(selectedVersion => {
                this.selectedVersionProperties ??= selectedVersion.properties;
            });

        this.widgetPropertiesService.propertiesChange$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(customProperties => {
                this.updateProperties(customProperties);
            });
    }

    async ngOnInit(): Promise<void> {
        this.isSDAEnabled = await this.userService.hasPermission('SocialDynamicAdvertising');
        this.elements$
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                distinctUntilChanged(
                    (prev, curr) =>
                        prev.length === curr.length &&
                        curr.every(currElement =>
                            prev.find(prevElement => prevElement.id === currElement.id)
                        )
                ),
                filter(elements => elements.length > 0)
            )
            .subscribe(elements => {
                this.element = elements[0];
                this.updateProperties();
            });
    }

    /**
     * Apply the changed values to the element
     */
    private propertyValueChanged(eventType: ElementChangeType = ElementChangeType.Burst): void {
        const properties = this.mappedProperties[this.element.id];
        this.mutatorService.setElementPropertyValue(
            this.element,
            'customProperties',
            properties,
            eventType
        );

        this.changeDetector.markForCheck();
    }

    /**
     * Resets the properties of the widget to the values defined in the brand library.
     * This is used when the user clicks the "Reset to library" button in the properties panel.
     */
    resetProperties(): void {
        const libraryElement = this.brandLibraryDataService.getElementById(this.element.parentId);
        if (!libraryElement) {
            throw new Error('Could not find any library elements with the same id.');
        }

        this.resetToLibraryProperties(libraryElement, this.element);
    }

    isFirstTextProperty(property: IWidgetCustomProperty): boolean {
        const textProperty = this.widgetProperties.filter(p => p.unit === 'text').at(0);
        if (!textProperty) {
            return false;
        }
        return textProperty.name === property.name;
    }

    /**
     * Updates the brand library element custom properties with the current values of the widget element.
     * This is used when the user clicks the "Update in library" button in the properties panel.
     */
    async updateLibraryElementProperties(): Promise<void> {
        const libraryElement = this.brandLibraryDataService.getElementById(this.element.parentId);

        if (!libraryElement) {
            throw new Error('Could not find any library elements with the same id.');
        }

        const result = await this.uiConfirmDialogService.confirm({
            headerText: 'Update in library',
            text: `Are you sure you want to update the widget "${
                libraryElement.name
            }" in the brand library? ${this.preview ? '' : 'This action cannot be undone.'}`,
            confirmText: 'Update',
            cancelText: 'Cancel'
        });

        if (result !== 'confirm') {
            return;
        }

        libraryElement.properties = libraryElement.properties.filter(isAnyWidgetReference);

        // Add custom properties with the new values which are currently defined in the properties panel
        for (const property of this.widgetProperties) {
            let value: string | undefined;

            if (property.unit === 'text') {
                const versionedValue = this.widgetService.getVersionedCustomPropertyValue(
                    property,
                    true
                );
                value = sanitizeString(versionedValue as string);
            } else {
                value = serializeWidgetPropertyValue(property.unit, property.value);
            }

            const elementProperty = getElementPropertyByName(this.element, property.name);
            const newProperty = createElementProperty({
                name: applyCustomPropertyPrefix(property.name),
                unit: property.unit,
                value: value,
                label: sanitizeString(property.label),
                versionPropertyId: property.versionPropertyId,
                hasDynamicContent: elementProperty?.hasDynamicContent
            });

            libraryElement.properties.push(newProperty);
        }

        try {
            if (this.preview) {
                this.widgetPropertiesService.updateLibraryElement(this.widgetProperties);
            } else {
                await firstValueFrom(this.brandLibraryDataService.updateElement(libraryElement));
            }
        } catch (error) {
            handleError('Could not update element in brand library', {
                contexts: { originalError: error }
            });
        }
    }

    openWidgetInfo(): void {
        if (this.isEffectWidget) {
            this.widgetService.openEffectInfoPage();
            return;
        }

        this.widgetService.openWidgetInfoPage();
    }

    private updateProperties(properties?: IWidgetCustomProperty[]): void {
        const props = properties || this.element.customProperties;
        this.widgetProperties = [];
        if (!this.element || !props) {
            return;
        }

        if (this.brandLibraryDataService.brandLibrary) {
            const libraryWidget = this.brandLibraryDataService.getElementByDataNode(this.element);

            if (libraryWidget) {
                this.isEffectWidget = isEffectLibraryElement(libraryWidget);
            }

            this.isBannerflowLibraryWidget =
                libraryWidget?.type === ElementKind.BannerflowLibraryWidget;
        } else {
            this.isBannerflowLibraryWidget = true;
        }

        this.populateProperties(props);
    }

    private resetToLibraryProperties(
        libraryWidget: IBrandLibraryElement,
        dataNode: IWidgetElementDataNode
    ): void {
        const newProperties = this.getNewProperties(libraryWidget, dataNode);
        const properties = this.cloneMappedProperties(this.mappedProperties[this.element.id]);
        const currentVersion = this.editorStateService.currentVersion;
        const currentVersionProperties = this.selectedVersionProperties;

        for (const property of properties) {
            const newProperty = newProperties.find(
                ({ name, unit }) => name === property.name && unit === property.unit
            );

            if (newProperty) {
                property.value = newProperty.value;

                const versionProperty = currentVersionProperties.find(
                    ({ id }) => id === property.versionPropertyId
                );

                if (versionProperty && isVersionedProperty(property)) {
                    this.versionsService.upsertVersionProperty(currentVersion.id.toString(), {
                        ...versionProperty,
                        value: property.value
                    });
                }
            }
        }

        this.widgetProperties = properties;
        this.mappedProperties[this.element.id] = properties;
        this.propertyValueChanged();
    }

    private getNewProperties(
        libraryWidget: IBrandLibraryElement,
        dataNode: IWidgetElementDataNode
    ): IWidgetCustomProperty[] {
        return mapElementToCustomWidgetProperties(libraryWidget).map(property => {
            const elementProperty = dataNode.globalElement.properties.find(
                ({ name, unit }) => name === property.name && unit === property.unit
            );

            return {
                ...property,
                versionPropertyId: elementProperty?.versionPropertyId
            };
        });
    }

    private populateProperties(properties: IWidgetCustomProperty[]): void {
        this.mappedProperties[this.element.id] = this.cloneMappedProperties(properties);
        this.widgetProperties = this.cloneMappedProperties(this.mappedProperties[this.element.id]);

        this.propertyValueChanged();
    }

    private cloneMappedProperties(properties: IWidgetCustomProperty[]): IWidgetCustomProperty[] {
        return structuredClone(properties).map(property => {
            if (property.unit === 'color') {
                return {
                    ...property,
                    value: new Color(property.value as IColor)
                };
            }

            return property;
        });
    }
}
