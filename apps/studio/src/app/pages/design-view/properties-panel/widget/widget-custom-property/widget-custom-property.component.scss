:where(:root:not([data-uinew])) :host {
    .setting-row {
        &.full-width {
            grid-template-columns: 100%;

            .property {
                justify-self: auto;
            }
        }

        &.input-width {
            grid-template-columns: auto 1fr;
        }
    }

    .property {
        justify-self: end;

        &-input > ui-number-input {
            max-width: 45px;
        }
    }

    .ui-select,
    ui-textarea,
    .font-picker {
        width: 10rem;
    }

    .setting-label {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .info {
            vertical-align: middle;
        }
    }

    .setting-value {
        .ui-select {
            &::ng-deep {
                .button {
                    min-width: unset;
                    max-width: 9.8rem;
                    width: 100%;
                }

                .text {
                    padding-right: 2rem;
                }
            }
        }
    }

    .dynamic-text-popover-content {
        padding: 5px 10px;
    }
}
