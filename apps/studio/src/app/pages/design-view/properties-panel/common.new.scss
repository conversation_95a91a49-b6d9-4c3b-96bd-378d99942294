:where(:root[data-uinew]) :host {
    studio-ui-section {
        &:not(:has(.setting-row)) .setting-value {
            margin-bottom: var(--nui-space-200);
        }
        color-button + .setting-value {
            margin-bottom: 0;
        }
    }

    .setting-row:first-child,
    .setting:not(:has(.setting-row)) {
        margin-top: var(--nui-space-100);
    }

    // NOTE: setting-row is not present in all properties
    // TODO: normalize all properties to follow this structure after uplift migration and remove redundant classes
    // .setting-row > .setting-label, .setting-value.slider > .property-input
    .setting-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--nui-space-300);
        margin-bottom: var(--nui-space-200);
        min-height: 2.4rem;

        ui-label {
            --color: var(--nui-text-secondary);
        }
        // This is used to show the line under the label to connect content in radius and paddings on separate mode
        &.with-more-content .setting-label:after {
            content: '';
            position: absolute;
            top: calc(50% + 1.2rem);
            left: var(--more-content-arrow-left, calc(50% - 7.5px));
            width: 15px;
            height: var(--more-content-arrow-height, calc(50% + var(--nui-space-200)));
            border-left: 1px solid var(--nui-border-neutral-secondary-bold);
            border-bottom: 1px solid var(--nui-border-neutral-secondary-bold);
            border-bottom-left-radius: var(--nui-border-radius-tiny);
        }
    }
    // TODO: refactor labels to use ui-label component after uplift migration
    .setting-label {
        position: relative;
        display: flex;
        flex-wrap: nowrap;
        align-self: stretch;
        align-items: center;
        white-space: nowrap;
        color: var(--nui-text-secondary);
        gap: var(--nui-space-100);
        font-family: var(--nui-label-regular-font-family);
        font-weight: var(--nui-label-regular-font-weight);
        font-size: var(--nui-label-regular-font-size);
        line-height: var(--nui-label-regular-line-height);
        letter-spacing: var(--nui-label-regular-letter-spacing);
    }
    .setting-value {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--nui-space-200);

        .property-input {
            display: flex;
            width: 100%;
            justify-content: flex-end;
        }

        &.number-setting {
            ui-number-input {
                width: 12rem;
            }
        }
        &.col-1 {
            ui-select {
                width: 9rem;
            }
            &.wide ui-select {
                width: 12rem;
            }
            &:has(ui-button-group) {
                ui-number-input {
                    width: 4.5rem;
                }
            }
        }

        &.col-2 {
            ui-number-input {
                width: 4.5rem;
            }
            &:has(ui-button) {
                gap: var(--nui-space-200);
                ui-select {
                    width: 12rem;
                }
            }
        }
        &.col-3 {
            gap: var(--nui-space-100);
            &:has(ui-button) {
                gap: 0;
                ui-range {
                    width: 6.5rem;
                    margin-right: var(--nui-space-300);
                }

                ui-number-input {
                    width: 4.5rem;
                    margin-right: var(--nui-space-200);
                }
            }
        }
        &.col-4 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0;
            &:has(ui-button) {
                ui-range {
                    width: 6.5rem;
                    margin-right: var(--nui-space-300);
                }
                ui-select {
                    width: 9rem;
                    margin-right: var(--nui-space-200);
                }
                ui-number-input {
                    width: 4.5rem;
                    margin-right: var(--nui-space-200);
                }
                :first-child {
                    flex: 1;
                }
            }
        }
        &.slider:not(:has(ui-button)) {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--nui-space-300);

            ui-range {
                width: 7.9rem;
            }
            ui-number-input {
                width: 4.5rem;
            }
            :first-child {
                flex: 1;
            }
        }
        &:has(color-button):not(.shadow) {
            width: 100%;
            justify-content: flex-end;
            &.only-setting-child {
                height: 3.2rem;
            }
        }
        .input-group.shadow {
            margin-bottom: 0;
        }
        &.shadow {
            height: 2.4rem;
            &:first-child {
                margin-top: var(--nui-space-200);
            }
        }

        ui-select {
            width: 100%;
        }
    }

    .placeholder-text {
        margin-bottom: var(--nui-space-200);
    }
    .rotate-90 {
        transform: rotate(90deg);
    }
    .rotate-180 {
        transform: rotate(180deg);
    }
    .rotate-270 {
        transform: rotate(270deg);
    }
}
