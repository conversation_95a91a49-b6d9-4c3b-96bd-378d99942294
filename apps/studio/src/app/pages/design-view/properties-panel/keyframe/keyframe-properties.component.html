@if (keyframes.length) {
    <studio-ui-section
        class="no-padding"
        headline="Keyframe{{ keyframes.length > 1 ? 's' : '' }}"
        [isNewUI]="isNewUI()">
        <div class="section-body">
            <div class="body">
                <div class="setting">
                    <!-- Timing function properties-->
                    <div
                        class="setting-row alternate"
                        [class.inactive]="selectedTimingFunctionKey === undefined">
                        <div
                            class="setting-label"
                            [uiTooltip]="
                                'The easing of the animation between<br> this keyframe and the previous one'
                            ">
                            Easing
                        </div>
                        <div class="setting-value easing-setting col-1 wide">
                            <ui-select
                                size="xs"
                                type="secondary"
                                [selected]="selectedTimingFunctionKey"
                                (selectedChange)="applyTimingFunction($event)"
                                placeholder="Various"
                                allowEmpty="true"
                                [useTargetWidth]="true">
                                @for (
                                    timingFunction of timingFunctions | keyvalue;
                                    track timingFunction.key
                                ) {
                                    <ui-option [value]="timingFunction.key">
                                        {{ timingFunction.value.name }}
                                    </ui-option>
                                }
                            </ui-select>
                        </div>
                    </div>
                    <!-- Duration -->
                    <div
                        class="setting-row wide"
                        [class.inactive]="duration === undefined">
                        <div
                            class="setting-label"
                            [uiTooltip]="
                                'Time this keyframe will be still<br> before animating to next keyframe'
                            ">
                            Duration
                        </div>
                        <div
                            class="setting-value col-2 wide"
                            [class.slider]="isNewUI()">
                            <div
                                class="property-input slider"
                                [class.slider]="!isNewUI()">
                                <ui-range
                                    size="xs"
                                    [value]="duration || 0"
                                    (valueChange)="applyDuration($event)"
                                    [min]="0"
                                    [max]="maxDuration"
                                    [step]="0.01"></ui-range>
                            </div>
                            <div class="property-input">
                                <ui-number-input
                                    size="xs"
                                    type="secondary"
                                    [arrowButtons]="!isNewUI()"
                                    [value]="duration"
                                    (valueChange)="applyDuration($event)"
                                    [keyboardEmit]="true"
                                    [placeholder]="'-'"
                                    [min]="0"
                                    [max]="maxDuration"
                                    [step]="durationStep"
                                    [allowEmpty]="true"
                                    [disableUndo]="true"></ui-number-input>
                            </div>
                        </div>
                    </div>
                    <!-- Time -->
                    <div
                        class="setting-row wide"
                        [class.disabled]="keyframes.length > 1">
                        <div
                            class="setting-label"
                            [uiTooltip]="
                                'Position in time of this keyframe<br> relative to the element'
                            ">
                            Time
                        </div>
                        <div
                            class="setting-value col-2 wide"
                            [class.slider]="isNewUI()">
                            <div
                                class="property-input slider"
                                [class.slider]="!isNewUI()">
                                <ui-range
                                    size="xs"
                                    [value]="time || 0"
                                    (valueChange)="applyTime($event)"
                                    [min]="0"
                                    [max]="maxTime"
                                    [step]="0.01"></ui-range>
                            </div>
                            <div class="property-input">
                                <ui-number-input
                                    size="xs"
                                    type="secondary"
                                    [arrowButtons]="!isNewUI()"
                                    [value]="time"
                                    [placeholder]="'-'"
                                    (valueChange)="applyTime($event)"
                                    [keyboardEmit]="true"
                                    [min]="0"
                                    [max]="maxTime"
                                    [step]="timeStep"
                                    [allowEmpty]="true"
                                    [disableUndo]="true"></ui-number-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </studio-ui-section>
}
