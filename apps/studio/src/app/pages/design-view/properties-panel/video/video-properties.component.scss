:where(:root:not([data-uinew])) :host {
    .setting-row {
        .setting-label {
            &.margin-right {
                margin-right: 10px;
            }
        }
    }
}

:where(:root[data-uinew]) :host {
    studio-ui-section {
        padding-bottom: var(--nui-space-200);
    }
    asset-property {
        display: block;
        margin-bottom: var(--nui-space-200);
    }
    .setting {
        &:first-child {
            margin-top: var(--nui-space-100);
        }
        ui-button {
            --width: 100%;
        }
    }
    .play-pause-setting {
        --more-content-arrow-left: var(--nui-space-200);
        --more-content-arrow-height: 5.1rem;
    }
    .playback-options {
        margin-left: 40px;
    }
}
