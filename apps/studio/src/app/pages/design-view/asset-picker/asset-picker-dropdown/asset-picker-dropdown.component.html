<ui-dropdown
    #dropdown
    size="sm"
    [positions]="positions"
    [offset]="offset"
    [width]="isNewUI() ? '180' : 'auto'"
    type="menu">
    <ui-dropdown-item
        (click)="openAssetPickerDialog()"
        data-test-id="add-from-brand-library"
        svgIcon="folder-multiple"
        nuiIcon="folder_copy">
        From brand library
    </ui-dropdown-item>
    @if (allowFeed) {
        <ui-dropdown-item
            (click)="openFeedPickerDialog()"
            svgIcon="feed"
            nuiIcon="rss_feed">
            From feed
        </ui-dropdown-item>
    }
    @if (allowUpload) {
        <ui-dropdown-item
            (click)="handleFromDevice(fileInput)"
            [svgIcon]="'upload-file'"
            style="cursor: pointer"
            nuiIcon="upload">
            From your device
            <input
                id="upload-file"
                style="display: none"
                type="file"
                multiple
                [accept]="fileTypes"
                #fileInput
                (change)="uploadFiles(fileInput.files)" />
        </ui-dropdown-item>
    }

    @if (allowRemove) {
        <ui-dropdown-item
            (click)="removeAsset()"
            svgIcon="delete"
            nuiIcon="delete">
            Remove {{ assetType }}
        </ui-dropdown-item>
    }
</ui-dropdown>
