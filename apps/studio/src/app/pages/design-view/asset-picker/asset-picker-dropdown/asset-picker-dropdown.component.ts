import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
    Input,
    OnChanges,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Logger } from '@bannerflow/sentinel-logger';
import {
    UIConfirmDialogResult,
    UIConfirmDialogService,
    UIDialogRef,
    UIDialogService,
    UIDropdownComponent,
    UIModule,
    UINewThemeService
} from '@bannerflow/ui';
import {
    getLibraryKindFromElementKind,
    isImageNode,
    isVideoElement,
    isVideoNode,
    isWidgetNode
} from '@creative/nodes';
import { IBrandLibraryElement } from '@domain/brand/brand-library';
import { ElementKind } from '@domain/elements';
import { LibraryKind } from '@domain/media-library';
import { CreativesetDataService } from '@studio/common';
import { BrandLibraryDataService } from '../../../../shared/media-library/brand-library.data.service';
import { HeavyVideoService } from '../../../../shared/services/heavy-video.service/heavy-video.service';
import { MediaLibraryComponent } from '../../media-library/media-library.component';
import { AssetPropertyContext } from '../../properties-panel/asset-property/asset-property';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { AssetPickerUploadService } from '../asset-picker-upload.service';
import { IFeedItemSelection } from '../asset-picker.component';
import { AssetPickerService } from '../asset-picker.service';

enum FileType {
    Image = '.jpg, .jpeg, .png, .svg, .gif',
    Video = '.mp4'
}

@Component({
    selector: 'asset-picker-dropdown',
    templateUrl: 'asset-picker-dropdown.component.html',
    imports: [UIModule],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AssetPickerDropdownComponent implements OnChanges {
    private assetPickerService = inject(AssetPickerService);
    private brandLibraryDataService = inject(BrandLibraryDataService);
    private creativesetDataService = inject(CreativesetDataService);
    private editorStateService = inject(EditorStateService);
    private elementSelectionService = inject(ElementSelectionService);
    private heavyVideoService = inject(HeavyVideoService);
    private injector = inject(Injector);
    private uiConfirmDialogService = inject(UIConfirmDialogService);
    private uiDialogService = inject(UIDialogService);
    private uiNewThemeService = inject(UINewThemeService);
    private uploadService = inject(AssetPickerUploadService);

    @Input() positions: UIDropdownComponent['positions'];
    @Input() offset: UIDropdownComponent['offset'] = { x: 0, y: 0 };
    @Input() assetType: ElementKind.Image | ElementKind.Video = ElementKind.Image;
    @Input() context: AssetPropertyContext;
    @Input() allowUpload: boolean;
    @Input() allowRemove: boolean;
    @Input() allowFeed: boolean;

    @ViewChild('dropdown', { static: true }) dropdown: UIDropdownComponent;

    fileTypes = FileType.Image;

    private dialogRef: UIDialogRef;
    private logger = new Logger('AssetPickerDropdownComponent');
    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor() {
        this.uploadService.assetUploadComplete$.pipe(takeUntilDestroyed()).subscribe(async asset => {
            let replaceAll = false;
            if (this.context === AssetPropertyContext.Replace) {
                if (this.isElementUsedInOtherDesigns()) {
                    const result = await this.confirmReplaceAll();
                    if (result === 'cancel') {
                        return;
                    }
                    replaceAll = result === 'confirm';
                }
            }
            this.assetPickerService.selectAsset({
                asset,
                replaceAll
            });
        });
    }

    ngOnChanges(): void {
        if (this.assetType === ElementKind.Image) {
            this.fileTypes = FileType.Image;
        } else if (this.assetType === ElementKind.Video) {
            this.fileTypes = FileType.Video;
        }
    }

    openAssetPickerDialog(): void {
        const libraryKind = getLibraryKindFromElementKind(this.assetType);
        this.openLibraryDialog(libraryKind);
    }

    openFeedPickerDialog(): void {
        this.openLibraryDialog(LibraryKind.Feeds);
    }

    private openLibraryDialog(filter: LibraryKind = LibraryKind.Image): void {
        const assetTypeLabel = this.assetType === ElementKind.Video ? 'Video' : 'Image';
        const elements = this.elementSelectionService.currentSelection.elements;

        const selectedAssetIds = elements.map(element => {
            if (isImageNode(element)) {
                return element.imageAsset?.id;
            }

            if (isVideoNode(element)) {
                return element.videoAsset?.id;
            }
        });

        const isMixedAssets = new Set(selectedAssetIds).size > 1;

        const headerText =
            this.context === AssetPropertyContext.Replace
                ? `Replace ${assetTypeLabel} Content`
                : `Choose ${assetTypeLabel}`;

        this.dialogRef = this.uiDialogService.openComponent(MediaLibraryComponent, {
            injector: this.injector,
            width: '100%',
            height: '100%',
            maxWidth: '100%',
            padding: 0,
            theme: 'default',
            headerText,
            panelClass: ['no-padding', 'fullscreen'],
            data: {
                context: this.context,
                multiselect: false,
                filter,
                callback: this.useSelection,
                showReplaceAll: this.isElementUsedInOtherDesigns(),
                feed: !isMixedAssets && elements?.length && elements[0].feed,
                assetType: this.assetType
            }
        });
    }

    handleFromDevice(fileInput: HTMLInputElement): void {
        fileInput.value = '';
        fileInput.click();
    }

    useSelection = async (
        selection: IBrandLibraryElement | IFeedItemSelection | undefined,
        replaceAll: boolean
    ): Promise<void> => {
        if (!selection) {
            return;
        }
        if ('selectedFeed' in selection) {
            this.assetPickerService.selectFeed({ selection, replaceAll });
        } else {
            await this.useSelectedAsset(selection, replaceAll);
        }
        this.dialogRef.close();
    };

    uploadFiles(fileList: FileList | null): void {
        if (!fileList) {
            this.logger.warn('No file to upload!');
            return;
        }

        this.uploadService.uploadAssets(Array.from(fileList));
    }

    removeAsset(): void {
        this.assetPickerService.removeAsset();
    }

    private confirmReplaceAll(): Promise<UIConfirmDialogResult> {
        return this.uiConfirmDialogService.confirm({
            headerText: `Replace ${this.assetType}`,
            text: `This ${this.assetType} is used in multiple sizes. Do you want to replace it in all other sizes too?`,
            cancelText: 'No',
            confirmText: 'Yes',
            discardText: 'No',
            showCancelButton: false
        });
    }

    private isElementUsedInOtherDesigns(): boolean {
        const elements = this.elementSelectionService.currentSelection.elements;
        const isWidgetInSelection = elements.some(element => isWidgetNode(element));
        if (isWidgetInSelection) {
            return false;
        }

        const currentDesignId = this.editorStateService.designId;
        const otherDesigns = this.creativesetDataService.creativeset.designs.filter(
            design => design.id !== currentDesignId
        );
        for (const design of otherDesigns) {
            for (const element of elements!) {
                const elementExists = design.elements.some(el => el.id === element.id);
                if (elementExists) {
                    return true;
                }
            }
        }

        return false;
    }

    private async useSelectedAsset(
        selection: IBrandLibraryElement,
        replaceAll: boolean
    ): Promise<void> {
        const libraryAsset = this.brandLibraryDataService.getAssetByElement(selection);

        if (!libraryAsset) {
            throw new Error('Could not get libraryAsset from element');
        }

        const isHeavyVideo = this.heavyVideoService.isHeavyVideo(libraryAsset.fileSize);
        if (isVideoElement(selection) && isHeavyVideo) {
            const allowVideo = await this.heavyVideoService.promptHeavyVideo();
            if (!allowVideo) {
                return;
            }
        }

        this.assetPickerService.selectAsset({ asset: libraryAsset, replaceAll });
    }
}
