@if (assetType === 'image' && !isFeed) {
    @if (isNewUI() && !isMixed && name && context !== 'widget-image') {
        <ui-label
            size="sm"
            class="asset-name"
            type="secondary"
            [truncate]="true"
            [uiTooltip]="name"
            [uiTooltipDisabled]="!name"
            [uiTooltipPosition]="'left'">
            {{ name }}
        </ui-label>
    }
    <div class="details-preview-wrapper">
        <div class="preview">
            <div
                class="image-wrapper"
                [uiDropdownTarget]="dropdown.dropdown">
                @if (isMixed) {
                    <div class="no-image mixed">
                        <ui-svg-icon
                            icon="progress"
                            nuiIcon="photo_library"></ui-svg-icon>
                    </div>
                } @else {
                    @if (thumbnailUrl) {
                        <div
                            class="image"
                            [style.backgroundImage]="thumbnailUrl"
                            [class.empty-state]="
                                propertiesService.stateValueIsUndefined('imageAsset') ||
                                propertiesService.stateValueIsUndefined('videoAsset')
                            "></div>
                    } @else {
                        <div class="no-image">
                            <ui-svg-icon
                                icon="plus"
                                nuiIcon="add"></ui-svg-icon>
                        </div>
                    }
                }
            </div>
        </div>

        <div class="details">
            @if (!showReplaceButton) {
                @if (isNewUI()) {
                    <ui-label
                        size="sm"
                        type="secondary">
                        {{ label }}
                    </ui-label>
                } @else {
                    <div class="details__label">
                        {{ label }}
                    </div>
                }
            }
            @if (isSizeModeEnabled && context !== 'widget-image') {
                <size-mode-options
                    [sizeMode]="sizeMode"
                    (sizeModeChanged)="onUpdateImageSizeMode($event)"></size-mode-options>
            }

            @if (showReplaceButton) {
                @if (isNewUI()) {
                    <ui-button
                        nuiType="plain-primary"
                        [selected]="true"
                        size="xs"
                        [text]="'Replace ' + assetType"
                        [uiDropdownTarget]="dropdown.dropdown" />
                } @else {
                    <div
                        class="replace-image-button"
                        data-test-id="replace-image-button"
                        [uiDropdownTarget]="dropdown.dropdown">
                        {{ 'Replace ' + assetType }}
                    </div>
                }
            }
        </div>
    </div>
}

@let isFeededImage = assetType === 'image' && isFeed;
@if (assetType === 'video' || isFeededImage) {
    <div class="details-preview-wrapper">
        <div class="details">
            @if (!showReplaceButton) {
                @if (isNewUI()) {
                    <ui-label
                        size="sm"
                        type="secondary">
                        {{ label }}
                    </ui-label>
                } @else {
                    <div class="details__label">
                        {{ label }}
                    </div>
                }
            }
            @if (isNewUI()) {
                <div #popoverTarget>
                    <ui-label
                        [uiTooltip]="name"
                        [uiTooltipDisabled]="!name"
                        size="sm"
                        [type]="isFeed ? 'primary' : 'secondary'"
                        [style.cursor]="isFeed ? 'pointer' : 'default'"
                        [style.user-select]="isFeed ? 'none' : 'text'"
                        [truncate]="true"
                        (click)="toggleFeedSettings()">
                        {{ isMixed ? 'Mixed' : name || 'No image selected' }}
                    </ui-label>
                </div>
            } @else {
                <div
                    #popoverTarget
                    [uiTooltip]="name"
                    [uiTooltipDisabled]="!name"
                    class="details__name"
                    [class.isFeed]="isFeed"
                    (click)="toggleFeedSettings()">
                    {{ isMixed ? 'Mixed' : name || 'No image selected' }}
                </div>
            }

            @if (showReplaceButton) {
                <ui-button
                    size="xs"
                    nuiType="plain-primary"
                    [selected]="true"
                    type="primary"
                    [capitalize]="!isNewUI()"
                    [uiDropdownTarget]="dropdown.dropdown"
                    [text]="'Replace ' + assetType"></ui-button>
            }
        </div>

        <div class="preview">
            <div class="type">
                @if (icon() || nuiIcon()) {
                    <ui-svg-icon
                        [icon]="icon() ?? 'none'"
                        [nuiIcon]="nuiIcon()"></ui-svg-icon>
                }
            </div>
            <div
                class="image-wrapper"
                [uiDropdownTarget]="dropdown.dropdown">
                @if (isMixed) {
                    <div class="no-image mixed">
                        <ui-svg-icon
                            icon="progress"
                            nuiIcon="photo_library"></ui-svg-icon>
                    </div>
                } @else {
                    @if (thumbnailUrl) {
                        <div
                            class="image"
                            [style.backgroundImage]="thumbnailUrl"
                            [class.empty-state]="
                                propertiesService.stateValueIsUndefined('imageAsset') ||
                                propertiesService.stateValueIsUndefined('videoAsset')
                            "></div>
                    } @else {
                        <div class="no-image">
                            <ui-svg-icon
                                icon="plus"
                                nuiIcon="add"></ui-svg-icon>
                        </div>
                    }
                }
            </div>
        </div>
    </div>

    @if (isFeededImage && isSizeModeEnabled && context !== 'widget-image') {
        <size-mode-options
            [sizeMode]="sizeMode"
            (sizeModeChanged)="onUpdateImageSizeMode($event)"></size-mode-options>
    }
}

<asset-picker-dropdown
    #dropdown
    [offset]="{ x: 15, y: -20 }"
    [assetType]="assetType"
    [context]="context"
    [allowUpload]="allowUpload"
    [allowRemove]="allowRemove && asset !== undefined"
    [allowFeed]="allowFeed">
</asset-picker-dropdown>
