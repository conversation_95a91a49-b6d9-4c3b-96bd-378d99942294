@use 'variables' as *;
@use '../properties-panel/common';

:where(:root[data-uinew]) :host {
    display: flex;
    flex-direction: column;
    gap: var(--nui-space-200);

    .asset-name {
        user-select: text;
        width: 100%;
    }

    &.with-replace-button {
        .preview {
            pointer-events: none;
        }
    }

    .details-preview-wrapper {
        display: flex;
        justify-content: space-between;
        gap: var(--nui-space-400);
        width: 100%;
    }

    asset-picker-dropdown {
        display: none;
    }

    .details {
        display: flex;
        flex-direction: column;
        width: 100%;
        overflow: hidden;

        ui-button {
            margin-top: var(--nui-space-300);
        }
    }

    .preview {
        display: flex;
    }

    .type {
        align-self: flex-end;
        padding: 0 var(--nui-space-050) var(--nui-space-050) 0;

        ui-svg-icon {
            --color: var(--nui-icon-disabled);
        }
    }

    .image-wrapper {
        width: 60px;
        height: 60px;
        cursor: pointer;
        background-color: var(--nui-surface-neutral-subtler);
        border: 1px solid var(--nui-border-neutral-subtle);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        border-radius: var(--nui-border-radius-small);

        &::after {
            z-index: 0;
            content: '';
            display: block;
            background-size: 8px 8px;
            position: absolute;
            top: var(--nui-space-050);
            left: var(--nui-space-050);
            width: calc(100% - var(--nui-space-050) * 2);
            height: calc(100% - var(--nui-space-050) * 2);
            background-position: center;
            background-repeat: repeat;
            background-image: $chessBackgroundUrl;
        }

        .image,
        .no-image {
            width: 100%;
            height: 100%;
            border-radius: var(--nui-border-radius-small);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 1;

            ui-svg-icon {
                --color: var(--nui-icon-secondary);
            }
        }

        .no-image {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--nui-surface-neutral-subtler);

            &.mixed {
                ui-svg-icon {
                    --color: var(--nui-icon-disabled);
                }
            }
        }
    }
}
