import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ElementKind } from '@domain/elements';
import { ImageSizeMode } from '@domain/image';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createBrandLibraryMock } from '@mocks/brand-library.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createEditorSaveStateServiceMock } from '@mocks/services/editor-save-state.service.mock';
import { BrandService, CreativesetDataService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { PropertiesService } from '../properties-panel/properties.service';
import { CopyPasteService } from '../services/copy-paste.service';
import { EditorSaveStateService } from '../services/editor-save-state.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementCreatorService } from '../services/element-creator.service';
import { ElementSelectionService } from '../services/element-selection.service';
import { HistoryService } from '../services/history.service';
import { KeyframeService } from '../timeline';
import { BrandLibraryElementService } from './brandlibrary-element.service';

describe('BrandLibraryElementService', () => {
    let brandLibraryElementService: BrandLibraryElementService;
    const mockElement = createDataNodeMock({
        kind: ElementKind.Image,
        imageSettings: {
            sizeMode: ImageSizeMode.Fill,
            x: 10,
            y: 10
        },
        imageAsset: {
            width: 100,
            height: 100,
            id: 'id',
            url: ''
        }
    });

    beforeEach(() => {
        defineMatchMedia();
        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideEnvironment(environment),
                EnvironmentService,
                BrandLibraryElementService,
                provideMock(CopyPasteService),
                {
                    provide: BrandLibraryDataService,
                    useValue: {
                        createElement: (): object => of({}),
                        brandLibrary: createBrandLibraryMock({
                            id: 'brandLibraryId',
                            elements: [createDataNodeMock({ id: 'id' })]
                        })
                    }
                },
                {
                    provide: BrandService,
                    useValue: {
                        brandId$: of('1'),
                        accountSlug$: of()
                    }
                },
                { provide: ElementCreatorService, useValue: {} },
                {
                    provide: CreativesetDataService,
                    useValue: createMockCreativesetDataService()
                },
                {
                    provide: EditorStateService,
                    useValue: createMockEditorStateService({
                        renderer: createRendererFixture(300, 250, [createDataNodeMock(mockElement)])
                    })
                },
                HistoryService,
                {
                    provide: PropertiesService,
                    useValue: {}
                },
                {
                    provide: KeyframeService,
                    useValue: {}
                },
                {
                    provide: ElementSelectionService,
                    useValue: {}
                },
                {
                    provide: EditorSaveStateService,
                    useValue: createEditorSaveStateServiceMock()
                }
            ]
        });

        brandLibraryElementService = TestBed.inject(BrandLibraryElementService);
    });

    it('should be created', () => {
        expect(brandLibraryElementService).toBeTruthy();
    });
});
