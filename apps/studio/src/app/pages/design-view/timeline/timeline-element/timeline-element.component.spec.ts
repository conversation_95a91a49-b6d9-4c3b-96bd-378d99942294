import { ElementRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import { createDataNodeMock } from '@mocks/element.mock';
import { createEditorEventServiceMock } from '@mocks/services/editor-event-service.mock';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { BehaviorSubject, of } from 'rxjs';
import { IsElementsPipe } from '../../pipes/is-element.pipe';
import { IsGroupPipe } from '../../pipes/is-group.pipe';
import { EditorEventService } from '../../services/editor-event';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementHighlightService } from '../../services/element-highlight.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { HistoryService } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { AnimationRecorderService } from '../animation-recorder.service';
import { StudioTimelineComponent } from '../studio-timeline/studio-timeline.component';
import { TimelineScrollService } from '../timeline-scroll.service';
import { TimelineTransformService } from '../timeline-transformer.service';
import { TimelineZoomService } from '../timeline-zoom.service';
import { AnimationService } from './animation.service';
import { KeyframeService } from './keyframe.service';
import { TimelineAnimationComponent } from './timeline-animation/timeline-animation.component';
import { TimelineElementGizmoDrawer } from './timeline-element-gizmo-drawer';
import { TimelineElementComponent } from './timeline-element.component';
import { TimelineElementService } from './timeline-element.service';
import { HeavyVideoService } from '@app/shared/services/heavy-video.service/heavy-video.service';
import { FFFeatureFlagsService } from '@bannerflow/feature-flags';

class MockElementRef extends ElementRef {}

describe('TimelineElementComponent', () => {
    let component: TimelineElementComponent;
    let fixture: ComponentFixture<TimelineElementComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [
                TimelineElementComponent,
                TimelineAnimationComponent,
                StudioTimelineComponent,
                IsGroupPipe,
                IsElementsPipe
            ],
            imports: [RouterTestingModule, ApolloTestingModule, UIModule],
            providers: [
                { provide: ElementRef, useClass: MockElementRef },
                { provide: EditorStateService, useValue: null },
                {
                    provide: EditorEventService,
                    useValue: createEditorEventServiceMock()
                },
                { provide: TimelineAnimationComponent, useValue: null },
                { provide: TimelineElementGizmoDrawer, useValue: null },
                {
                    provide: StudioTimelineComponent,
                    useValue: {
                        selectionNet: {},
                        zoomService: { zoom: 0 },
                        secondsToScrolledPixels: jest.fn(),
                        secondsToPixels: jest.fn(),
                        windowResize$: of()
                    }
                },
                {
                    provide: TimelineTransformService,
                    useValue: {
                        change$: new BehaviorSubject({}),
                        timelineChange: jest.fn()
                    }
                },
                {
                    provide: TimelineElementService,
                    useValue: { isRenaming: false, toggleGroup: jest.fn(), toggleVisibility$: of() }
                },
                { provide: KeyframeService, useValue: { change$: of() } },
                {
                    provide: AnimationService,
                    useValue: { addAnimation$: of(), deleteAnimations$: of() }
                },
                { provide: TimelineScrollService, useValue: { isScrolling: false, scroll$: of() } },
                { provide: TimelineZoomService, useValue: { zoom: 0, zoom$: of() } },
                {
                    provide: HistoryService,
                    useValue: {
                        snapshotApply$: of()
                    }
                },
                { provide: AnimationRecorderService, useValue: { recording$: of() } },
                { provide: MutatorService, useValue: { renderer: { getViewElementById: jest.fn() } } },
                { provide: ElementSelectionService, useValue: { change$: of() } },
                { provide: ElementHighlightService, useValue: { elementHighlight$: of() } },
                {
                    provide: HeavyVideoService,
                    useValue: { promptHeavyVideoOnVisibilityChange: jest.fn() }
                },
                {
                    provide: FFFeatureFlagsService,
                    useValue: {
                        isEnabled$: jest.fn().mockReturnValue(of(false))
                    }
                }
            ]
        }).compileComponents();
        fixture = TestBed.createComponent(TimelineElementComponent);
        component = fixture.componentInstance;
        component.node = createDataNodeMock({ id: '1', name: 'test', kind: ElementKind.Group });

        component.gizmoDrawer = {
            destroy: jest.fn()
        } as any;
        component.canvas = new ElementRef(document.createElement('canvas'));
        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should render folder icon for toggled and untoggled group timeline elements', () => {
        component.inHiddenNodeTree = false;
        component.toggleGroup();

        const icon = fixture.nativeElement.querySelector('#toggle-group');
        expect(icon).toBeTruthy();
        expect(icon.class).not.toBe('hidden');

        component.toggleGroup();

        expect(icon).toBeTruthy();
        expect(icon.class).not.toBe('hidden');
    });
});
