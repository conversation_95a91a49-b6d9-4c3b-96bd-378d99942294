// This file is WIP and may not be compatible with nui until it's finished
:where(:root[data-uinew]) :host {
    opacity: 0;
    transition: 0.2s ease-in-out opacity;

    --toggle-animations-icon-width: 0;
    --feeds-icon-width: 0;
    --actions-icon-width: 0;
    --left-panel-bg: var(--nui-surface-neutral-subtlest);

    display: block;
    min-height: var(--timeline-row-height);
    position: relative;
    mix-blend-mode: multiply;

    ui-input {
        max-width: 100%;
    }

    &.group-node {
        .left-panel {
            grid-template-columns:
                var(--icon-width)
                minmax(0, 1fr)
                var(--kebab-menu-width);
        }

        .canvas-wrapper {
            height: 100%;
            overflow: hidden;
        }

        .element-canvas {
            height: 100%;
        }
    }

    &.expanded {
        &:not(.selected, .in-group) {
            background-color: transparent;
        }
        &.selected.recording ui-input {
            --input-color: var(--nui-border-system-danger-boldest);
        }
        &:has(.animations-expanded) {
            .canvas-wrapper {
                height: 100%;
                overflow: hidden;
            }

            .element-canvas {
                height: 100%;
            }
            .left-panel {
                border-bottom: 1px solid var(--nui-border-neutral-secondary-bold);
            }
        }
    }

    &:not(.group-node) {
        overflow: hidden;
    }

    &:not(.scrolling):not(.transforming) {
        &.hover:not(.selected) {
            background-color: var(--nui-fill-brand-secondary-subtler);

            .left-panel {
                --left-panel-bg: var(--nui-fill-brand-secondary-subtle);
            }
            timeline-animation {
                --timeline-animation-left-panel-bg: var(--nui-fill-brand-secondary-subtle);
            }
        }
        &.selected {
            background-color: var(--nui-fill-brand-primary-subtlest);
            --left-panel-bg: var(--nui-fill-brand-primary-subtlest);
            timeline-animation:not(:hover) {
                --timeline-animation-left-panel-bg: var(--nui-fill-brand-primary-subtlest);
                --timeline-animation-backdrop-bg: var(--nui-fill-brand-secondary-subtlest);
            }
        }
    }

    &:hover,
    &.selected,
    &.expanded {
        .icon:not(ui-svg-icon) {
            opacity: 1;
        }

        .element-features .icon {
            &.actions,
            &.feeds {
                ui-svg-icon {
                    --color: var(--nui-text-disabled);
                }
            }
        }
    }

    &.selecting .left-panel {
        pointer-events: none;
    }

    &.hidden .eye {
        opacity: 1;
    }

    .input {
        margin-right: 1px;
    }

    .summary {
        display: grid;
        height: var(--timeline-row-height);
        grid-template-columns: var(--left-panel-width) auto;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .left-panel {
        display: grid;
        grid-template-columns:
            var(--icon-width)
            minmax(0, 1fr)
            var(--toggle-animations-icon-width) var(--kebab-menu-width);

        height: 100%;
        position: relative;
        padding: 0 var(--left-panel-padding);
        background-color: var(--left-panel-bg);

        &.hidden {
            .icon.toggle-group,
            .masking-icon,
            .element-features {
                opacity: 0.4;
            }

            ui-input {
                opacity: 0.4;

                &:focus {
                    opacity: 1;
                }
            }
        }

        &.keyframes-enabled {
            --toggle-animations-icon-width: var(--icon-width);
        }

        .icon.menu ui-svg-icon,
        .icon.lock ui-svg-icon,
        .icon.toggle-animations:not(.active) ui-svg-icon,
        .icon.eye ui-svg-icon {
            --color: var(--nui-button-icon-secondary);
            &:hover {
                --color: var(--nui-button-icon-primary-inverted);
            }
        }
        .icon.toggle-animations {
            position: relative;
            overflow: visible;
        }
        .icon.toggle-animations.active::after {
            content: '';
            position: absolute;
            left: 6px;
            bottom: -3px;
            height: 6px;
            width: 6px;
            border-top: 1px solid var(--nui-border-neutral-secondary-bold);
            border-left: 1px solid var(--nui-border-neutral-secondary-bold);
            transform: rotate(45deg);
            background-color: var(--left-panel-bg);
            pointer-events: none;
        }
    }

    .right-canvas {
        z-index: 1;
        margin-left: calc(var(--canvas-padding) * (-1));
        position: relative;
        width: calc(100% + var(--canvas-padding));
        height: var(--timeline-row-height);
    }

    .name {
        display: flex;
        align-items: center;
        padding-left: var(--nui-space-100);

        .element-features {
            margin-left: 4px;
            display: flex;

            .icon {
                pointer-events: none;
                margin-right: 4px;

                &:last-child {
                    margin-right: 0;
                }

                ui-svg-icon {
                    --color: var(--nui-icon-disabled);
                }
            }
        }
    }

    .icon:not(ui-svg-icon) {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        opacity: 0;
        transition:
            color 0.2s ease,
            opacity 0.2s ease;
        color: var(--icon-color);
        overflow: hidden;

        &.lock,
        &.toggle-group,
        &.masking {
            opacity: 1;
            margin-right: 2px;
        }

        &.eye {
            padding-left: var(--name-input-padding);
        }

        &.feature {
            opacity: 1;
        }

        &.hidden {
            opacity: 0 !important;
            pointer-events: none;
        }

        &.masking {
            overflow: visible;
            position: absolute;
        }
    }

    .track {
        position: relative;
        overflow: hidden;
    }

    .animations-expanded {
        display: block;
        z-index: 11;
    }

    .element-canvas {
        pointer-events: none;
        width: 100%;
        height: var(--timeline-row-height);
    }

    .toggle-group {
        flex: 0;
        flex-basis: var(--icon-width);
    }

    .masking {
        height: 100%;

        .masking-path {
            position: absolute;
            left: 0;
            top: 0;

            &.start {
                transform: translate(-13px, 17px);
            }
            &.line {
                transform: translate(-11px, 0px);
            }
            &.end {
                transform: translate(-15px, 0px);
            }
        }
    }
}
