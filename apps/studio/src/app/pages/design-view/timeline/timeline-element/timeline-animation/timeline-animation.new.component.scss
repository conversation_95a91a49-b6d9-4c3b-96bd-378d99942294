$animationBackdropPadding: 6px;

:where(:root[data-uinew]) :host {
    display: grid;
    height: var(--timeline-row-height);
    grid-template-columns: var(--left-panel-width) auto;
    position: relative;
    --timeline-animation-left-panel-bg: var(--nui-surface-neutral-subtlest);
    --timeline-animation-backdrop-bg: var(--nui-surface-neutral-subtlest);
    background-color: transparent;

    &.hidden {
        .name {
            color: var(--nui-text-disabled);
        }

        .eye {
            opacity: 1;
            color: var(--nui-button-icon-secondary);
        }
    }

    &.selected,
    &.hover,
    &:hover {
        --timeline-animation-left-panel-bg: var(--nui-fill-brand-secondary-subtle);
        --timeline-animation-backdrop-bg: var(--nui-fill-brand-secondary-subtle);
        .icon {
            opacity: 1;
        }
    }

    &:not(.scrolling):not(.transforming) {
        &:hover,
        &.hover {
            --timeline-animation-left-panel-bg: var(--nui-fill-brand-secondary-subtle);
            --timeline-animation-backdrop-bg: var(--nui-fill-brand-secondary-subtle);

            .menu {
                display: flex;
            }
        }
    }

    &.selecting .wrapper {
        pointer-events: none;
    }

    .left-panel {
        display: grid;
        grid-template-columns: var(--icon-width) minmax(0, 1fr) var(--kebab-menu-width);
        height: var(--timeline-row-height);
        position: relative;
        padding: 0 var(--left-panel-padding);
        background-color: var(--timeline-animation-left-panel-bg);
        z-index: 2;
    }

    .right-canvas {
        z-index: 1;
        margin-left: calc(var(--canvas-padding) * (-1));
        position: relative;
        width: calc(100% + var(--canvas-padding));
        height: var(--timeline-row-height);
        position: relative;

        // animation backdrop
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: calc(var(--animation-left) - $animationBackdropPadding);
            width: calc(
                var(--animation-width) + (var(--canvas-padding) + $animationBackdropPadding) * 2
            );
            height: 100%;
            pointer-events: none;
            border: 1px solid var(--nui-border-neutral-secondary-bold);
            background-color: var(--timeline-animation-backdrop-bg);
            z-index: -1;
            border-top-width: 0;
            border-bottom-width: 0;
        }
    }

    &.last {
        .left-panel {
            border-bottom: 1px solid var(--nui-border-neutral-secondary-bold);
        }
        .right-canvas::after {
            height: calc(var(--timeline-row-height) - 6px);
            border-top-width: 0;
            border-bottom-width: 1px;
            border-bottom-left-radius: var(--nui-border-radius-medium);
            border-bottom-right-radius: var(--nui-border-radius-medium);
        }
    }

    &.first {
        .right-canvas::before {
            content: '';
            position: absolute;
            left: calc(
                var(--animation-left) +
                    (var(--animation-width) + (var(--canvas-padding) + $animationBackdropPadding) * 2) /
                    2 - 3px
            );
            top: 2px;
            height: 6px;
            width: 6px;
            border-top: 1px solid var(--nui-border-neutral-secondary-bold);
            border-left: 1px solid var(--nui-border-neutral-secondary-bold);
            transform: rotate(45deg);
            background-color: var(--timeline-animation-backdrop-bg);
            pointer-events: none;
        }
        .right-canvas::after {
            bottom: 0;
            top: auto;
            height: calc(var(--timeline-row-height) - 6px);
            border-top-width: 1px;
            border-bottom-width: 0;
            border-top-left-radius: var(--nui-border-radius-medium);
            border-top-right-radius: var(--nui-border-radius-medium);
        }
    }

    .name {
        display: flex;
        align-items: center;
        margin-left: var(--nui-space-100);

        &.active.recording ui-input {
            --input-color: var(--nui-text-error);
        }
    }

    .icon:not(ui-svg-icon) {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition:
            color 0.2s ease,
            opacity 0.2s ease;
        opacity: 0;

        &.eye {
            cursor: pointer;

            &.visibility-hidden {
                opacity: 1;

                .icon {
                    opacity: 1;
                }
            }
        }

        &.menu,
        &.eye,
        &.toggle-animations {
            --color: var(--nui-button-icon-secondary);
            &.active,
            &:hover {
                --color: var(--nui-button-icon-primary-inverted);
            }
        }
    }

    .track {
        position: relative;
        overflow: hidden;
    }

    .animations-wrapper {
        grid-column: 1 / 2;
    }

    .animation-canvas {
        pointer-events: none;
        width: 100%;
        height: var(--timeline-row-height);
    }
}
