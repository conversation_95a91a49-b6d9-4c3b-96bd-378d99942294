<div
    class="control"
    [class.disabled]="disabled">
    @if (isNewUI()) {
        <ui-button
            *media="Breakpoint.DesktopUp"
            type="ghost-secondary"
            id="animation-controls-replay"
            class="icon restart"
            (click)="stepToStart()"
            uiTooltip="Jump to start"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            nuiSvgIcon="replay"></ui-button>
        <ui-button
            *media="Breakpoint.DesktopUp"
            type="ghost-secondary"
            id="animation-controls-step-back"
            class="icon step-left"
            (click)="stepBack()"
            uiTooltip="Step -1 second"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            nuiSvgIcon="skip_previous"></ui-button>
        <ui-button
            id="animation-controls-play-pause"
            type="ghost-secondary"
            class="icon play"
            (click)="togglePlay()"
            uiTooltip="Play / pause (space)"
            [class.inactive]="isInactive$ | async"
            [uiTooltipDisabled]="disabled || isMobileShowcase"
            [uiTooltipHideArrow]="true"
            [nuiSvgIcon]="(isPlaying$ | async) ? 'pause' : 'play_arrow'"></ui-button>
        <ui-button
            *media="Breakpoint.DesktopUp"
            type="ghost-secondary"
            id="animation-controls-step-forward"
            class="icon step-forward"
            (click)="stepForward()"
            uiTooltip="Step +1 second"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            nuiSvgIcon="skip_next"></ui-button>
        <ui-button
            id="animation-controls-view-preload-image"
            type="ghost-secondary"
            class="icon preload"
            (click)="stepToPreloadImage()"
            [class.active]="preloadActive$ | async"
            [uiTooltipHideArrow]="true"
            uiTooltip="View preload image"
            [uiTooltipDisabled]="disabled || isMobileShowcase"
            nuiSvgIcon="image"></ui-button>
    } @else {
        <ui-svg-icon
            *media="Breakpoint.DesktopUp"
            id="animation-controls-replay"
            class="icon restart"
            (click)="stepToStart()"
            uiTooltip="Jump to start"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            icon="playback-start-small"
            nuiIcon="replay"></ui-svg-icon>
        <ui-svg-icon
            *media="Breakpoint.DesktopUp"
            id="animation-controls-step-back"
            class="icon step-left"
            (click)="stepBack()"
            uiTooltip="Step -1 second"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            icon="playback-step-left-small"
            nuiIcon="skip_previous"></ui-svg-icon>
        <ui-svg-icon
            id="animation-controls-play-pause"
            class="icon play"
            (click)="togglePlay()"
            uiTooltip="Play / pause (space)"
            [class.inactive]="isInactive$ | async"
            [uiTooltipDisabled]="disabled || isMobileShowcase"
            [uiTooltipHideArrow]="true"
            [icon]="(isPlaying$ | async) ? 'playback-pause' : 'playback-play'"
            [nuiIcon]="(isPlaying$ | async) ? 'pause' : 'play_arrow'"></ui-svg-icon>
        <ui-svg-icon
            *media="Breakpoint.DesktopUp"
            id="animation-controls-step-forward"
            class="icon step-forward"
            (click)="stepForward()"
            uiTooltip="Step +1 second"
            [uiTooltipHideArrow]="true"
            [uiTooltipDisabled]="disabled"
            icon="playback-step-right-small"
            nuiIcon="skip_next"></ui-svg-icon>
        <ui-svg-icon
            id="animation-controls-view-preload-image"
            class="icon preload"
            (click)="stepToPreloadImage()"
            [class.active]="preloadActive$ | async"
            [uiTooltipHideArrow]="true"
            uiTooltip="View preload image"
            [uiTooltipDisabled]="disabled || isMobileShowcase"
            icon="image"
            nuiIcon="image"></ui-svg-icon>
    }
</div>
