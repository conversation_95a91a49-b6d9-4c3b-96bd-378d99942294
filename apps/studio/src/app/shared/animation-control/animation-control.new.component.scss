@use 'mixins' as *;

:where(:root[data-uinew]) :host {
    .control {
        display: grid;
        grid-template-columns: repeat(5, auto);
        align-items: center;
        justify-items: center;

        @include media-breakpoint-down(desktop) {
            display: flex;
            justify-content: flex-start;
            height: 100%;
            gap: var(--nui-space-200);
        }

        &.disabled {
            color: var(--nui-button-icon-disabled);
        }

        .icon {
            cursor: pointer;
            color: var(--nui-button-icon-primary-inverted);

            &:hover {
                color: var(--nui-button-icon-brand);
            }

            &.play {
                color: var(--nui-button-icon-brand);

                &.inactive {
                    color: var(--nui-button-icon-primary-inverted);
                    &:hover {
                        color: var(--nui-button-icon-brand);
                    }
                }
            }

            &.active {
                color: var(--nui-button-icon-brand);
            }
        }
    }
}
