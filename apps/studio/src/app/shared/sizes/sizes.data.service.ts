import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { CREATE_SIZES, UPDATE_NAME, DELETE_SIZES } from '@data/graphql';
import { DuplicateSizesRequest, DuplicateSizesResponse } from '@domain/api/generated/sapi';
import { ICreativeset } from '@domain/creativeset/creativeset';
import { UpdateSizeResponse, CreativeSize, ZNewCreativeSize } from '@domain/creativeset/size';
import { FeatureService } from '@studio/common/feature/feature.service';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { Apollo } from 'apollo-angular';
import { map, Observable, Subject, switchMap, tap } from 'rxjs';
import {
    CreateSizesDto,
    DeleteSizesDto,
    UpdateSizeDto,
    DuplicateSizesDto
} from '@domain/api/generated/design-api';
import { handleError } from '@studio/utils/errors/errors';

@Injectable({ providedIn: 'root' })
export class SizesService {
    private apollo = inject(Apollo);
    private creativesetDataService = inject(CreativesetDataService);
    private featureService = inject(FeatureService);
    private httpClient = inject(HttpClient);

    private _update$ = new Subject<UpdateSizeResponse>();
    update$ = this._update$.asObservable();

    private readonly sapiOrigin = inject(EnvironmentService).origins.sapi;
    private readonly dapiOrigin = inject(EnvironmentService).origins.designApi;

    createSizes(sizes: CreativeSize[], creativeSetId: string): Observable<ICreativeset> {
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const url = `${this.dapiOrigin}/creative-sets/${creativeSetId}/sizes`;

            const payload: CreateSizesDto = {
                sizes: sizes.map(size => ZNewCreativeSize.parse(size))
            };

            return this.httpClient
                .post<never>(url, payload)
                .pipe(switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)));
        } else {
            return this.apollo
                .mutate({
                    mutation: CREATE_SIZES,
                    variables: {
                        sizes: sizes.map(size => ZNewCreativeSize.parse(size)),
                        creativesetId: creativeSetId
                    }
                })
                .pipe(switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)));
        }
    }

    duplicateSizes(creativeSetId: string, sizes: CreativeSize[]): Observable<ICreativeset> {
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const url = `${this.dapiOrigin}/creative-sets/${creativeSetId}/sizes/duplicate`;

            const payload: DuplicateSizesDto = {
                ids: sizes.map(({ id }) => +id)
            };

            return this.httpClient
                .post<never>(url, payload)
                .pipe(switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)));
        } else {
            const url = `${this.sapiOrigin}/sizes/duplicate`;

            const payload: DuplicateSizesRequest = {
                sizeIds: sizes.map(({ id }) => +id)
            };

            return this.httpClient
                .post<DuplicateSizesResponse>(url, payload)
                .pipe(switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)));
        }
    }

    updateSizeName(
        sizeId: string,
        name: string,
        creativesetId: string
    ): Observable<UpdateSizeResponse> {
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const url = `${this.dapiOrigin}/creative-sets/${creativesetId}/sizes/${sizeId}`;

            const payload: UpdateSizeDto = {
                name
            };

            return this.httpClient.put<never>(url, payload).pipe(
                map(() => ({ id: sizeId, name })),
                tap(size => this._update$.next(size))
            );
        } else {
            return this.apollo
                .mutate({
                    mutation: UPDATE_NAME,
                    variables: {
                        sizeId,
                        name,
                        creativesetId
                    }
                })
                .pipe(
                    map(() => ({ id: sizeId, name })),
                    tap(size => this._update$.next(size))
                );
        }
    }

    deleteSizesInCreativeset(creativeSetId: string, sizes: CreativeSize[]): Observable<ICreativeset> {
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const url = `${this.dapiOrigin}/creative-sets/${creativeSetId}/sizes`;

            const payload: DeleteSizesDto = {
                ids: sizes.map(({ id }) => +id)
            };

            return this.httpClient
                .delete<never>(url, { body: payload })
                .pipe(switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)));
        } else {
            return this.apollo
                .mutate({
                    mutation: DELETE_SIZES,
                    variables: {
                        creativesetId: creativeSetId,
                        ids: sizes.map(s => s.id)
                    }
                })
                .pipe(
                    map(({ data, errors }) => {
                        if (!data || errors) {
                            handleError('Could not delete sizes in creativeset', {
                                contexts: { originalError: errors }
                            });
                        }
                    }),
                    switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId))
                );
        }
    }
}
