@use 'mixins' as *;
:where(:root[data-uinew]) :host {
    --topbar-height: 50px;

    .items {
        position: relative;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;

        gap: var(--nui-space-200);
        margin: var(--nui-space-200) 0;

        @include media-breakpoint-down(desktop) {
            grid-template-columns: 1fr 1fr;
            column-gap: var(--nui-space-200);
            // gap: var(--nui-space-200);
        }

        @include media-breakpoint-down(tablet) {
            grid-template-columns: 1fr;
            // gap: var(--nui-space-200);
            padding: 0 var(--nui-space-200);
        }
    }

    .creative-list-item {
        &:hover {
            .hover-icon,
            .status-icon_no-status {
                visibility: visible;
            }
        }
    }

    .virtual-scroll-viewport {
        height: calc(100vh - var(--topbar-height));
        display: block;
    }

    .no-creatives {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
}
