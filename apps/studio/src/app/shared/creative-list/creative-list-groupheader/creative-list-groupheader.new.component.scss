@use 'mixins' as *;

:where(:root[data-uinew]) :host {
    display: flex;
    padding: var(--nui-space-100) var(--nui-space-100) var(--nui-space-100) var(--nui-space-300);
    margin-top: var(--nui-space-200);
    border-radius: var(--nui-border-radius-medium);
    border: 1px solid var(--nui-border-neutral-subtle);
    background: var(--nui-fill-brand-secondary-subtlest);

    @include media-breakpoint-down(desktop) {
        height: 0rem;
    }

    &:hover {
        border: 1px solid var(--nui-border-neutral-secondary-boldest);
        background: var(--nui-fill-neutral-subtlest);
    }

    &.selected {
        border: 1px solid var(--nui-border-brand-primary-boldest);
        background: var(--nui-fill-brand-secondary-subtlest);
    }

    .size-header {
        display: grid;
        grid-template-columns: 1fr 33% 1fr;
        width: 100%;
        &:hover {
            .hover-icon {
                visibility: visible;
            }
        }
    }

    .left {
        display: flex;
        align-items: center;
    }

    .center {
        display: flex;
        justify-content: center;
        gap: var(--nui-space-200);

        font-size: var(--nui-body-bold-font-size);
        font-weight: var(--nui-body-bold-font-weight);
        line-height: var(--nui-body-bold-line-height);
        letter-spacing: var(--nui-body-bold-letter-spacing);

        .creative-size-fake-input {
            display: flex;
            align-items: center;
            gap: var(--nui-space-100, 4px);

            &.editing {
                .hover-icon,
                .status-icon {
                    display: none;
                }
            }
            .edit-loader {
                position: relative;
            }
        }

        .size-name {
            color: var(--nui-text-secondary);
            font-weight: var(--nui-body-regular-font-weight);
        }

        .size-name-calc {
            display: inline-block;
            height: 0;
            overflow: hidden;
            position: absolute;
            top: 0;
            left: 0;
            font-size: 11px;
        }

        .hover-icon {
            visibility: hidden;
        }
    }

    .right {
        .actions {
            display: flex;
            justify-content: end;
            height: 100%;

            .group-dropdown,
            .collapse-icon {
                display: flex;
                justify-content: center;
                align-items: center;
                width: var(--nui-button-height);
                height: var(--nui-button-height);
                cursor: pointer;
            }
        }
    }
}
