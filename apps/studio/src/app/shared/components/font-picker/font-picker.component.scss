:where(:root:not([data-uinew])) :host {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    &.labelVisible {
        .select {
            grid-template-columns: 4.8rem auto;
        }
    }

    ui-inline-loader {
        margin: auto;
    }

    .select {
        display: grid;
        min-width: 100%;
        max-width: 100%;
        grid-gap: 1rem;
        margin-bottom: 1rem;

        &:last-child {
            margin-bottom: 0;
        }

        &-label {
            min-width: 6rem;
            max-width: 6rem;
            margin-top: 4px;
            align-items: center;
            white-space: nowrap;
            overflow-x: hidden;
            text-overflow: ellipsis;
        }

        .ui-select {
            width: 100%;
            flex: none;
            max-width: 14.2rem;
            min-width: 14.2rem;

            &.deleted {
                opacity: 0.6;
            }

            ::ng-deep {
                .button {
                    min-width: 0;
                }
            }
        }
    }
}
