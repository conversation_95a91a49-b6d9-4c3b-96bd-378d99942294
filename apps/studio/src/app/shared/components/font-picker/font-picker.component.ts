import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    DestroyRef,
    EventEmitter,
    HostBinding,
    inject,
    input,
    Input,
    OnChanges,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
    UIDialogService,
    UIInlineLoaderComponent,
    UILabelComponent,
    UINewThemeService,
    UIOptionComponent,
    UISelectComponent,
    UITooltipDirective
} from '@bannerflow/ui';
import { removeDeletedFonts, tryGetFontByStyleId } from '@creative/font-families.utils';
import { ISelectedFont } from '@domain/font';
import { IFontFamily, IFontFamilyStyle } from '@domain/font-families';
import { CreativesetDataService, FontFamiliesService } from '@studio/common';
import { FONT_PLACEHOLDERS } from '@studio/domain/components/font-picker';
import { EventLoggerService, FontNotFoundError } from '@studio/monitoring/events';
import { FontLoader } from '@studio/utils/font-loading';
import { map } from 'rxjs/operators';
import { SortArrayPipe } from '../../pipes/sort-array.pipe';
import { FontManagerComponent } from '../fonts/font-manager.component';
import { ToolbarButtonComponent } from '../toolbar-button/toolbar-button.component';

const MIXED_FONT = '$mixed';

@Component({
    imports: [
        SortArrayPipe,
        ToolbarButtonComponent,
        UIInlineLoaderComponent,
        UIOptionComponent,
        UISelectComponent,
        UITooltipDirective,
        UILabelComponent
    ],
    selector: 'font-picker',
    templateUrl: 'font-picker.component.html',
    styleUrls: ['font-picker.component.scss', 'font-picker.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'font-picker'
    }
})
export class FontPickerComponent implements OnInit, OnChanges {
    private changeDetector = inject(ChangeDetectorRef);
    private creativesetDataService = inject(CreativesetDataService);
    private destroyRef = inject(DestroyRef);
    private eventLoggerService = inject(EventLoggerService);
    private fontFamiliesService = inject(FontFamiliesService);
    private uiDialogService = inject(UIDialogService);
    private uiNewThemeService = inject(UINewThemeService);

    @ViewChild('fontFamilySelect', { static: true }) fontFamilySelect: UISelectComponent;
    @ViewChild('fontStyleSelect', { static: true }) fontStyleSelect: UISelectComponent;
    @Input() selectedFontFamilyId: string | undefined;
    @Input() selectedFontStyleId: string | undefined;
    @Input() labels = true;
    @HostBinding('class.labelVisible') labelVisible = this.labels;
    @Input() showAsPlaceholder = false;
    @Input() disabled = false;
    searchable = input(true);

    @Output() mousedown = new EventEmitter<MouseEvent>();
    @Output() onOpen = new EventEmitter<void>();
    @Output() selectedFontChange = new EventEmitter<ISelectedFont>();
    @Output() previewFontChange = new EventEmitter<Partial<ISelectedFont>>();
    @Output() onPreviewStop = new EventEmitter();

    selectedFontFamily?: IFontFamily;
    selectedFontStyle?: IFontFamilyStyle;
    filteredFontFamilies: IFontFamily[];
    fontFamilyPlaceholder: string;
    fontStylePlaceholder: string;

    isFontManagerDown = this.fontFamiliesService.isFontManagerDown;

    isLoadingFontStyle = computed(() => this.computeIsLoadingFont());

    private failedToLoadFontStyleIds = toSignal(this.fontFamiliesService.failedToLoadFontStyleIds, {
        initialValue: []
    });
    private fontFamilies = toSignal(this.fontFamiliesService.fontFamilies$, { initialValue: [] });
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    ngOnInit(): void {
        this.fontFamiliesService.fontFamilies$
            .pipe(
                map(fontFamilies => removeDeletedFonts(fontFamilies)),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(fontFamilies => {
                this.setFontFamilies(fontFamilies);
            });
    }

    ngOnChanges(): void {
        if (!this.filteredFontFamilies) {
            return;
        }

        this.updateSelectedFont();

        this.changeDetector.detectChanges();
    }

    private computeIsLoadingFont(): boolean {
        if (this.selectedFontFamilyId === MIXED_FONT || this.selectedFontStyleId === MIXED_FONT) {
            return false;
        }
        const fontFamilies = this.fontFamilies();
        if (!this.selectedFontFamilyId || !this.selectedFontStyleId) {
            return true;
        }
        const failedToLoadFontStyleIds = this.failedToLoadFontStyleIds();
        if (failedToLoadFontStyleIds.includes(this.selectedFontStyleId)) {
            return false;
        }
        const isFontStyleLoaded = !!tryGetFontByStyleId(fontFamilies, this.selectedFontStyleId);
        return !isFontStyleLoaded;
    }

    private setFontFamilies(fontFamilies: IFontFamily[]): void {
        const currentBrandId = this.creativesetDataService.brand.id;
        this.filteredFontFamilies = fontFamilies.filter(
            fontFamily =>
                !fontFamily.visibleBrandIds ||
                fontFamily.visibleBrandIds.find(brandId => currentBrandId === brandId)
        );
        this.updateSelectedFont();
        this.changeDetector.detectChanges();
    }

    private getFontStyle(
        fontFamilies: IFontFamily[],
        fontFamilyId: string,
        fontStyleId: string
    ): IFontFamilyStyle | undefined {
        if (!fontFamilies.length || !fontFamilyId) {
            return;
        }
        const fontFamily = fontFamilies.find(({ id }) => fontFamilyId === id);
        return fontFamily && fontFamily.fontStyles.find(({ id }) => id === fontStyleId);
    }

    private updateSelectedFont(): void {
        this.selectedFontFamily = this.selectedFontStyle = undefined;

        // Mixed font
        if (this.selectedFontFamilyId === MIXED_FONT) {
            this.fontFamilyPlaceholder = FONT_PLACEHOLDERS.MIXED;
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.MIXED;
            return;
        }
        // No font at all
        if (!this.selectedFontFamilyId) {
            this.fontFamilyPlaceholder = FONT_PLACEHOLDERS.NO_FONT_SELECTED;
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.NO_STYLE_SELECTED;
            return;
        }

        const fontFamilies = this.fontFamilies();
        if (!fontFamilies.length) {
            this.fontFamilyPlaceholder = FONT_PLACEHOLDERS.NO_FONTS;
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.NO_STYLES;
            return;
        }

        let fontFamily = fontFamilies.find(({ id }) => id === this.selectedFontFamilyId);
        if (!fontFamily && this.selectedFontStyleId) {
            fontFamily = tryGetFontByStyleId(fontFamilies, this.selectedFontStyleId);
            if (fontFamily) {
                this.selectedFontFamilyId = fontFamily?.id;
            }
        }

        if (!fontFamily) {
            this.fontFamilyPlaceholder = FONT_PLACEHOLDERS.NO_FONT_FOUND;
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.NO_STYLE_FOUND;

            // Load font by fontStyleId if it hasn't attempted to
            const failedToLoadFontStyleIds = this.failedToLoadFontStyleIds();
            if (
                this.selectedFontStyleId &&
                !failedToLoadFontStyleIds.includes(this.selectedFontStyleId)
            ) {
                this.fontFamiliesService.loadFontByStyleId(this.selectedFontStyleId);
            } else {
                this.eventLoggerService.log(
                    new FontNotFoundError({ fontFamilyId: this.selectedFontStyleId })
                );
            }
            return;
        }

        this.fontFamilyPlaceholder = `[${fontFamily.name}]`;
        if (!fontFamily.deletedAt) {
            this.selectedFontFamily = this.filteredFontFamilies.find(
                ({ id }) => id === this.selectedFontFamilyId
            );
        }

        if (this.selectedFontStyleId === MIXED_FONT) {
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.MIXED;
            return;
        }

        if (!this.selectedFontStyleId) {
            this.fontStylePlaceholder = FONT_PLACEHOLDERS.NO_STYLE_SELECTED;
            return;
        }

        const fontStyle = this.getFontStyle(
            this.filteredFontFamilies,
            this.selectedFontFamilyId,
            this.selectedFontStyleId
        );
        if (fontStyle) {
            this.selectedFontStyle = fontStyle;
            this.fontStylePlaceholder = `[${fontStyle.name}]`;
            return;
        }

        const deletedFontStyle = this.getFontStyle(
            fontFamilies,
            this.selectedFontFamilyId,
            this.selectedFontStyleId
        );
        if (deletedFontStyle) {
            this.fontStylePlaceholder = `[${deletedFontStyle.name}]`;
            return;
        }

        this.fontStylePlaceholder = FONT_PLACEHOLDERS.NO_STYLE_FOUND;
        this.eventLoggerService.log(new FontNotFoundError({ fontStyleId: this.selectedFontStyleId }));
    }

    selectOpen(): void {
        this.onOpen.next();
    }

    onFontFamilyChanged(fontFamily: IFontFamily): void {
        this.selectedFontFamily = fontFamily;
        this.selectedFontStyle = this.getClosestFontStyle(
            fontFamily.id,
            this.selectedFontStyle ? this.selectedFontStyle.id : ''
        );
        this.selectedFontChange.emit({
            fontFamily: this.selectedFontFamily,
            fontStyle: this.selectedFontStyle
        });
    }

    onFontStyleChanged(fontStyle: IFontFamilyStyle): void {
        this.selectedFontChange.emit({ fontFamily: this.selectedFontFamily!, fontStyle: fontStyle });
    }

    getClosestFontStyle(fontFamilyId: string, fontStyleId: string): IFontFamilyStyle {
        const selectedFontFamily = this.filteredFontFamilies.find(family => family.id === fontFamilyId);
        let closestFontStyle: IFontFamilyStyle | undefined;

        if (selectedFontFamily) {
            for (const fontFamily of this.filteredFontFamilies) {
                const currentFontStyle = fontFamily.fontStyles.find(
                    fontStyle => fontStyle.id === fontStyleId
                );

                if (currentFontStyle) {
                    // Check if the new font family has a similar style
                    let newFontStyle = selectedFontFamily.fontStyles.find(
                        fontStyle =>
                            fontStyle.italic === currentFontStyle.italic &&
                            fontStyle.weight === currentFontStyle.weight
                    );

                    if (newFontStyle) {
                        closestFontStyle = newFontStyle;
                        break;
                    }
                    // If it doesn't match italic and weight, look for just the weight instead
                    else {
                        newFontStyle = selectedFontFamily.fontStyles.find(
                            fontStyle => fontStyle.weight === currentFontStyle.weight
                        );
                        closestFontStyle = newFontStyle;
                    }
                }
            }
        }

        if (!closestFontStyle) {
            closestFontStyle =
                selectedFontFamily!.fontStyles.find(fontStyle => fontStyle.weight === 400) ||
                selectedFontFamily!.fontStyles[0];
        }

        return closestFontStyle;
    }

    async onPreviewFontFamily(fontFamily: IFontFamily): Promise<void> {
        const fontStyle = this.getClosestFontStyle(
            fontFamily.id,
            (this.selectedFontStyle && this.selectedFontStyle.id) || this.selectedFontStyleId!
        );
        await FontLoader.loadFontFace(fontStyle);
        this.previewFontChange.emit({ fontFamily, fontStyle });
    }

    async onPreviewFontStyle(fontStyle: IFontFamilyStyle): Promise<void> {
        await FontLoader.loadFontFace(fontStyle);
        this.previewFontChange.emit({ fontFamily: this.selectedFontFamily!, fontStyle });
    }

    stopCurrentPreview(): void {
        this.onPreviewStop.emit();
    }

    fontManagerClosed = (): void => {
        this.fontFamiliesService.loadFontFamilies();
        this.creativesetDataService.syncFonts();
    };

    openFontManager(): void {
        const dialog = this.uiDialogService.openComponent(FontManagerComponent, {
            padding: 0,
            headerText: 'Manage brand fonts',
            width: '100%',
            maxWidth: '100%',
            height: '100%',
            panelClass: ['inlined-iframe', 'fullscreen'],
            data: {
                brandId: this.creativesetDataService.brand.id
            }
        });
        dialog.beforeClose().subscribe(() => this.fontManagerClosed());
    }
}
