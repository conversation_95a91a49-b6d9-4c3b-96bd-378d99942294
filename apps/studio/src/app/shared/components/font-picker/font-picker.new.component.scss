:where(:root[data-uinew]) :host {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    &.labelVisible {
        ui-select {
            width: 12rem;
        }
    }

    ui-inline-loader {
        margin: auto;
    }

    .select {
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin-bottom: var(--nui-space-200);

        &:last-child {
            margin-bottom: 0;
        }

        .ui-select.deleted {
            opacity: 0.6;
        }
    }
}
