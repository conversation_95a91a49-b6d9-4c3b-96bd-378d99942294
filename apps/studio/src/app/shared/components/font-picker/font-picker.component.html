@if (isLoadingFontStyle()) {
    <ui-inline-loader [uiTooltip]="'Loading font information'" />
} @else {
    <div
        class="select"
        [uiTooltipDisabled]="!isFontManagerDown()"
        [uiTooltip]="'We could not fetch the list of fonts at this moment'">
        @if (labels) {
            @if (isNewUI()) {
                <ui-label
                    size="sm"
                    type="secondary"
                    >Font</ui-label
                >
            } @else {
                <div class="select-label">Font</div>
            }
        }
        <ui-select
            size="xs"
            type="secondary"
            #fontFamilySelect
            id="font-family"
            [searchable]="searchable()"
            [useTargetWidth]="true"
            [uiTooltip]="selectedFontFamily?.name || ''"
            [uiTooltipDisabled]="!selectedFontFamily?.name"
            [selected]="selectedFontFamilyId === '$mixed' ? 'Mixed' : selectedFontFamily"
            (selectedChange)="onFontFamilyChanged($event)"
            (previewChange)="onPreviewFontFamily($event)"
            (previewStop)="stopCurrentPreview()"
            [placeholder]="fontFamilyPlaceholder"
            [backdropCss]="'prevent-text-blur'"
            [disabled]="disabled || filteredFontFamilies.length === 0 || isFontManagerDown()"
            (onOpen)="selectOpen()">
            <div (mouseleave)="stopCurrentPreview()">
                @if (selectedFontFamilyId === '$mixed') {
                    <ui-option
                        [value]="'Mixed'"
                        [selected]="true">
                        Mixed
                    </ui-option>
                    <div class="option-divider"></div>
                }
                @for (fontFamily of filteredFontFamilies || []; track fontFamily.id) {
                    <ui-option
                        (mousedown)="mousedown.emit($event)"
                        [value]="fontFamily">
                        {{ fontFamily.name }}
                    </ui-option>
                }

                <div class="buttons">
                    <toolbar-button
                        icon="settings"
                        nuiIcon="settings"
                        size="sm"
                        width="100%"
                        [iconSize]="14"
                        (click)="openFontManager()" />
                </div>
            </div>
        </ui-select>
    </div>
    <div
        class="select"
        [uiTooltipDisabled]="!isFontManagerDown()"
        [uiTooltip]="'We could not fetch the list of fonts at this moment'">
        @if (labels) {
            @if (isNewUI()) {
                <ui-label
                    size="sm"
                    type="secondary"
                    >Style</ui-label
                >
            } @else {
                <div class="select-label">Style</div>
            }
        }
        <ui-select
            size="xs"
            type="secondary"
            #fontStyleSelect
            id="font-style"
            [useTargetWidth]="true"
            [searchable]="searchable()"
            [uiTooltip]="selectedFontStyle?.name || ''"
            [uiTooltipDisabled]="!selectedFontStyle?.name"
            [placeholder]="fontStylePlaceholder"
            [selected]="selectedFontStyleId === '$mixed' ? 'Mixed' : selectedFontStyle"
            [disabled]="disabled || !filteredFontFamilies || isFontManagerDown()"
            [backdropCss]="'prevent-text-blur'"
            (selectedChange)="onFontStyleChanged($event)"
            (previewChange)="onPreviewFontStyle($event)"
            (previewStop)="stopCurrentPreview()"
            (onOpen)="selectOpen()">
            <div (mouseleave)="stopCurrentPreview()">
                @if (selectedFontStyleId === '$mixed') {
                    <ui-option
                        [value]="'Mixed'"
                        [selected]="true">
                        Mixed
                    </ui-option>
                    <div class="option-divider"></div>
                }
                @if (selectedFontFamily) {
                    @for (
                        fontStyle of selectedFontFamily.fontStyles || [] | sortArray: { weight: 'asc' };
                        track fontStyle.id
                    ) {
                        <ui-option
                            (mousedown)="mousedown.emit($event)"
                            [value]="fontStyle">
                            {{ fontStyle.name }}
                        </ui-option>
                    }
                }
                <div class="buttons">
                    <toolbar-button
                        icon="settings"
                        width="100%"
                        [iconSize]="14"
                        (click)="openFontManager()" />
                </div>
            </div>
        </ui-select>
    </div>
}
