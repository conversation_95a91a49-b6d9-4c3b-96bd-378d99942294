@use 'variables' as *;

:where(:root:not([data-uinew])) :host {
    .button {
        height: 2.2rem;
        width: 4.6rem;
        padding: 0.2rem;
        border-radius: 0.3rem;
        background: var(--studio-color-background-second);
        border: 1px solid var(--studio-color-border);
        cursor: pointer;

        &__color,
        &__background {
            width: 100%;
            height: 100%;
        }

        &__background {
            background: $chessBackgroundUrl;
        }

        .mixed {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--studio-color-grey-86);
            color: var(--studio-color-grey-semidark);

            ui-svg-icon {
                --font-size: 4rem;
            }
        }
    }
}

:where(:root[data-uinew]) :host {
    .button {
        height: 2.4rem;
        width: 4rem;
        padding: 1px;
        border-radius: var(--nui-border-radius-tiny);
        background: var(--fill-brand-secondary-subtlest);
        border: 1px solid var(--nui-border-neutral-secondary-bold);
        cursor: pointer;

        &__color,
        &__background {
            width: 100%;
            height: 100%;
        }

        &__background {
            background: $chessBackgroundUrl;
        }

        .mixed {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--studio-color-grey-86);
            color: var(--studio-color-grey-semidark);

            ui-svg-icon {
                --font-size: 4rem;
            }
        }
    }
}
