import { Component, Input, ElementRef, input } from '@angular/core';
import { Icon, UIModule, UINUIIcon } from '@bannerflow/ui';

@Component({
    imports: [UIModule],
    selector: 'toolbar-button',
    templateUrl: './toolbar-button.component.html',
    styleUrls: ['./toolbar-button.component.scss', './toolbar-button.new.component.scss']
})
export class ToolbarButtonComponent {
    @Input() icon: Icon;
    nuiIcon = input<UINUIIcon>();
    @Input() iconSize = 10;

    @Input()
    set width(val: string) {
        this.host.nativeElement.style.maxWidth = val;
    }

    constructor(private host: ElementRef) {}
}
