@if (isNewUI()) {
    <nui-version-picker [allowMultiSelect]="allowManageVersions" />
} @else {
    @let selectedVersionIds = selectedVersionIds$ | async;
    @if (selectedVersions$ | async; as selectedVersions) {
        <div
            [class.mobile-showcase]="isMobileShowcase$ | async"
            class="container">
            @if ({ viewMode: viewMode$ | async }; as data) {
                <div class="version-picker">
                    @if (data.viewMode !== 'add' && data.viewMode !== 'loading') {
                        <div
                            id="select-version"
                            class="selected-version"
                            [ngClass]="{ select: data.viewMode === 'select' }"
                            (click)="toggleSelectView()"
                            data-test-id="version-picker">
                            <div class="selected-row">
                                @if (isShowingAllVersions$ | async) {
                                    <!-- all versions -->
                                    <ui-svg-icon
                                        class="all-versions-icon"
                                        icon="thumbnails"></ui-svg-icon>
                                    <div class="version-name"><span>All versions</span></div>
                                } @else {
                                    <ng-container #notShowingAll>
                                        @switch (selectedVersions.length) {
                                            <!-- single version selected -->
                                            @case (1) {
                                                <version-flag
                                                    [localizationId]="
                                                        selectedVersions[0].localization.id
                                                    "
                                                    [title]="selectedVersions[0].name"
                                                    [showName]="true"></version-flag>
                                            }
                                            <!-- multiple version selected -->
                                            @default {
                                                <div class="flag-wrapper">
                                                    @for (
                                                        version of selectedVersions;
                                                        track version;
                                                        let i = $index
                                                    ) {
                                                        @if (3 > i) {
                                                            <version-flag
                                                                [localizationId]="
                                                                    version.localization.id
                                                                "></version-flag>
                                                        }
                                                    }
                                                </div>
                                                <span
                                                    >{{ selectedVersions.length }} of
                                                    {{ (versions$ | async)?.length }}</span
                                                >
                                            }
                                        }
                                    </ng-container>
                                }
                                <div class="arrow">
                                    @if (versionPickerArrow$ | async; as versionPickerArrow) {
                                        <ui-svg-icon [icon]="versionPickerArrow"></ui-svg-icon>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    @if (data.viewMode === 'loading') {
                        <div class="loading">
                            <ui-loader></ui-loader>
                        </div>
                    }
                    @if (data.viewMode === 'add') {
                        <div class="add-version">
                            @if (newVersionPlaceholder$ | async; as newVersion) {
                                <version-flag
                                    [localizationId]="newVersion.localization.id"></version-flag>
                                <ui-input
                                    #versionNameInput
                                    autocomplete="off"
                                    [maxlength]="250"
                                    [(value)]="newVersionName"
                                    (submit)="saveNewVersion()"
                                    (cancel)="stopAddVersion()"
                                    (blur)="saveNewVersion()"></ui-input>
                            }
                        </div>
                    }
                    @if (data.viewMode === 'select' || data.viewMode === 'select-localization') {
                        <div
                            class="version-dropdown"
                            #versionList
                            (click)="$event.stopPropagation()">
                            @if (versions$ | async; as versions) {
                                <div class="version-list">
                                    @if (versions.length > 1 && showAllVersions) {
                                        <div
                                            id="interaction-all-versions"
                                            class="version-item"
                                            (click)="selectAllVersions()"
                                            [ngClass]="{ selected: (isShowingAllVersions$ | async) }">
                                            <ui-svg-icon
                                                icon="thumbnails"
                                                class="all-versions-icon"></ui-svg-icon>
                                            <span>All versions</span>
                                        </div>
                                    }
                                    @for (version of versions; track version; let i = $index) {
                                        <div
                                            id="version-{{ i + 1 }}"
                                            class="version-item"
                                            [attr.data-test-id]="'version-' + version.name"
                                            (click)="selectVersion($event, version)"
                                            [ngClass]="{
                                                selected:
                                                    selectedVersionIds?.indexOf(version.id) !== -1 &&
                                                    (isShowingAllVersions$ | async) === false
                                            }">
                                            <version-flag
                                                [localizationId]="version.localization.id"
                                                [showName]="true"
                                                [title]="version.name"></version-flag>
                                        </div>
                                    }
                                </div>
                            }
                            <ng-container *permissions="'ManageVersions'">
                                @if (allowManageVersions) {
                                    <div
                                        class="version-toolbar"
                                        *permissions="'Default'">
                                        <toolbar-button
                                            id="interaction-add-version"
                                            icon="plus"
                                            [iconSize]="12"
                                            (click)="showAddLocalizationView($event)"
                                            [uiDropdownTarget]="localizationsDropdown.dropdown">
                                        </toolbar-button>
                                        <version-language-picker
                                            #localizationsDropdown
                                            (selectLocalization)="
                                                selectLocalization(
                                                    $event.localization,
                                                    $event.autoTranslate
                                                )
                                            "
                                            [offset]="{ x: 0, y: -5 }"
                                            [positions]="[
                                                {
                                                    originX: 'end',
                                                    originY: 'top',
                                                    overlayX: 'start',
                                                    overlayY: 'top'
                                                }
                                            ]"></version-language-picker>
                                        <toolbar-button
                                            id="interaction-version-settings"
                                            icon="settings"
                                            [iconSize]="14"
                                            (click)="openVersionDialog()">
                                        </toolbar-button>
                                    </div>
                                }
                            </ng-container>
                        </div>
                    }
                </div>
            }
        </div>
    }
}
