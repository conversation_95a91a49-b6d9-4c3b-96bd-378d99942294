import { CommonModule } from '@angular/common';
import { Component, computed, inject, input } from '@angular/core';
import { UIModule } from '@bannerflow/ui';
import { IBrandLocalization } from '@domain/brand';
import { BrandService } from '@studio/common/brand';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { map } from 'rxjs';
import { toSignal } from '@angular/core/rxjs-interop';
import { TruncateSpanComponent } from '../../../directives/truncate-span.directive';
import { VersionFromIdPipe } from '../../../pipes/version-from-id.pipe';

@Component({
    imports: [UIModule, CommonModule, VersionFromIdPipe, TruncateSpanComponent],
    selector: 'version-flag',
    templateUrl: './version-flag.component.html',
    styleUrls: ['./version-flag.component.scss', './version-flag.new.component.scss']
})
export class VersionFlagComponent {
    private readonly brandService = inject(BrandService);
    private readonly environmentService = inject(EnvironmentService);

    localizationId = input<string | undefined>();
    showName = input<boolean>(false);
    versionId = input<string | undefined>();
    showVersionName = input<boolean>(false);
    title = input<string | undefined>();
    isFullScreen = input<boolean>(false);
    sizeSelectLabel = input<'small' | 'default'>('small');

    private readonly localizations = toSignal<IBrandLocalization[] | undefined>(
        this.brandService.localizations$,
        { initialValue: undefined }
    );

    flagSize = toSignal<'tiny' | undefined>(
        this.environmentService.isMobile$.pipe(map(isMobile => (isMobile ? undefined : 'tiny'))),
        { initialValue: undefined }
    );

    cultureCode = computed<string | undefined>(() => {
        const currentLocalizationId = this.localizationId();
        if (!currentLocalizationId) {
            return undefined;
        }
        const localization = (this.localizations() ?? []).find(l => l.id === currentLocalizationId);
        return localization?.cultureCode;
    });

    name = computed<string | undefined>(() => {
        const currentLocalizationId = this.localizationId();
        if (!currentLocalizationId) {
            return undefined;
        }

        const explicitTitle = this.title();
        if (explicitTitle) {
            return explicitTitle;
        }
        const localization = (this.localizations() ?? []).find(l => l.id === currentLocalizationId);
        return localization?.name;
    });
}
