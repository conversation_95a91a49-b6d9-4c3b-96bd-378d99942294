@let _allVersionsSelected = allVersionsSelected();
@let _selectedVersions = selectedVersions();
@let _language = newVersionLanguage();
<ui-select
    #uiSelect
    [multiSelect]="true"
    [type]="'primary'"
    [size]="'sm'"
    [showThumbnails]="true"
    [showMultiselectIcon]="false"
    [selected]="_selectedVersions">
    @if (!loaded()) {
        <ng-container ui-select-label>
            <ui-loader
                [inline]="true"
                [style.width]="'100%'" />
        </ng-container>
    } @else if (_language) {
        <ng-container ui-select-label>
            <ui-flag
                class="flag"
                nuiSize="sm"
                [nuiFlag]="$any(_language.localization.cultureCode)" />
            <ui-inline-edit
                size="sm"
                [autoClose]="false"
                [isEditing]="true"
                [hideEditIcon]="true"
                [value]="_language.localization.name"
                [valueCheckFunction]="checkNewVersionValue"
                (save)="handleNewVersionSave($event)"
                (cancel)="handleNewVersionCancel()" />
        </ng-container>
    } @else if (_allVersionsSelected) {
        <ng-container ui-select-label>
            <ui-label
                size="sm"
                weight="bold"
                leadingIcon="grid_on">
                All versions
            </ui-label>
        </ng-container>
    } @else if (_selectedVersions.length === 1) {
        <ng-container ui-select-label>
            @let selectedVersion = _selectedVersions[0];
            <ui-label
                size="sm"
                weight="bold"
                [leadingFlag]="$any(selectedVersion.localization.cultureCode)">
                {{ selectedVersion.name }}
            </ui-label>
        </ng-container>
    } @else if (_selectedVersions.length > 0) {
        <ng-container ui-select-label>
            @let visibleFlagsLimit = 4;
            @for (version of _selectedVersions; track version.id; let index = $index) {
                @if (index < visibleFlagsLimit) {
                    <ui-flag
                        class="flag"
                        nuiSize="sm"
                        [nuiFlag]="$any(version.localization.cultureCode)" />
                }
            }
        </ng-container>
    }
    <div class="ui-scrollbar options-container">
        <ui-option
            size="sm"
            nuiIcon="grid_on"
            [selected]="_allVersionsSelected"
            (click)="selectAllVersions()">
            All versions
        </ui-option>
        @for (selectableVersion of selectableVersions(); track selectableVersion.id) {
            <ui-option
                size="sm"
                [value]="selectableVersion"
                [nuiFlag]="$any(selectableVersion.localization.cultureCode)"
                [selected]="selectableVersion.selected"
                (click)="toggleVersion(selectableVersion, $event)">
                {{ selectableVersion.name }}
            </ui-option>
        }
    </div>
    <ng-container *permissions="'ManageVersions'">
        @if (allowMultiSelect()) {
            <div class="actions">
                <ui-button
                    size="sm"
                    type="ghost-secondary"
                    nuiSvgIcon="add"
                    (click)="openLanguagePicker()" />
                <ui-button
                    size="sm"
                    type="ghost-secondary"
                    nuiSvgIcon="settings"
                    (click)="openVersionDialog()" />
            </div>
            <language-picker
                #languagePicker
                (languageSelected)="handleLanguageSelected($event)" />
        }
    </ng-container>
</ui-select>
