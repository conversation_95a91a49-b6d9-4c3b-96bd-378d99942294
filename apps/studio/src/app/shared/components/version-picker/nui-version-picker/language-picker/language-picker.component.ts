import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, output, signal, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
    UIOptionComponent,
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective,
    UIToggleSwitchComponent
} from '@bannerflow/ui';
import { IBrandLocalization } from '@domain/brand';
import { BrandService } from '@studio/common';

export type LanguageSelected = {
    localization: IBrandLocalization;
    aiTranslation: boolean;
};

@Component({
    selector: 'language-picker',
    imports: [
        NgTemplateOutlet,
        UIToggleSwitchComponent,
        UIPopoverDirective,
        UIPopoverTemplateDirective,
        UIOptionComponent
    ],
    hostDirectives: [{ directive: UIPopoverTargetDirective }],
    styles: `
        :host {
            display: block;
            width: 100%;
        }
        .wrapper {
            width: var(--select-width);
            box-sizing: border-box;

            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;

            background: var(--nui-surface-neutral-subtlest, #ffffff);
            border: 1px solid var(--nui-menu-border-primary-selected, #005eeb);
            border-radius: var(--nui-menu-radius);

            z-index: 4;

            .header {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                padding: var(--nui-space-200);
                gap: var(--nui-space-200);

                width: 100%;
                border-bottom: var(--nui-border-width-small) solid
                    var(--nui-menu-border-primary-default);

                ui-toggle-switch {
                    width: 100%;
                }
            }
        }
    `,
    template: `
        <ui-popover
            #uiPopover="ui-popover"
            [config]="popoverConfig">
            <ng-template ui-popover-template>
                <ng-container *ngTemplateOutlet="content" />
            </ng-template>
        </ui-popover>
        <ng-template #content>
            <div class="wrapper">
                <div class="header">
                    <ui-toggle-switch
                        size="sm"
                        label="AI Translation"
                        (selectedChange)="handleAiTranslationChange($event)" />
                </div>
                @for (localization of localizations(); track localization.id) {
                    <ui-option
                        size="sm"
                        [nuiFlag]="$any(localization.cultureCode)"
                        (click)="handleLocalizationSelected(localization)">
                        {{ localization.name }}
                    </ui-option>
                }
            </div>
        </ng-template>
    `
})
export class LanguagePickerComponent {
    private targetDirective = inject(UIPopoverTargetDirective);
    private uiPopover = viewChild<UIPopoverDirective>('uiPopover');
    private brandService = inject(BrandService);

    languageSelected = output<LanguageSelected>();

    protected localizations = toSignal(this.brandService.localizations$, { initialValue: [] });
    private aiTranslationEnabled = signal(false);

    protected readonly popoverConfig: UIPopoverComponent['config'] = {
        panelClass: 'transparent no-padding',
        position: 'bottom',
        width: '172px',
        offset: { x: 0, y: 5 }
    };

    open(): void {
        this.uiPopover()?.open(this.targetDirective);
    }

    protected handleLocalizationSelected(localization: IBrandLocalization): void {
        this.uiPopover()?.close();
        this.languageSelected.emit({
            localization,
            aiTranslation: this.aiTranslationEnabled()
        });
    }

    protected handleAiTranslationChange(newValue: boolean): void {
        this.aiTranslationEnabled.set(newValue);
    }
}
