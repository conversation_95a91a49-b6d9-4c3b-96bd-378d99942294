:host {
    --select-width: 172px;
}

ui-loader {
    width: 100%;
}

ui-select {
    min-width: 160px;
    max-width: var(--select-width);
}

ui-option {
    --label-line-height: 20px;
}

.options-container {
    overflow-y: auto;
    max-height: 151px;
}

.actions {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: var(--nui-space-200);
    width: 100%;
    height: 48px;
    border-top: 1px solid var(--nui-menu-border-primary-default);
    flex: none;
    align-self: stretch;

    ui-button {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0px;

        flex: none;
        --icon-button-width: 70px;
    }
}
