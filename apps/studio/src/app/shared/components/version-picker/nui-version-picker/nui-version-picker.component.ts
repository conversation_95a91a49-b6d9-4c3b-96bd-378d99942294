import { Component, computed, inject, input, signal, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { PermissionsDirective } from '@app/shared/directives';
import { FiltersService } from '@app/shared/filters';
import { VersionsService } from '@app/shared/versions/state/versions.service';
import {
    InlineEditOnSaveEvent,
    UIButtonComponent,
    UIFlagComponent,
    UIInlineEditComponent,
    UILabelComponent,
    UILoaderComponent,
    UIOptionComponent,
    UISelectComponent,
    UISelectLabelDirective
} from '@bannerflow/ui';
import { IBrandLocalization } from '@domain/brand';
import { IVersion } from '@domain/creativeset/version';
import { BrandService } from '@studio/common/brand/brand.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { VersionDialogService } from '../../version-dialog';
import { LanguagePickerComponent, LanguageSelected } from './language-picker/language-picker.component';

interface SelectableVersion extends IVersion {
    localization: IBrandLocalization;
    selected: boolean;
}

@Component({
    imports: [
        LanguagePickerComponent,
        PermissionsDirective,
        UIButtonComponent,
        UIFlagComponent,
        UIInlineEditComponent,
        UILabelComponent,
        UILoaderComponent,
        UIOptionComponent,
        UISelectComponent,
        UISelectLabelDirective
    ],
    selector: 'nui-version-picker',
    templateUrl: './nui-version-picker.component.html',
    styleUrl: './nui-version-picker.component.scss'
})
export class NuiVersionPickerComponent {
    private brandService = inject(BrandService);
    private environmentService = inject(EnvironmentService);
    private filterService = inject(FiltersService);
    private versionDialogService = inject(VersionDialogService);
    private versionsService = inject(VersionsService);

    allowMultiSelect = input(true);

    protected allVersionsSelected = toSignal(this.filterService.isShowingAllVersions$, {
        initialValue: true
    });

    protected selectableVersions = computed(() => this.computeSelectableVersions());
    protected selectedVersions = computed(() =>
        this.selectableVersions().filter(({ selected }) => selected)
    );
    protected loaded = toSignal(this.versionsService.loaded$, { initialValue: false });

    protected newVersionLanguage = signal<LanguageSelected | undefined>(undefined);

    private uiSelectComponent = viewChild<UISelectComponent>('uiSelect');
    private languagePicker = viewChild<LanguagePickerComponent>('languagePicker');

    private localizations = toSignal(this.brandService.localizations$, { initialValue: [] });
    private _selectableVersions = toSignal(this.versionsService.selectableVersions$, {
        initialValue: []
    });

    private selectedVersionIds = toSignal(this.filterService.selectedVersionIds$, { initialValue: [] });

    protected checkNewVersionValue = (newValue: string): boolean => {
        const duplicatedName = this.selectableVersions().some(({ name }) => name === newValue);
        if (duplicatedName) {
            return false;
        }
        return !!newValue.trim();
    };

    // TODO:(COBE-4061) Move this logic to bf-ui. Should be part of the select component
    protected toggleVersion(version: SelectableVersion, event: MouseEvent): void {
        if (this.allowMultiSelect()) {
            this.handleToggleForMultiSelect(version, event);
            return;
        }
        this.handleToggleForSingleSelect(version);
    }

    private handleToggleForSingleSelect(version: SelectableVersion): void {
        if (!version.selected) {
            this.filterService.selectVersion(version, false);
        }
    }

    private handleToggleForMultiSelect(version: SelectableVersion, event: MouseEvent): void {
        if (event.shiftKey) {
            const selectableVersions = this.selectableVersions();
            const versionClickedIndex = selectableVersions.findIndex(({ id }) => id === version.id);
            const firstSelectedVersionIndex = selectableVersions.findIndex(({ selected }) => selected);

            const range = [firstSelectedVersionIndex, versionClickedIndex];

            // Select anything between versionClickedIndex and firstSelectedVersionIndex
            // (asc or desc order) that's not selected
            const newSelection = selectableVersions
                .slice(Math.min(...range), Math.max(...range) + 1)
                .filter(({ selected }) => !selected)
                .map(({ id }) => id);

            if (newSelection.length) {
                this.filterService.selectVersions(newSelection);
                this.uiSelectComponent()?.close();
            }
            return;
        }
        const isMacMetaKeyPressed = this.environmentService.isMac && event.metaKey;
        const isPCMetaKeyPressed = !this.environmentService.isMac && event.ctrlKey;
        if (isMacMetaKeyPressed || isPCMetaKeyPressed) {
            if (version.selected) {
                this.filterService.deselectVersion(version);
            } else {
                this.filterService.selectVersion(version, true);
            }
            return;
        }
        if (this.selectedVersionIds().length > 1) {
            this.filterService.selectVersion(version, false);
            return;
        }
        if (version.selected) {
            this.filterService.deselectVersion(version);
        } else {
            this.filterService.selectVersion(version, false);
        }
    }

    protected selectAllVersions(): void {
        this.filterService.selectAllVersions();
        this.uiSelectComponent()?.close();
    }

    protected openVersionDialog(): void {
        this.uiSelectComponent()?.close();
        this.versionDialogService.openVersionDialog();
    }

    protected openLanguagePicker(): void {
        this.languagePicker()?.open();
    }

    protected handleLanguageSelected(languageSelected: LanguageSelected): void {
        this.uiSelectComponent()?.close();
        this.versionsService.prepareNewVersion(languageSelected.localization);
        this.newVersionLanguage.set(languageSelected);
    }

    protected handleNewVersionCancel(): void {
        this.newVersionLanguage.set(undefined);
    }

    protected handleNewVersionSave({ newValue }: InlineEditOnSaveEvent): void {
        const autoTranslate = !!this.newVersionLanguage()?.aiTranslation;
        this.versionsService.saveNewVersion(newValue, autoTranslate);
        this.newVersionLanguage.set(undefined);
    }

    private computeSelectableVersions(): SelectableVersion[] {
        const selectableVersions = this._selectableVersions();
        const localizations = this.localizations();
        const selectedVersionIds = this.selectedVersionIds();
        const allVersionsSelected = this.allVersionsSelected();
        const newValue: SelectableVersion[] = selectableVersions.map(version => {
            const localization = localizations.find(({ id }) => id === version.localization.id);
            if (!localization) {
                throw new Error('Localization missing in Brand');
            }
            return {
                ...version,
                localization,
                selected:
                    !allVersionsSelected &&
                    selectedVersionIds.some(selectedVersionId => selectedVersionId === version.id)
            };
        });
        return newValue;
    }
}
