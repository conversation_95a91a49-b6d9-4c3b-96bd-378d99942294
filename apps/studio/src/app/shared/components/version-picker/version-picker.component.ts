import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    HostListener,
    inject,
    Input,
    OnInit,
    Optional,
    Output,
    SkipSelf,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Logger } from '@bannerflow/sentinel-logger';
import { UIInputComponent, UIModule, UINewThemeService, UINotificationService } from '@bannerflow/ui';
import { IBrandLocalization } from '@domain/brand';
import { IVersion } from '@domain/creativeset/version';
import { BrandService } from '@studio/common/brand';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { isElementDescendantOfElement } from '@studio/utils/dom-utils';
import { BehaviorSubject, combineLatest, filter, map, Observable } from 'rxjs';
import { TranslationPanelService } from '../../../pages/manage-view/translation-panel/translation-panel.service';
import { PermissionsDirective } from '../../directives/permissions.directive';
import { FiltersService } from '../../filters/state/filters.service';
import { VersionsService } from '../../versions/state/versions.service';
import { ToolbarButtonComponent } from '../toolbar-button/toolbar-button.component';
import { VersionDialogService } from '../version-dialog/version-dialog.service';
import { VersionLanguagePickerComponent } from '../version-language-picker/version-language-picker.component';
import { VersionFlagComponent } from './version-flag/version-flag.component';
import { NuiVersionPickerComponent } from './nui-version-picker/nui-version-picker.component';

type VersionPickerViewMode = 'show' | 'select' | 'select-localization' | 'add' | 'loading';

@Component({
    imports: [
        CommonModule,
        NuiVersionPickerComponent,
        PermissionsDirective,
        ToolbarButtonComponent,
        UIModule,
        VersionFlagComponent,
        VersionLanguagePickerComponent
    ],
    selector: 'version-picker',
    templateUrl: 'version-picker.component.html',
    styleUrls: ['version-picker.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VersionPickerComponent implements OnInit {
    private destroyRef = inject(DestroyRef);
    private uiNewThemeService = inject(UINewThemeService);

    @Input() isMobile = false;
    @Input() showAllVersions: boolean;
    @Input() allowManageVersions: boolean;
    @Output() pickerOpen = new EventEmitter<void>();
    @Output() pickerClose = new EventEmitter<void>();

    @ViewChild('versionNameInput') versionNameInput: UIInputComponent;

    private viewMode: VersionPickerViewMode = 'show';

    private _viewMode$ = new BehaviorSubject<VersionPickerViewMode>('show');
    viewMode$ = this._viewMode$.asObservable();

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    localizations$ = this.brandService.localizations$;
    isShowingAllVersions$ = this.filtersService.isShowingAllVersions$;
    selectedVersions$ = this.versionsService.selectedVersions$;
    selectedVersionIds$ = this.versionsService.selectedVersions$.pipe(
        map(versions => versions.map(({ id }) => id))
    );
    versions$ = this.versionsService.selectableVersions$;
    newVersionPlaceholder$ = this.versionsService.newVersionPlaceholder$;

    newVersionName = '';

    private logger = new Logger('VersionPickerComponent');
    private isMultiSelecting = false;
    private selectedVersions: IVersion[] = [];
    flagSize$ = this.environmentService.isMobileShowcase$.pipe(
        map(isMobileShowcase => (isMobileShowcase ? undefined : 'tiny'))
    );

    isMobileShowcase$ = this.environmentService.isMobileShowcase$;
    versionPickerArrow$: Observable<'arrow-up' | 'arrow-down'>;
    autoTranslate: boolean;

    constructor(
        private host: ElementRef,
        private uiNotificationService: UINotificationService,
        private brandService: BrandService,
        private versionsService: VersionsService,
        private environmentService: EnvironmentService,
        private filtersService: FiltersService,
        // version dialog service is available only when manageview module is loaded
        @Optional() @SkipSelf() private versionDialogService?: VersionDialogService,
        @Optional() @SkipSelf() private translationPanelService?: TranslationPanelService
    ) {
        this.versionPickerArrow$ = combineLatest([
            this.viewMode$,
            this.environmentService.isMobile$
        ]).pipe(
            map(([viewMode, isMobile]) => {
                const isOpen = viewMode === 'select-localization' || viewMode === 'select';
                if (isMobile) {
                    return isOpen ? 'arrow-down' : 'arrow-up';
                }
                return isOpen ? 'arrow-up' : 'arrow-down';
            })
        );
    }

    ngOnInit(): void {
        this.versionsService.selectedVersions$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(selectedVersions => {
                this.selectedVersions = selectedVersions;
                if (!this.isMultiSelecting) {
                    this.setViewMode('show');
                }
                this.isMultiSelecting = false;
            });

        this.viewMode$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(viewMode => {
            this.viewMode = viewMode;
            if (viewMode === 'select' || viewMode === 'select-localization') {
                this.pickerOpen.emit();
            } else {
                this.pickerClose.emit();
            }
        });

        this.versionsService.newVersionPlaceholder$
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(() => this.viewMode !== 'add')
            )
            .subscribe(newVersionPlaceholder => {
                if (!newVersionPlaceholder) {
                    this.newVersionName = '';
                    this.setViewMode('show');
                    return;
                }

                this.newVersionName = newVersionPlaceholder.name;
                this.setViewMode('add');
                setTimeout(() => {
                    this.versionNameInput?.focus?.(true);
                });
            });

        this.versionsService.loaded$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(loaded => {
            this.setViewMode(loaded ? 'show' : 'loading');
        });

        this.versionsService.error$
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(error => !!error)
            )
            .subscribe(error => {
                this.uiNotificationService.open((error as Error).message, {
                    autoCloseDelay: 5000,
                    type: 'error',
                    placement: 'top'
                });
            });
    }

    toggleSelectView(): void {
        const newViewMode = this.viewMode === 'select' ? 'show' : 'select';
        this.setViewMode(newViewMode);
    }

    showAddLocalizationView(event?: Event): void {
        event?.stopPropagation();
        this.setViewMode('select-localization');
    }

    @HostListener('document:click', ['$event'])
    onDocumentClick(event: MouseEvent): void {
        if (!isElementDescendantOfElement(this.host.nativeElement, event.target)) {
            this.deselect();
        }
    }

    @HostListener('window:keydown.escape')
    onEscapeKeyDown(): void {
        this.deselect();
    }

    private deselect(): void {
        switch (this.viewMode) {
            case 'select-localization':
                this.setViewMode('select');
                break;
            case 'select':
            default:
                this.setViewMode('show');
                break;
        }
    }

    private async checkTranslationPanelPristiness(): Promise<boolean> {
        if (!this.translationPanelService) {
            return true;
        }
        return await this.translationPanelService.isPristine();
    }

    async selectVersion(event: MouseEvent, version: IVersion): Promise<void> {
        const canContinue = await this.checkTranslationPanelPristiness();
        if (!canContinue) {
            return;
        }
        const defmodKey = navigator.userAgent.includes('Mac OS X') ? event.metaKey : event.ctrlKey;
        const multiSelect = defmodKey && this.allowManageVersions;

        this.isMultiSelecting = multiSelect;
        if (this.isMultiSelecting && this.selectedVersions.find(v => v.id === version.id)) {
            if (this.selectedVersions.length > 1) {
                this.filtersService.deselectVersion(version);
            }
        } else {
            this.filtersService.selectVersion(version, multiSelect);
        }
    }

    async selectAllVersions(): Promise<void> {
        const canContinue = await this.checkTranslationPanelPristiness();
        if (!canContinue) {
            return;
        }
        this.filtersService.selectAllVersions();
    }

    selectLocalization(localization: IBrandLocalization, autoTranslate: boolean): void {
        this.autoTranslate = autoTranslate;
        this.versionsService.prepareNewVersion(localization);
    }

    stopAddVersion(): void {
        this.versionsService.cancelNewVersion();
    }

    saveNewVersion(): void {
        this.versionsService.saveNewVersion(this.newVersionName, this.autoTranslate);
    }

    openVersionDialog(): void {
        this.versionDialogService?.openVersionDialog(this.onVersionDialogClosed);
        this.setViewMode('show');
    }

    onVersionDialogClosed = (): void => {
        this.setViewMode('show');
    };

    private setViewMode(newViewMode: VersionPickerViewMode): void {
        this._viewMode$.next(newViewMode);
    }
}
