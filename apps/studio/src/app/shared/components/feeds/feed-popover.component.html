@if (feed) {
    <div class="header">
        <div class="feed">
            <ui-svg-icon
                icon="feed"
                nuiIcon="rss_feed"
                size="sm"></ui-svg-icon>
            @if (isNewUI()) {
                <ui-label
                    weight="bold"
                    [truncate]="true"
                    >{{ feedName }}</ui-label
                >
            } @else {
                <strong
                    truncateSpan
                    [spanText]="feedName"></strong>
            }
        </div>
        @if (canEditSource) {
            <feed-picker
                (feedSelectionChanged)="useSelectedFeedItem($event)"
                [value]="feed"
                [minimized]="true"></feed-picker>
        }
    </div>
    <div class="settings">
        <div
            class="setting"
            data-test-id="path-input">
            @if (isNewUI()) {
                <ui-label size="xs">Field</ui-label>
            } @else {
                Field
            }
            @if (disableInputs) {
                <div>
                    {{ path }}
                </div>
            } @else {
                <ui-select
                    size="xs"
                    type="secondary"
                    [useTargetWidth]="true"
                    [(selected)]="path"
                    [placeholder]="placeholder"
                    [class.deleted]="!!placeholder"
                    (selectedChange)="onFeedPathValueChanged($event)"
                    data-test-id="feed-field">
                    @for (field of fields; track $index) {
                        <ui-option
                            [value]="field.text"
                            [disabled]="field.showDisabledText">
                            <div
                                [uiTooltip]="field.disabledTooltipText ?? ''"
                                [uiTooltipDisabled]="
                                    !field.showDisabledText || !field.disabledTooltipText
                                ">
                                {{ field.text }}
                            </div>
                        </ui-option>
                    }
                </ui-select>
            }
        </div>
        <div
            class="setting"
            data-test-id="occurrence-input">
            @if (isNewUI()) {
                <ui-label size="xs">Step on</ui-label>
            } @else {
                Step on
            }
            @if (disableInputs) {
                @if (isNewUI()) {
                    <ui-label
                        type="disabled"
                        size="xs"
                        >{{ visibleFeedStep.occurrence }}</ui-label
                    >
                } @else {
                    <div>
                        {{ visibleFeedStep.occurrence }}
                    </div>
                }
            } @else {
                <ui-select
                    size="xs"
                    type="secondary"
                    [useTargetWidth]="true"
                    [(selected)]="visibleFeedStep.occurrence"
                    (selectedChange)="onFeedOccurrenceChanged($event)"
                    placeholder="-">
                    <ui-option value="loop">Creative loop</ui-option>
                    <ui-option value="none">Never</ui-option>
                </ui-select>
            }
        </div>
        <div class="half">
            <div
                class="setting"
                data-test-id="size-input">
                @if (isNewUI()) {
                    <ui-label size="xs">Step size</ui-label>
                } @else {
                    Step size
                }
                @if (disableInputs) {
                    @if (isNewUI()) {
                        <ui-label
                            type="disabled"
                            size="xs"
                            >{{ visibleFeedStep.size }}</ui-label
                        >
                    } @else {
                        <div>
                            {{ visibleFeedStep.size }}
                        </div>
                    }
                } @else {
                    <ui-number-input
                        size="xs"
                        type="secondary"
                        [arrowButtons]="!isNewUI()"
                        [(value)]="visibleFeedStep.size"
                        [min]="1"
                        placeholder="-"
                        [disabled]="feed.step.occurrence === 'none'"
                        (valueChange)="onFeedValueChanged($event, 'size')"
                        data-test-id="feed-step-size">
                    </ui-number-input>
                }
            </div>
            <div
                class="setting"
                data-test-id="start-input">
                @if (isNewUI()) {
                    <ui-label size="xs">Start at</ui-label>
                } @else {
                    Start at
                }
                @if (disableInputs) {
                    @if (isNewUI()) {
                        <ui-label
                            type="disabled"
                            size="xs"
                            >{{ visibleFeedStep.start }}</ui-label
                        >
                    } @else {
                        <div>
                            {{ visibleFeedStep.start }}
                        </div>
                    }
                } @else {
                    <ui-number-input
                        size="xs"
                        type="secondary"
                        [arrowButtons]="!isNewUI()"
                        [value]="visibleFeedStep.start"
                        [min]="1"
                        [max]="totalFeedItems || 99"
                        placeholder="-"
                        (valueChange)="onFeedValueChanged($event, 'start')"
                        data-test-id="feed-step-start">
                    </ui-number-input>
                }
            </div>
        </div>
    </div>
}
