:where(:root:not([data-uinew])) :host {
    .header {
        display: flex;
        align-items: center;
        height: 24px;
        padding: 0 1rem;
        user-select: none;
        padding-top: 0.5rem;
        justify-content: space-between;
        overflow: hidden;

        feed-picker {
            opacity: 0.3;
            cursor: pointer;
        }

        .feed {
            display: flex;
            overflow: hidden;

            > strong {
                white-space: nowrap;
                overflow: hidden;
            }

            .icon {
                margin-right: 0.5rem;
            }
        }
    }

    .settings {
        background: var(--studio-color-surface-second);
        padding: 1rem 1.2rem;

        .setting {
            display: grid;
            grid-template-columns: 1fr auto;
            align-items: center;
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0px;
            }
        }

        .half {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 20px;

            .setting {
                margin-bottom: 0;

                &:first-child {
                    width: 10.3rem;
                }

                &:last-child {
                    width: 8.1rem;
                }
            }

            .input {
                width: 3rem;
            }
        }
    }

    .ui-select {
        width: 13rem;

        &.deleted {
            opacity: 0.6;
        }

        ::ng-deep .button {
            min-width: auto;
        }
    }
}
