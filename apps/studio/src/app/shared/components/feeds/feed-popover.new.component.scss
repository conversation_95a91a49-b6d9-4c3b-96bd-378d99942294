:where(:root[data-uinew]) :host {
    display: flex;
    flex-direction: column;
    padding: 0;
    width: 200px;
    gap: var(--nui-space-500);

    .header {
        display: flex;
        align-items: center;
        height: 2rem;
        user-select: none;
        justify-content: space-between;
        overflow: hidden;

        .feed {
            display: flex;
            overflow: hidden;
            gap: var(--nui-space-200);
        }
    }
    .settings {
        display: flex;
        flex-direction: column;
        gap: var(--nui-space-300);
        width: 100%;
        .half {
            display: flex;
            justify-content: space-between;
            gap: var(--nui-space-500);

            .setting {
                width: 50%;
            }
            ui-number-input {
                width: 2.4rem;
            }
        }
        .setting {
            display: flex;
            align-items: center;
            justify-content: space-between;

            ui-select {
                width: 13.6rem;
            }
        }
    }
}
