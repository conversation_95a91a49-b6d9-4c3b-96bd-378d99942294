:where(:root:not([data-uinew])) :host {
    display: block;
    cursor: pointer;

    &:hover {
        .feed-property {
            .feed-icon {
                color: var(--studio-color-black);
            }
        }
    }

    .feed-property {
        display: grid;
        grid-template-columns: auto 1fr;
        grid-gap: 5px;
        align-items: center;
        justify-items: start;
        font-size: 11px;

        &.unselected {
            grid-template-columns: auto;
        }

        .feed-icon {
            color: var(--studio-color-grey-71);
        }

        .feed-name {
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            overflow: hidden;
            color: var(--studio-color-primary);
        }
    }
}

:where(:root[data-uinew]) :host {
    display: flex;
    cursor: pointer;
    align-items: center;
    .feed-property {
        ui-button {
            --width: 100%;
        }
    }
}
