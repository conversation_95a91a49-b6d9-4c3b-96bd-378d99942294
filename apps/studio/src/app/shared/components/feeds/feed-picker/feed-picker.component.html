@if (!minimized) {
    <div
        class="feed-property"
        [class.unselected]="!value.id"
        (click)="openDialog()"
        data-test-id="feed-picker-edit">
        @if (isNewUI()) {
            <ui-button
                [nuiSvgIcon]="value.id && !mixed ? 'rss_feed' : undefined"
                size="xs"
                nuiType="plain-secondary"
                [text]="name || 'Select feed'" />
        } @else {
            @if (value.id && !mixed) {
                <ui-svg-icon
                    class="feed-icon"
                    size="sm"
                    icon="feed"></ui-svg-icon>
            }
            <div
                [uiTooltipDisabled]="!value.id"
                [uiTooltip]="name"
                class="feed-name">
                {{ name || 'Select feed' }}
            </div>
        }
    </div>
} @else {
    @if (isNewUI()) {
        <ui-button
            nuiSvgIcon="edit"
            size="xs"
            nuiType="plain-secondary"
            (click)="openDialog()" />
    } @else {
        <div>
            <ui-svg-icon
                [icon]="'edit'"
                nuiIcon="edit"
                (click)="openDialog()"
                data-test-id="feed-picker-edit"></ui-svg-icon>
        </div>
    }
}
