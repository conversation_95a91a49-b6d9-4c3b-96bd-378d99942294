import { ElementRef, inject, Injectable } from '@angular/core';
import { IUIPopoverConfig, UINewThemeService, UIPopoverRef, UIPopoverService } from '@bannerflow/ui';
import { IFeedStore } from '@domain/creative/feed/feed-store.header';
import { IFeed, IFeedStep } from '@domain/feed';
import { cloneDeep } from '@studio/utils/clone';
import { Subject } from 'rxjs';
import { FeedPopoverComponent } from './feed-popover.component';
import { FeedService } from './feed.service';

interface OpenOptions {
    disableInputs?: boolean;
    disableEditSource?: boolean;
}

@Injectable({ providedIn: 'root' })
export class FeedSettingService {
    private feedService = inject(FeedService);
    private uiPopover = inject(UIPopoverService);
    private uiNewThemeService = inject(UINewThemeService);

    feed: IFeed;
    visibleFeedStep: IFeedStep | undefined;
    elementId: string;
    feedStore: IFeedStore;
    openingInProcess = false;
    lastFeedPillClicked: string | undefined; // ugly hack to avoid global vars, I'm truly sorry
    private oldFeed: IFeed;
    private feedPopoverRef: UIPopoverRef<FeedPopoverComponent>;
    private config: IUIPopoverConfig = {
        position: 'left',
        arrowPosition: 'right',
        panelClass: 'no-padding',
        width: '229px',
        maxWidth: '229px',
        minWidth: '229px',
        theme: 'tiny',
        backgroundColor: 'var(--studio-color-surface-secondary)'
    };

    private _feedValueChanged$ = new Subject<{ newFeed: IFeed; oldFeed: IFeed }>();
    feedValueChanged$ = this._feedValueChanged$.asObservable();

    private _updateFeedPopover$ = new Subject<void>();
    updateFeedPopover$ = this._updateFeedPopover$.asObservable();

    private _close$ = new Subject<void>();
    close$ = this._close$.asObservable();

    async open(
        target: ElementRef,
        elementId: string,
        feed: IFeed,
        visibleFeedStep: IFeedStep | undefined,
        feedStore: IFeedStore,
        { disableInputs = false, disableEditSource = false }: OpenOptions,
        config?: IUIPopoverConfig
    ): Promise<void> {
        if (this.openingInProcess) {
            return;
        } else {
            this.openingInProcess = true;
        }

        await this.feedService.getFeeds();

        this.feed = feed;
        this.visibleFeedStep = visibleFeedStep;
        this.oldFeed = cloneDeep(feed);
        this.elementId = elementId;
        this.feedStore = feedStore;
        const popoverConfig: IUIPopoverConfig = {
            ...this.config,
            ...(this.uiNewThemeService.isNewThemeEnabled() && {
                panelClass: 'ui-card-popover',
                width: '226px',
                size: 'sm'
            }),
            ...config
        };

        this.feedPopoverRef = this.uiPopover.openComponent<FeedPopoverComponent>(
            target,
            FeedPopoverComponent,
            {
                ...popoverConfig,
                data: {
                    ...popoverConfig.data,
                    disableInputs
                }
            }
        );

        this.feedPopoverRef.onClose.subscribe(() => {
            this._close$.next();
        });

        if (disableEditSource) {
            this.feedPopoverRef.afterViewInit.then(() => {
                this.feedPopoverRef?.subComponentRef?.instance?.hideEditSource();
            });
        }
        this.openingInProcess = false;
    }

    changeFeedValue(feed: IFeed): void {
        this._feedValueChanged$.next({ newFeed: feed, oldFeed: this.oldFeed });
    }

    updateFeedPopoverValues(feed: IFeed, feedStore: IFeedStore): void {
        this.feed = feed;
        this.feedStore = feedStore;
        this._updateFeedPopover$.next();
    }

    close(): void {
        this._close$.next();
        if (this.feedPopoverRef) {
            this.feedPopoverRef.close();
        }
    }
}
