import { Component, inject } from '@angular/core';
import { UIPREnvPickerComponent } from '@bannerflow/ui';
import { EnvironmentService } from '@studio/common/services/environment.service';

@Component({
    imports: [UIPREnvPickerComponent],
    selector: 'toggle-environment',
    templateUrl: './toggle-environment.component.html',
    styleUrls: ['./toggle-environment.component.scss']
})
export class ToggleEnvironmentComponent {
    private environmentService = inject(EnvironmentService);

    showToggleEnvironment = this.environmentService.appEnvironment.stage === 'sandbox';
}
