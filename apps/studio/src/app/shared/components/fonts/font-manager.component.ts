import { Component, ElementRef, inject, OnD<PERSON>roy, OnInit, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { UIDialogComponent } from '@bannerflow/ui';
import { AuthService, BrandService } from '@studio/common';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { take } from 'rxjs';

const FM_READY_MESSAGE = 'fm-ready';
const TOKEN_REFRESH_TIME = 30 * 60 * 1000;

interface FontManagerMessage {
    accessToken: string;
    accountSlug?: string;
}

@Component({
    selector: 'font-manager',
    templateUrl: 'font-manager.component.html',
    styleUrls: ['font-manager.component.scss'],
    standalone: false
})
export class FontManagerComponent implements OnInit, OnDestroy {
    private authService = inject(AuthService);
    private brandService = inject(BrandService);
    private dialogComponent = inject(UIDialogComponent);
    private environmentService = inject(EnvironmentService);
    private sanitizer = inject(DomSanitizer);

    private iframe = viewChild<ElementRef<HTMLIFrameElement>>('iframe');

    private accessToken = toSignal(this.authService.getAccessToken(), { initialValue: '' });
    private accountSlug = toSignal(this.brandService.accountSlug$, { initialValue: '' });

    url: SafeUrl;

    private intervalId: number;

    ngOnInit(): void {
        const brandId = this.dialogComponent.config.data?.brandId;
        if (!brandId) {
            throw new Error('Brand ID not set');
        }
        const { fontManager } = this.environmentService.origins;
        const unsanitizedUrl = `${fontManager}/in-frame/${brandId}`;
        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(unsanitizedUrl);

        const handleFMReady = (event: MessageEvent): void => {
            if (event.origin !== fontManager) {
                return;
            }

            const data = (event as MessageEvent).data;
            if (data === FM_READY_MESSAGE) {
                this.sendToFM({
                    accessToken: this.accessToken(),
                    accountSlug: this.accountSlug()
                });
                window.removeEventListener('message', handleFMReady);
            }
        };
        window.addEventListener('message', handleFMReady);

        // Update token
        this.intervalId = window.setInterval(() => {
            this.authService
                .getAccessToken()
                .pipe(take(1))
                .subscribe(accessToken => {
                    this.sendToFM({ accessToken });
                });
        }, TOKEN_REFRESH_TIME);
    }

    ngOnDestroy(): void {
        if (this.intervalId) {
            window.clearInterval(this.intervalId);
        }
    }

    private sendToFM(data: FontManagerMessage): void {
        const iframe = this.iframe();
        const { fontManager } = this.environmentService.origins;
        iframe?.nativeElement.contentWindow?.postMessage(data, fontManager);
    }
}
