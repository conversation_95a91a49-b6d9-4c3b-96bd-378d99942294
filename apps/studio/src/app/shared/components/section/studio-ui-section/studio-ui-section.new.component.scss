:host.new-ui {
    --header-padding: 0;
    display: block;

    &:not(.inset) {
        .wrapper {
            padding: 0;
        }
    }

    &.divider {
        border-bottom: 1px solid var(--nui-border-neutral-subtle);
    }

    &.inset {
        --header-padding: 0 0 var(--nui-space-200) 0;

        .wrapper {
            padding: var(--nui-space-200) var(--nui-space-200) 0 var(--nui-space-200);
        }
        &.has-actions {
            .wrapper {
                padding-top: var(--nui-space-100);
            }
            .section-header {
                padding-bottom: var(--nui-space-100);
            }
        }
    }
    .wrapper {
        pointer-events: none;
        &.disabled {
            pointer-events: none;
            --color: var(--nui-text-disabled);
            .action {
                pointer-events: none;
            }
        }
    }
    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: var(--header-padding);
        cursor: default;

        .header-piece,
        .action {
            display: flex;
            align-items: center;
            gap: var(--nui-space-050);
            overflow: hidden;
        }
        .header-name {
            flex: 1;
            pointer-events: auto;
        }
        .action {
            pointer-events: auto;
        }
        &.clickable {
            cursor: pointer;
            pointer-events: auto;
        }
    }
    .section-body {
        pointer-events: auto;
    }
}
