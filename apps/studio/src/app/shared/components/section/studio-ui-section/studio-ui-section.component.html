@if (isNewUI()) {
    <div
        class="wrapper"
        [class.disabled]="disabled">
        <ui-accordion>
            <ui-accordion-item
                [expanded]="!collapsed && !empty"
                (toggleChange)="collapsed = !$event"
                size="xs"
                [inset]="inset()"
                [simple]="true">
                <ng-template ui-accordion-header-template>
                    @let clickable = (clickableHeader || collapsable || toggle) && !disabled;

                    <div
                        class="section-header"
                        [class.clickable]="clickable"
                        (click)="onHeaderClick($event)">
                        <div
                            class="header-piece header-name"
                            (click)="!clickable && $event.stopPropagation()">
                            @if (headlineIcon) {
                                <ui-svg-icon
                                    icon="none"
                                    [nuiIcon]="nuiHeadlineIcon()"
                                    [uiTooltip]="headlineTooltip || headline"
                                    [uiTooltipDisabled]="headlineTooltipDisabled"
                                    size="xs" />
                            }
                            <ui-label
                                weight="bold"
                                size="sm"
                                [truncate]="true"
                                [uiTooltip]="headlineTooltip || headline"
                                [uiTooltipDisabled]="headlineTooltipDisabled">
                                {{ headline }}
                            </ui-label>
                            @if (headlineIconAfter) {
                                <ui-svg-icon
                                    icon="none"
                                    [nuiIcon]="nuiHeadlineIconAfter()"
                                    [uiTooltip]="headlineTooltip || headline"
                                    [uiTooltipDisabled]="headlineTooltipDisabled"
                                    size="xs" />
                            }
                        </div>
                        <div class="header-piece">
                            @if (customAction) {
                                <ng-container *ngTemplateOutlet="customAction"></ng-container>
                            }
                            <div
                                class="action"
                                #actionElement>
                                <ng-container *ngTemplateOutlet="headerTemplate"></ng-container>
                                @if (dropdownTarget) {
                                    <ui-button
                                        type="plain-secondary"
                                        nuiSvgIcon="more_vert"
                                        size="sm"
                                        (click)="$event.stopPropagation()"
                                        [uiDropdownTarget]="dropdownTarget" />
                                } @else {
                                    @for (a of actions; track a.id) {
                                        <ui-button
                                            [type]="a.nuiType ?? 'ghost-primary'"
                                            size="xs"
                                            [text]="a.text"
                                            [attr.id]="a.id"
                                            [uiDropdownTarget]="a.dropdownTarget"
                                            [style.visibility]="a.hidden ? 'hidden' : 'visible'"
                                            [nuiSvgIcon]="a.nuiIcon"
                                            (click)="executeAction(a, $event)" />
                                    }
                                }
                            </div>
                            <ui-button
                                type="plain-secondary"
                                size="sm"
                                [style.display]="toggle ? 'block' : 'none'"
                                class="toggle-action"
                                (click)="toggleProperties()">
                                {{ toggleText }}
                            </ui-button>

                            @if (collapsable && !empty) {
                                <ui-svg-icon
                                    size="xs"
                                    icon="none"
                                    class="collapse-button"
                                    (click)="toggleCollapse()"
                                    [nuiIcon]="
                                        collapsed ? 'keyboard_arrow_up' : 'keyboard_arrow_down'
                                    " />
                            }
                        </div>
                    </div>
                </ng-template>
                <div
                    class="section-body"
                    #sectionBody>
                    <ng-container *ngTemplateOutlet="contentTemplate"></ng-container>
                </div>
            </ui-accordion-item>
        </ui-accordion>
    </div>
} @else {
    @if (headline) {
        <div
            class="header"
            [ngClass]="{ clickable: clickableHeader }"
            (click)="onHeaderClick($event)">
            <div class="inner">
                @if (headlineIcon) {
                    <div
                        class="headline-icon headline-icon-before"
                        [uiTooltip]="headlineTooltip || headline"
                        [uiTooltipDisabled]="headlineTooltipDisabled">
                        <ui-svg-icon [icon]="headlineIcon" />
                    </div>
                }
                <span
                    class="headline"
                    [class.inactive]="inactive"
                    [uiTooltip]="headlineTooltip || headline"
                    [uiTooltipDisabled]="headlineTooltipDisabled">
                    {{ headline }}
                </span>

                @if (headlineIconAfter) {
                    <div
                        class="headline-icon headline-icon-after"
                        [uiTooltip]="headlineTooltip || headline"
                        [uiTooltipDisabled]="headlineTooltipDisabled">
                        <ui-svg-icon
                            [icon]="headlineIconAfter"
                            size="xs" />
                    </div>
                }
            </div>
            <span
                [hidden]="!toggle"
                class="toggle-action"
                (click)="toggleProperties()">
                {{ toggleText }}
            </span>
            @if (customAction) {
                <ng-container *ngTemplateOutlet="customAction" />
            }

            <div
                class="action"
                #actionElement>
                <ng-container *ngTemplateOutlet="headerTemplate"></ng-container>
                @if (dropdownTarget) {
                    <ui-svg-icon
                        icon="kebab"
                        [uiDropdownTarget]="dropdownTarget" />
                } @else {
                    @for (a of actions; track a.id) {
                        @if (a.icon) {
                            <ui-svg-icon
                                [attr.id]="a.id"
                                [style.display]="a.hidden ? 'none' : 'inline-block'"
                                [icon]="a.icon"
                                [style.cursor]="'pointer'"
                                (click)="executeAction(a, $event)" />
                        }
                    }
                }
                @if (collapsable) {
                    <ui-svg-icon
                        class="collapse-button"
                        [ngClass]="{ collapsed: collapsed }"
                        (click)="toggleCollapse()"
                        icon="arrow-up" />
                }
            </div>
        </div>
    }
    <div
        #sectionBody
        class="section-body ui-scrollbar"
        [style.display]="collapsed || empty ? 'none' : 'block'">
        <ng-container *ngTemplateOutlet="contentTemplate"></ng-container>
    </div>
}

<ng-template #headerTemplate>
    <ng-content select="[section-header-action]" />
</ng-template>

<ng-template #contentTemplate>
    <ng-content />
</ng-template>
