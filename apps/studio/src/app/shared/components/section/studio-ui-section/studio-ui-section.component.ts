import { CommonModule } from '@angular/common';
import {
    Component,
    Input,
    Output,
    EventEmitter,
    ViewChild,
    ElementRef,
    TemplateRef,
    input
} from '@angular/core';
import { Icon, NewButtonType, UIDropdownComponent, UIModule, UINUIIcon } from '@bannerflow/ui';

@Component({
    imports: [CommonModule, UIModule],
    selector: 'studio-ui-section',
    templateUrl: './studio-ui-section.component.html',
    styleUrls: ['./studio-ui-section.component.scss', './studio-ui-section.new.component.scss'],
    host: {
        '[class.new-ui]': 'isNewUI()',
        '[class.has-actions]': 'actions.length > 0',
        '[class.inset]': 'inset()',
        '[class.divider]': 'divider()'
    }
})
export class StudioUISectionComponent {
    @Input() headline: string;
    @Input() headlineTooltip = '';
    @Input() headlineIcon: Icon | undefined;
    @Input() headlineIconAfter: Icon | undefined;
    @Input() headlineTooltipDisabled = false;
    @Input() collapsable: boolean;
    @Input() collapsed = false;
    @Input() actions: ISectionAction[] = [];
    @Input() clickableHeader = false;
    @Input() dropdownTarget: UIDropdownComponent | undefined;
    @Input() customAction: TemplateRef<any> | undefined;
    @Input() empty = false;
    @Input() toggle = false;
    @Input() inactive = true;
    @Input() disabled = false;
    isNewUI = input<boolean>(false);
    nuiHeadlineIcon = input<UINUIIcon>();
    nuiHeadlineIconAfter = input<UINUIIcon>();
    inset = input<boolean>(true);
    divider = input<boolean>(false);

    @Output() toggleClick = new EventEmitter<boolean>();

    @Output() headerClick: EventEmitter<MouseEvent> = new EventEmitter();
    @ViewChild('actionElement') actionElement: ElementRef;
    @ViewChild('sectionBody') sectionBody: ElementRef;

    toggleText = 'Less Options';
    hideProperties: boolean;
    // inactive = true;

    /**
     * When header is clicked. Note that clicking any icons will not trigger this event.
     */
    onHeaderClick($event: MouseEvent): void {
        if (this.clickableHeader && !this.disabled) {
            this.headerClick.emit($event);
        }
    }

    toggleProperties(): void {
        if (this.disabled) {
            return;
        }
        this.hideProperties = !this.hideProperties;
        this.toggleText = this.hideProperties ? 'More Options' : 'Less Options';
        this.toggleClick.emit(this.hideProperties);
    }

    toggleCollapse(): void {
        if (!this.disabled) {
            this.collapsed = !this.collapsed;
        }
    }

    executeAction(action: ISectionAction, event?: Event): void {
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        }

        action.action?.call(action.thisArg, action.arg0, action.arg1, action.arg2);
    }
}

interface ISectionAction {
    id: string;
    action?: (this: any, arg0: any, arg1: any, arg2: any) => void;
    icon?: Icon;
    nuiIcon?: UINUIIcon;
    dropdownTarget?: UIDropdownComponent;
    text?: string;
    hidden: boolean;
    nuiType?: NewButtonType;
    thisArg?: any;
    arg0?: any;
    arg1?: any;
    arg2?: any;
}
