:where(:root:not([data-uinew])) :host {
    --arrow-size: 10px;
    --background-color: var(--studio-color-surface-second);
    --border-color: var(--studio-color-border-second);
    position: relative;
    display: block;
    width: 100%;
    z-index: 10;
    height: auto;

    &.show-arrow .arrow {
        border-left: 1px solid var(--studio-color-border-second);
        border-top: 1px solid var(--studio-color-border-second);
    }

    &.show-background {
        .arrow,
        .animate {
            background: var(--studio-color-surface-second);
        }
    }

    &.show-shadow {
        filter: url(#inset-shadow);
    }

    .content {
        padding: var(--section-padding);
        display: block;
        position: relative;
        height: auto;
        overflow: visible;
        z-index: 2;
    }

    .animate {
        padding: 0;
        display: block;
        position: relative;
        height: auto;
        border-top: 1px solid var(--studio-color-border-second);
        border-bottom: 1px solid var(--studio-color-border-second);

        &.no-borders {
            border-top: none;
            border-bottom: none;
        }
    }

    .arrow {
        --half-size: calc(var(--arrow-size) / 2);
        --negative-half-size: calc(var(--arrow-size) / (-2));
        overflow: visible;
        position: absolute;
        width: var(--arrow-size);
        height: var(--arrow-size);
        left: 50%;
        top: var(--half-size);
        z-index: 1;
        opacity: 0;
        pointer-events: none;
        transform: translate(calc(var(--arrow-size) / (-2)), calc(var(--arrow-size) / (-2)))
            rotate(45deg);

        display: block;
        transition:
            top 0.2s ease,
            opacity 0.2s ease;

        &.open {
            top: -1px;
            opacity: 1;
        }
    }
}
