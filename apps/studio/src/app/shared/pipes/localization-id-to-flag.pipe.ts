import { Pipe, PipeTransform } from '@angular/core';
import { BrandService } from '@studio/common/brand';
import { take } from 'rxjs';
import { UINUIFlag } from '@bannerflow/ui';
import { IBrandLocalization } from '@domain/brand/brand';

@Pipe({
    standalone: true,
    name: 'localizationIdToFlag'
})
export class LocalizationIdToFlagPipe implements PipeTransform {
    private localizations: IBrandLocalization[] = [];

    constructor(private brandService: BrandService) {
        this.brandService.localizations$.pipe(take(1)).subscribe(localizations => {
            this.localizations = localizations;
        });
    }

    transform(localizationId: string): UINUIFlag {
        const localization = this.localizations.find(l => l.id === localizationId);
        return localization?.cultureCode as UINUIFlag;
    }
}
