import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SentinelService } from '@bannerflow/sentinel';
import { serializeVersionProperties } from '@creative/serialization/versions/version-serializer';
import { creativeToChecksum } from '@creative/utils';
import { convertCreativesetToDto, deserializeCreative } from '@data/deserialization/creativeset';
import { deserializeVersion, deserializeVersions } from '@data/deserialization/version';
import {
    CREATE_VERSION_MUTATION,
    DELETE_VERSIONS_MUTATION,
    SET_DEFAULT_VERSION_MUTATION,
    UPDATE_VERSIONS_MUTATION
} from '@data/graphql/versions.queries';
import { VersionInfoDto } from '@domain/api/generated/sapi';
import { CreativeUpdate, ICreative } from '@domain/creativeset/creative/creative';
import { ICreateVersionsVariables, IVersion } from '@domain/creativeset/version';
import { CreativesetDataService, fixVersionProperties, validateVersions } from '@studio/common';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { FeatureService } from '@studio/common/feature/feature.service';
import { cloneDeep } from '@studio/utils/clone';
import { Apollo } from 'apollo-angular';
import { Observable, map, switchMap } from 'rxjs';
import { CreateVersionDto, DeleteVersionsDto } from '@domain/api/generated/design-api';

@Injectable({
    providedIn: 'root'
})
export class VersionsDataService {
    private apollo = inject(Apollo);
    private creativesetDataService = inject(CreativesetDataService);
    private httpClient = inject(HttpClient);
    private readonly dapiOrigin = inject(EnvironmentService).origins.designApi;
    private readonly sapiOrigin = inject(EnvironmentService).origins.sapi;
    private featureService = inject(FeatureService);
    private sentinelService = inject(SentinelService);

    dapiCreateVersions(
        creativeSetId: string,
        newVersions: CreateVersionDto[]
    ): Observable<{ versions: IVersion[]; creatives: ICreative[] }> {
        const url = `${this.dapiOrigin}/creative-sets/${creativeSetId}/versions`;

        return this.httpClient
            .post<never>(url, {
                versions: newVersions
            })
            .pipe(
                switchMap(() => this.creativesetDataService.getCreativeset(creativeSetId)),
                map(creativeSet => {
                    const createdVersions = creativeSet.versions.filter(version =>
                        newVersions.some(v => v.name === version.name)
                    );
                    const createdCreatives = creativeSet.creatives.filter(creative =>
                        createdVersions.some(v => v.id === creative.version.id)
                    );
                    return { versions: createdVersions, creatives: createdCreatives };
                })
            );
    }
    createVersions(
        creativeSetId: string,
        newVersions: IVersion[]
    ): Observable<{ versions: IVersion[]; creatives: ICreative[] }> {
        newVersions = fixVersionProperties(
            newVersions,
            this.creativesetDataService.creativeset.creatives,
            this.sentinelService
        );
        validateVersions(newVersions);

        const versionsToUpdate: ICreateVersionsVariables['versions'] = newVersions.map(version => ({
            name: version.name,
            localizationId: version.localization.id,
            targetUrl: version.targetUrl,
            properties: serializeVersionProperties(version.properties, false)
        }));
        const variables: ICreateVersionsVariables = {
            versions: versionsToUpdate,
            creativesetId: creativeSetId
        };

        return this.apollo
            .mutate({
                mutation: CREATE_VERSION_MUTATION,
                variables
            })
            .pipe(
                map(result => {
                    if (!result.data?.createVersions) {
                        throw new Error('Could not deserialize CreateVersion response.');
                    }

                    const { versions, creatives } = result.data.createVersions;
                    const creativeset = this.creativesetDataService.creativeset;
                    const creativesetDto = convertCreativesetToDto(creativeset, this.sentinelService);

                    creativesetDto.versions.push(...versions);

                    const deserializedCreatives = creatives.map(creative =>
                        deserializeCreative(creativesetDto, creative, creativeset.designs)
                    );

                    return {
                        versions: deserializeVersions(versions),
                        creatives: cloneDeep(deserializedCreatives)
                    };
                })
            );
    }

    updateVersions(
        creativesetId: string,
        versions: IVersion[],
        dirtyCreatives: CreativeUpdate[]
    ): Observable<IVersion[]> {
        const newVersions = fixVersionProperties(
            versions,
            this.creativesetDataService.creativeset.creatives,
            this.sentinelService
        );

        validateVersions(newVersions);

        const variables = {
            versions: newVersions.map(version => ({
                id: version.id,
                name: version.name,
                localizationId: version.localization.id,
                targetUrl: version.targetUrl,
                properties: serializeVersionProperties(version.properties, false)
            })),
            creativesetId,
            creatives: dirtyCreatives
        };

        return this.apollo
            .mutate({
                mutation: UPDATE_VERSIONS_MUTATION,
                variables
            })
            .pipe(
                map(result => {
                    return deserializeVersions(result.data?.updateVersions.versions || []);
                })
            );
    }

    deleteVersions(creativeSetId: string, versions: IVersion[]): Observable<string[]> {
        const versionIds = versions.map(({ id }) => id);
        if (this.featureService.isFeatureEnabled('design-api-put')) {
            const url = `${this.dapiOrigin}/creative-sets/${creativeSetId}/versions`;

            const payload: DeleteVersionsDto = {
                ids: versionIds.map(id => +id)
            };

            return this.httpClient.delete<never>(url, { body: payload }).pipe(map(() => versionIds));
        } else {
            return this.apollo
                .mutate({
                    mutation: DELETE_VERSIONS_MUTATION,
                    variables: {
                        ids: versionIds,
                        creativesetId: creativeSetId
                    }
                })
                .pipe(
                    map(result => {
                        if (!result.data?.deleteVersions) {
                            throw new Error('Could not deserialize DeleteVersions response.');
                        }
                        return result.data.deleteVersions.ids;
                    })
                );
        }
    }

    updateVersionInfo(creativesetId: string, version: IVersion): Observable<IVersion> {
        const url = `${this.sapiOrigin}/creative-sets/${creativesetId}/versions/${version.id}`;
        const creativeset = this.creativesetDataService.creativeset;

        const creativesToUpdate = creativeset.creatives
            .filter(creative => creative.version.id === version.id)
            .map(creative => ({
                id: creative.id,
                checksum: creativeToChecksum(creative, version, creativeset.stateId)
            }));

        const requestBody = {
            version: {
                name: version.name,
                localizationId: version.localization.id,
                targetUrl: version.targetUrl
            },
            creatives: creativesToUpdate
        };

        return this.httpClient.put<VersionInfoDto>(url, requestBody).pipe(
            map(response => ({
                ...version,
                id: response.id?.toString() ?? version.id,
                name: response.name ?? version.name,
                localization: {
                    id: response.localizationId ?? version.localization.id
                },
                targetUrl: response.targetUrl ?? version.targetUrl
            }))
        );
    }

    setDefaultVersion(creativesetId: string, version: IVersion): Observable<IVersion> {
        const versionId = version.id;

        return this.apollo
            .mutate({
                mutation: SET_DEFAULT_VERSION_MUTATION,
                variables: {
                    creativesetId: creativesetId,
                    versionId
                }
            })
            .pipe(
                map(result => {
                    if (!result.data?.setDefaultVersion) {
                        throw new Error('Could not deserialize SetDefaultVersion response.');
                    }
                    return deserializeVersion(result.data.setDefaultVersion);
                })
            );
    }
}
