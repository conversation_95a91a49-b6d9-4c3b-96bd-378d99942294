import { TestBed } from '@angular/core/testing';
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { serializeVersionProperties } from '@creative/serialization/versions/version-serializer';
import { convertCreativesetToDto, deserializeCreative } from '@data/deserialization/creativeset';
import { deserializeVersions } from '@data/deserialization/version';
import { createMockCreativeSize } from '@mocks/creative/creative-size.mock';
import { createMockCreative, createMockCreativeDTO } from '@mocks/creative/creative.mock';
import { createCreativesetMock } from '@mocks/creativeset.mock';
import { createDesignMock } from '@mocks/design.mock';
import { createVersionDtoMock, createVersionMock } from '@mocks/version.mock';
import { BrandService, CreativesetDataService } from '@studio/common';
import { Apollo } from 'apollo-angular';
import { of } from 'rxjs';
import { VersionsDataService } from './versions.data.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { environment } from '@app/environments/environment.test';

describe('VersionsDataService', () => {
    let versionsDataService: VersionsDataService;
    let apollo: Apollo;

    const apolloMock = {
        mutate: jest.fn()
    } as unknown as Apollo;

    const mockVersions = [
        createVersionMock({ id: '1', name: 'en' }),
        createVersionMock({ id: '2', name: 'se' })
    ];

    const mockSizes = [createMockCreativeSize({ id: '1' }), createMockCreativeSize({ id: '2' })];

    const mockDesigns = [createDesignMock({ id: '1' }), createDesignMock({ id: '2' })];

    const mockCreatives = [
        createMockCreative(false, {
            id: '1',
            size: mockSizes[0],
            design: mockDesigns[0],
            version: mockVersions[0]
        }),
        createMockCreative(false, {
            id: '2',
            size: mockSizes[1],
            design: mockDesigns[1],
            version: mockVersions[1]
        })
    ];

    const mockCreativeSet = createCreativesetMock({
        id: 'creativeset-id',
        creatives: mockCreatives,
        sizes: mockSizes,
        designs: mockDesigns,
        versions: mockVersions
    });

    const mockCreativesetDataService = {
        creativeset: mockCreativeSet
    } as unknown as CreativesetDataService;

    const environmentServiceMock: Partial<EnvironmentService> = {
        origins: environment.origins
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                VersionsDataService,
                provideHttpClient(),
                provideHttpClientTesting(),
                { provide: Apollo, useValue: apolloMock },
                { provide: CreativesetDataService, useValue: mockCreativesetDataService },
                { provide: EnvironmentService, useValue: environmentServiceMock },
                {
                    provide: BrandService,
                    useValue: {
                        brandId$: of('1'),
                        accountSlug$: of()
                    }
                }
            ]
        });

        versionsDataService = TestBed.inject(VersionsDataService);
        apollo = TestBed.inject(Apollo);
    });

    describe('createVersions', () => {
        const creativesetId = mockCreativeSet.id;
        const newVersions = [
            createVersionMock({ id: '1', name: 'en' }),
            createVersionMock({ id: '2', name: 'se' })
        ];

        const versionsDto = [
            createVersionDtoMock({ id: '1', name: 'en' }),
            createVersionDtoMock({ id: '2', name: 'se' })
        ];

        const creativesDto = [
            createMockCreativeDTO({
                id: '1',
                version: { id: mockVersions[0].id },
                design: { id: mockDesigns[0].id },
                size: { id: mockSizes[0].id! }
            }),
            createMockCreativeDTO({
                id: '2',
                version: { id: mockVersions[1].id },
                design: { id: mockDesigns[1].id },
                size: { id: mockSizes[1].id! }
            })
        ];

        it('should create versions', done => {
            const mockMutationResponse = {
                data: {
                    createVersions: {
                        versions: versionsDto,
                        creatives: creativesDto
                    }
                }
            };

            apollo.mutate = jest.fn().mockReturnValue(of(mockMutationResponse));

            const expectedVersions = deserializeVersions(versionsDto);
            const expectedCreatives = creativesDto.map(creative =>
                deserializeCreative(
                    convertCreativesetToDto(mockCreativeSet),
                    creative,
                    mockCreativeSet.designs
                )
            );

            const result$ = versionsDataService.createVersions(creativesetId, newVersions);

            result$.subscribe(result => {
                expect(result).toEqual({ versions: expectedVersions, creatives: expectedCreatives });

                expect(apollo.mutate).toHaveBeenCalledWith({
                    mutation: expect.anything(),
                    variables: {
                        versions: newVersions.map(version => ({
                            name: version.name,
                            localizationId: version.localization.id,
                            targetUrl: version.targetUrl,
                            properties: serializeVersionProperties(version.properties, false)
                        })),
                        creativesetId
                    }
                });
                done();
            });
        });

        it('should throw error when mutation response fails', done => {
            const mockMutationResponse = {
                data: {}
            };

            apollo.mutate = jest.fn().mockReturnValue(of(mockMutationResponse));

            const result$ = versionsDataService.createVersions(creativesetId, newVersions);

            result$.subscribe({
                next: () => {
                    fail('Expected an error, but the Observable emitted a value');
                },
                error: err => {
                    expect(err).toBeDefined();
                    expect(err.message).toBe('Could not deserialize CreateVersion response.');
                    done();
                }
            });
        });
    });

    describe('updateVersionInfo', () => {
        it('should send version info with creative checksums and transform REST response', done => {
            const mockVersion = createVersionMock({ id: '1' });
            const mockRestResponse = {
                id: 123,
                name: 'Updated Name',
                localizationId: 'updated-id',
                targetUrl: 'https://updated.com'
            };

            const httpClientMock = TestBed.inject(HttpClient);
            const putSpy = jest.spyOn(httpClientMock, 'put').mockReturnValue(of(mockRestResponse));

            versionsDataService.updateVersionInfo('creativeset-id', mockVersion).subscribe(result => {
                expect(result).toEqual({
                    ...mockVersion,
                    id: '123',
                    name: 'Updated Name',
                    localization: { id: 'updated-id' },
                    targetUrl: 'https://updated.com'
                });

                expect(putSpy).toHaveBeenCalledWith(
                    expect.stringContaining('/creative-sets/creativeset-id/versions/1'),
                    {
                        version: {
                            name: mockVersion.name,
                            localizationId: mockVersion.localization.id,
                            targetUrl: mockVersion.targetUrl
                        },
                        creatives: [
                            {
                                id: '1',
                                checksum: expect.any(String)
                            }
                        ]
                    }
                );

                done();
            });
        });
    });
});
