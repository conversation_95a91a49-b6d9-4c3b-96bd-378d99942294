import { IAppEnvironment } from '@domain/environment';
import { build } from './build-info';

export const environment: IAppEnvironment = {
    build,
    stage: 'production',
    production: true,
    sandbox: false,
    auth0: {
        clientId: '75jBojTVqFK6NKpU7W5fW9XHdz1TYOml',
        domain: 'https://login.bannerflow.com'
    },
    appInsights: {
        instrumentationKey: '805f97b4-921e-4d2c-ad99-1382c5cc0261',
        samplingPercentage: 33,
        enabled: true
    },
    newrelic: {
        enabled: true,
        accountId: '4122654',
        trustKey: '4122654',
        agentId: '*********',
        licenseKey: 'NRJS-bd28b6acdc31b77b97c',
        applicationId: '*********'
    },
    featureFlags: {
        enabled: true,
        url: 'https://features.bannerflow.com/api/frontend',
        clientKey: '*:production.038d7c473a3dd0900621771775d057f26de4a30d30c7f0f4153cddaa'
    },
    signalR: {
        enabled: true,
        url: 'https://api.bannerflow.com/studio/hub'
    },
    nsSignalR: {
        enabled: true,
        url: 'https://api.bannerflow.com/notification-service/hub'
    },
    intercomId: 'vc7jxmzv',
    gtmId: 'GTM-W5CTW5Z',
    origins: {
        acg: 'https://api.bannerflow.com/acg',
        accountAccessService: 'https://api.bannerflow.com/account-access',
        analyticsFrontendUrl: 'https://analytics.bannerflow.com',
        b2: 'https://b2.bannerflow.com',
        bannerflowLibrary: 'https://api.bannerflow.com/bannerflow-library',
        bannerlingo: 'https://api.bannerflow.com/bls',
        bfc: 'https://home.bannerflow.com',
        campaignManager: 'https://cm.bannerflow.com',
        campaignService: 'https://api.bannerflow.com/cs',
        commentService: 'https://api.bannerflow.com/comment-service',
        cps: 'https://api.bannerflow.com/preview',
        designApi: 'https://api.bannerflow.com/design-api/api',
        feedStorage: 'https://c.bannerflow.net/sfeeds',
        fontManager: 'https://fontmanager.bannerflow.com',
        fontManagerApi: 'https://api.bannerflow.com/font-manager',
        fontService: 'https://c.bannerflow.net/fs',
        fontStorage: 'https://fonts.bannerflow.net',
        genAiImage: 'https://api.bannerflow.com/gen-ai-image-service',
        imageOptimizer: 'https://c.bannerflow.net/io',
        listService: 'https://api.bannerflow.com/list-service',
        notificationService: 'https://api.bannerflow.com/notification-service/api',
        sapi: 'https://api.bannerflow.com/studio/api',
        socialCampaignManager: 'https://scm.bannerflow.com',
        socialCampaignService: 'https://scm.bannerflow.com',
        studioBlobStorage: 'https://c.bannerflow.net',
        videoStorage: 'https://c.bannerflow.net',
        unleash: 'https://features.bannerflow.com'
    },
    commentServiceCreativeToken: '3b15c66f-e3f8-4f12-b2af-3d5a27222dfb',
    commentServiceCreativesetToken: '4d215810-4945-4ce2-8df0-06d3b13c06fe'
};
