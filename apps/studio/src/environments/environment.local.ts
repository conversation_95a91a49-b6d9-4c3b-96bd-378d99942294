import { IAppEnvironment } from '@domain/environment';
import { build } from './build-info';

export const localEnvironment: IAppEnvironment = {
    build,
    stage: 'local',
    production: false,
    sandbox: false,
    auth0: {
        clientId: 'NvkuUSIxJFUhfbGz8oRrQypPMueOnxd7',
        domain: 'https://local-login.bannerflow.com'
    },
    newrelic: {
        enabled: false,
        accountId: '',
        trustKey: '',
        agentId: '',
        licenseKey: '',
        applicationId: ''
    },
    appInsights: {
        instrumentationKey: '<no-guid>',
        samplingPercentage: 100,
        enabled: false
    },
    signalR: {
        enabled: true,
        url: 'https://sapi.bannerflow.local/hub'
    },
    nsSignalR: {
        enabled: false,
        url: ''
    },
    featureFlags: {
        enabled: true,
        url: 'https://features.bannerflow.com/api/frontend',
        clientKey: '*:development.038021c6beec5378f85ecb597b4f7276342114eb4773d99605d82017'
    },
    intercomId: 'vc7jxmzv',
    origins: {
        acg: 'https://api.bannerflow.local/acg',
        accountAccessService: 'https://api.bannerflow.local/account-access',
        analyticsFrontendUrl: 'https://localhost:5007',
        b2: 'https://b2.bannerflow.local',
        bannerflowLibrary: 'https://api.bannerflow.local/bannerflow-library',
        bannerlingo: 'https://api.bannerflow.local/bls',
        bfc: 'https://home.bannerflow.local',
        campaignManager: 'http://localhost:4344',
        campaignService: 'https://api.bannerflow.local/cs',
        commentService: 'https://api.bannerflow.local/comment-service',
        cps: 'https://api.bannerflow.local/preview',
        designApi: 'https://api.bannerflow.local/design-api/api',
        feedStorage: 'https://blob-storage.bannerflow.local/devstoreaccount1/sfeeds',
        fontManager: 'https://fm.bannerflow.local',
        fontManagerApi: 'https://api.bannerflow.local/font-manager',
        fontService: 'https://fs.bannerflow.local',
        fontStorage: 'https://blob-storage.bannerflow.local',
        genAiImage: 'https://api.bannerflow.local/gen-ai-image-service',
        imageOptimizer: 'https://io.bannerflow.local',
        listService: 'https://api.bannerflow.local/list-service',
        notificationService: 'https://api.bannerflow.local/notification-service/api',
        sapi: 'https://sapi.bannerflow.local/api',
        socialCampaignManager: 'http://localhost:7788',
        socialCampaignService: 'https://sandbox-scm.bannerflow.com',
        studioBlobStorage: 'https://blob-storage.bannerflow.local',
        videoStorage: 'https://blob-storage.bannerflow.local',
        unleash: 'https://features.bannerflow.local'
    },
    commentServiceCreativeToken: '5edbd9c5-0d3f-444e-8e09-b2b528b082e6',
    commentServiceCreativesetToken: 'b821e067-625c-4db5-bee2-61a695398fab'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
