import { IAppEnvironment } from '@domain/environment';
import { build } from './build-info';

export const environment: IAppEnvironment = {
    build,
    stage: 'sandbox',
    production: true,
    sandbox: true,
    auth0: {
        clientId: '********************************',
        domain: 'https://sandbox-login.bannerflow.com'
    },
    newrelic: {
        enabled: true,
        accountId: '4232543',
        trustKey: '4122654',
        agentId: '*********',
        licenseKey: 'NRJS-d0b27a9b958bc4b281c',
        applicationId: '*********'
    },
    appInsights: {
        instrumentationKey: 'a88b7aba-b08e-4416-891e-a3ee95c1977e',
        samplingPercentage: 50,
        enabled: true
    },
    featureFlags: {
        enabled: true,
        clientKey: '*:development.038021c6beec5378f85ecb597b4f7276342114eb4773d99605d82017',
        url: 'https://features.bannerflow.com/api/frontend'
    },
    signalR: {
        enabled: true,
        url: 'https://sandbox-api.bannerflow.com/studio/hub'
    },
    nsSignalR: {
        enabled: true,
        url: 'https://sandbox-api.bannerflow.com/notification-service/hub'
    },
    intercomId: 'vc7jxmzv',
    gtmId: 'GTM-M2FZ5ZZ',
    origins: {
        acg: 'https://sandbox-api.bannerflow.com/acg',
        accountAccessService: 'https://sandbox-api.bannerflow.com/account-access',
        analyticsFrontendUrl: 'https://sandbox-analytics.bannerflow.com',
        b2: 'https://sandbox-b2.bannerflow.com',
        bannerflowLibrary: 'https://sandbox-api.bannerflow.com/bannerflow-library',
        bannerlingo: 'https://sandbox-api.bannerflow.com/bls',
        bfc: 'https://sandbox-home.bannerflow.com',
        campaignManager: 'https://sandbox-cm.bannerflow.com',
        campaignService: 'https://sandbox-api.bannerflow.com/cs',
        commentService: 'https://sandbox-api.bannerflow.com/comment-service',
        cps: 'https://sandbox-api.bannerflow.com/preview',
        designApi: 'https://sandbox-api.bannerflow.com/design-api/api',
        feedStorage: 'https://c.sandbox-bannerflow.net/sfeeds',
        fontManager: 'https://sandbox-fontmanager.bannerflow.com',
        fontManagerApi: 'https://sandbox-api.bannerflow.com/font-manager',
        fontService: 'https://c.sandbox-bannerflow.net/fs',
        fontStorage: 'https://fonts.sandbox-bannerflow.net',
        genAiImage: 'https://sandbox-api.bannerflow.com/gen-ai-image-service',
        imageOptimizer: 'https://c.sandbox-bannerflow.net/io',
        listService: 'https://sandbox-api.bannerflow.com/list-service',
        notificationService: 'https://sandbox-api.bannerflow.com/notification-service/api',
        sapi: 'https://sandbox-api.bannerflow.com/studio/api',
        socialCampaignManager: 'https://sandbox-scm.bannerflow.com',
        socialCampaignService: 'https://sandbox-scm.bannerflow.com',
        studioBlobStorage: 'https://c.sandbox-bannerflow.net',
        videoStorage: 'https://c.sandbox-bannerflow.net',
        unleash: 'https://features.bannerflow.com' // this is the same as prod
    },
    commentServiceCreativeToken: '71be6767-4c5d-4af5-b1bb-d1ff0828fb95',
    commentServiceCreativesetToken: 'b821e067-625c-4db5-bee2-61a695398fab'
};
