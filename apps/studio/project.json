{"name": "studio", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "studio", "sourceRoot": "apps/studio/src", "tags": ["type:app", "scope:client", "scope:studio"], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "dependsOn": [{"target": "build-widget", "projects": "creative"}], "options": {"outputPath": "dist/apps/studio/", "index": "apps/studio/src/index.html", "main": "apps/studio/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/studio/tsconfig.app.json", "assets": ["apps/studio/src/favicon.ico", "apps/studio/src/assets", {"glob": "**/*", "input": "node_modules/monaco-editor/min", "output": "assets/monaco-editor/min"}, {"glob": "**/widget.js", "input": "dist/libs/creative", "output": "dist/libs/creative"}], "styles": ["node_modules/@bannerflow/ui/index.scss", "apps/studio/src/styles/base.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["apps/studio/src/styles", "node_modules/@bannerflow"]}, "baseHref": "/", "allowedCommonJsDependencies": ["graphql-tag", "zen-observable", "bowser", "base64-js", "gifuct-js", "dompurify", "moment", "deepmerge", "qr-code-styling", "ajv", "ajv-keywords", "ajv-formats"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "4mb"}, {"type": "bundle", "name": "main", "maximumWarning": "3mb", "maximumError": "3.8mb"}, {"type": "bundle", "name": "pages-design-view-design-view-module", "maximumWarning": "1.5mb", "maximumError": "2mb"}, {"type": "bundle", "name": "pages-manage-view-manage-view-module", "maximumWarning": "1mb", "maximumError": "1.6mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "100kb"}], "fileReplacements": [{"replace": "apps/studio/src/environments/environment.ts", "with": "apps/studio/src/environments/environment.prod.ts"}, {"replace": "apps/studio/src/app/pages/design-view/media-library/environments/effects.ts", "with": "apps/studio/src/app/pages/design-view/media-library/environments/effects.prod.ts"}], "outputHashing": "all", "sourceMap": {"hidden": false, "styles": true, "scripts": true}}, "sandbox": {"budgets": [{"type": "initial", "maximumWarning": "11mb", "maximumError": "12mb"}, {"type": "bundle", "name": "main", "maximumWarning": "3mb", "maximumError": "3.5mb"}, {"type": "bundle", "name": "vendor", "maximumWarning": "8mb", "maximumError": "9mb"}, {"type": "bundle", "name": "pages-design-view-design-view-module", "maximumWarning": "5mb", "maximumError": "6mb"}, {"type": "bundle", "name": "pages-manage-view-manage-view-module", "maximumWarning": "5mb", "maximumError": "6mb"}], "fileReplacements": [{"replace": "apps/studio/src/environments/environment.ts", "with": "apps/studio/src/environments/environment.sandbox.ts"}, {"replace": "apps/studio/src/app/pages/design-view/media-library/environments/effects.ts", "with": "apps/studio/src/app/pages/design-view/media-library/environments/effects.sandbox.ts"}], "optimization": false, "outputHashing": "all", "sourceMap": true, "vendorChunk": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "assets": ["apps/studio/src/favicon.ico", "apps/studio/src/assets", {"glob": "**/*", "input": "node_modules/monaco-editor/min", "output": "assets/monaco-editor/min"}, {"glob": "**/widget.js?(.map)", "input": "dist/libs/creative", "output": "dist/libs/creative"}]}, "test": {"assets": ["apps/studio/src/favicon.ico", "apps/studio/src/assets", "apps/studio/src/test-assets", {"glob": "**/*", "input": "node_modules/monaco-editor/min", "output": "assets/monaco-editor/min"}, {"glob": "**/widget.js?(.map)", "input": "dist/libs/creative", "output": "dist/libs/creative"}], "outputPath": "dist/apps/studio/test/", "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "apps/studio/src/environments/environment.ts", "with": "apps/studio/src/environments/environment.test.ts"}]}, "remote": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "apps/studio/src/environments/environment.ts", "with": "apps/studio/src/environments/environment.remote.ts"}, {"replace": "apps/studio/src/app/pages/design-view/media-library/environments/effects.ts", "with": "apps/studio/src/app/pages/design-view/media-library/environments/effects.sandbox.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "dependsOn": [{"target": "build-widget", "projects": "creative"}], "configurations": {"production": {"buildTarget": "studio:build:production"}, "development": {"buildTarget": "studio:build:development", "host": "studio.bannerflow.local", "port": 3000, "allowedHosts": ["studio.bannerflow.local", "host.docker.internal"]}, "remote": {"buildTarget": "studio:build:remote", "disableHostCheck": true, "port": 3000, "host": "remote-studio.bannerflow.com"}, "test": {"buildTarget": "studio:build:test", "port": 4269}}, "defaultConfiguration": "development", "continuous": true}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "studio:build"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/studio/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "dependsOn": [{"target": "build-widget", "projects": "creative"}], "options": {"buildTarget": "studio:build:test", "staticFilePath": "dist/apps/studio/test", "port": 4269, "spa": true}}, "build-ci": {"command": "node --max-old-space-size=4096 ./node_modules/nx/bin/nx build studio --configuration=production"}, "docker-build": {"dependsOn": [{"projects": "self", "target": "build", "params": "forward"}], "command": "docker build -f apps/studio/Dockerfile . -t bannerflow.azurecr.io/studio/studio-client:local-nginx"}}}