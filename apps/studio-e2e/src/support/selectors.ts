// ! This list should be ordered alphabetically

export enum ActionPropertiesSelectors {
    ActionProperties = 'action-properties',
    AddAction = '#add-action-header .action',
    ActionInteractionOption = '#interaction-action-click'
}

export enum AnimationControlSelectors {
    Replay = '#animation-controls-replay',
    StepBack = '#animation-controls-step-back',
    PlayPause = '#animation-controls-play-pause',
    StepForward = '#animation-controls-step-forward',
    ViewPreloadImage = '#animation-controls-view-preload-image'
}

export enum ApprovalStatusSelectors {
    SelectApproved = '#set-status-approved',
    SelectForReview = '#set-status-forreview',
    SelectInProggress = '#set-status-inprogress',
    SelectNoStatus = '#set-status-nostatus',
    SelectNotApproved = '#set-status-notapproved',
    SetStatusDropdownItem = '#interaction-setstatus',
    StatusList = '.status-list',
    StatusListItems = '.status-list .list-item',
    StatusListResetBtn = '[data-test-id="status-filter-reset"]',
    StatusSelector = '.approval-status-wrapper',
    StatusIconApproved = '.status-icon_approved',
    StatusIconNotApproved = '.status-icon_not-approved'
}

export enum CanvasSelectors {
    Canvas = '#canvas',
    CanvasLayer = 'canvas-layer',
    Creative = '#canvas .creative div[data-creative]',
    Element = '#canvas .creative div[data-creative] > div',
    Workspace = '#workspace',
    ZoomInput = '#zoom-input-input'
}

export enum ColorSelectors {
    ColorButton = '[data-test-id="color-button"]',
    ColorButtonFill = '.button__color',
    ColorPalette = '.palette',
    ColorPaletteElement = '.palette .color-container',
    ColorPalettesMenu = '[data-test-id="palettes-menu"]',
    ColorPalettesMenuItem = '[data-test-id="palettes-menu-item"]',
    ColorPicker = '[data-test-id="color-picker"]',
    ColorPickerAlphaCanvas = '.picker > .alpha canvas',
    ColorPickerAlphaInput = '.value .alpha input',
    ColorPickerCanvas = '.picker > .main canvas',
    ColorPickerHueCanvas = '.picker > .hue canvas',
    ColorPickerGizmo = '.picker .selector',
    ColorPickerInitialized = '[data-test-id="color-picker"] [data-initialized="true"]',
    ColorPickerInput = '.value .color input',
    ColorTypes = '.color-types',
    TypeSwitchGradient = '[data-test-id="color-type-switch-linear-gradient"]',
    TypeSwitchSolid = '[data-test-id="color-type-switch-solid"]'
}

export enum ContextDropdownMenuSelectors {
    AddToCampaign = '#add-to-campaign-context-menu',
    Duplicate = '#interaction-duplicate',
    DuplicateCreatives = '[data-test-id="duplicate-creatives-item"]',
    DuplicateCreativesToExisting = '[data-test-id="duplicate-creative-to-existing"]',
    DuplicateCreativesToNew = '[data-test-id="duplicate-creative-to-new"]',
    DuplicateCreativesHere = '[data-test-id="duplicate-creative-here"]',
    Kebab = '[data-test-id="creative-item-menu-open"]',
    Filter = '#interaction-ctx-menu-filter',
    SetStatus = '#interaction-setstatus',
    Export = '#interaction-export'
}

export enum DialogSelectors {
    Dialog = 'ui-dialog-master',
    Close = '#interaction-close-dialog',
    ButtonPrimary = 'ui-dialog-master ui-button.primary',
    ButtonDefault = 'ui-dialog-master ui-button.default'
}

export enum DuplicateCreativesSelectors {
    Breadcrumbs = '.breadcrumbs',
    LoadMore = 'duplicate-creative-dialog a.load',
    SearchInput = '.search-wrapper ui-input input'
}

export enum EditElementSelectors {
    EditElementView = '[data-test-id="element-edit-view"]',
    NameInput = '#widget-name', // rename?
    SaveButton = '#widget-save-btn',
    WidgetProperties = '.widget-properties',
    PropertySection = '.widget-properties .property-details'
}

export enum EditorTopbarSelectors {
    EditorTopbar = 'editor-topbar',
    ExitButton = '[data-test-id="exit-editor-button"]',
    SaveButton = '[data-test-id="save-design-button"]',
    UploadButton = '[data-test-id="upload-button"]',
    HamburgerMenu = '#interaction-hamburger-menu'
}

export enum ElementPopoverMenuSelectors {
    AddToBrandLibrary = '[data-cy="add-to-brandlibrary"]',
    DetachCreativeItem = '[data-test-id="detach-creative-item"]',
    DuplicateCreative = '[data-test-id="duplicate-creative-item"]',
    FindElementInBrandLibrary = '[data-cy="find-in-brandlibrary"]',
    UpdateFromWidgetLibrary = '[data-test-id="update-from-library"]'
}

export enum ExportDialogSelectors {
    GIFLoopsSettings = '#interaction-loops-setting',
    GIFLoopsCustomInput = '#interaction-custom-loops-input',
    ExportButtonGIF = '#export-gif'
}

export enum ExportDropdownMenuSelectors {
    MP4Video = '#interaction-export-video',
    GIF = '#interaction-export-gif'
}

export enum ElementSelectors {
    Buttons = '[data-test-id="element-kind-button"]',
    Ellipses = '[data-test-id="element-kind-ellipse"]',
    Images = '[data-test-id="element-kind-image"]',
    Rectangles = '[data-test-id="element-kind-rectangle"]',
    Texts = '[data-test-id="element-kind-text"]',
    Videos = '[data-test-id="element-kind-video"]',
    Widgets = '[data-test-id="element-kind-widget"]'
}

export enum FullScreenSelectors {
    FullScreenArrowLeft = '.full-screen-footer ui-svg-icon[icon="arrow-left"]',
    FullScreenArrowRight = '.full-screen-footer ui-svg-icon[icon="arrow-right"]',
    FullScreenBody = '.full-screen-body',
    FullScreenCloseButton = '.full-screen ui-svg-icon[icon="close"]',
    FullScreenCreative = '.full-screen studio-creative',
    FullScreenDialog = '.full-screen',
    FullScreenFooter = '.full-screen-footer',
    FullScreenHeader = '.full-screen-header',
    FullScreenSize = '.full-screen .size',
    FullScreenTargetURLDialog = 'target-url-dialog',
    FullScreenToggle = '.full-screen ui-toggle-switch .switch',
    FullScreenVersionName = '.full-screen .select-label'
}

export enum HamburgerMenuSelectors {
    AddToCampaign = '#add-to-campaign-hamburguer-menu',
    Campaigns = '#create-new-campaign',
    Export = '#interacton-export-creatives',
    ShareCreativeset = '#share-creative-set',
    Edit = '[data-test-id="edit-section-option"]',
    Undo = '[data-test-id="undo-option"]',
    Redo = '[data-test-id="redo-option"]'
}

export enum IconSelectors {
    ArrowDown = '.icon-arrow-down',
    ArrowLeft = '.icon-arrow-left',
    DirectionLeft = '.icon-direction-left',
    Edit = '.icon-edit',
    Help = '.icon-question-mark',
    Kebab = '.icon-kebab',
    VersionPickerArrowUp = 'version-picker .icon-arrow-up',
    VersionPickerArrowDown = 'version-picker .icon-arrow-down',
    VisiblityHidden = '.icon-visibility-hidden',
    VisiblityVisible = '.icon-visibility-visible'
}

export enum ManageViewSelectors {
    Controls = '.sidebar .controls',
    Creative = 'studio-creative',
    CreativeList = 'creative-list',
    CreativeListItem = 'creative-list-item',
    CreativeListItemCheckbox = 'creative-list-item ui-svg-icon[icon="checkbox-false"]',
    CreativeListItems = 'creative-list > .items > creative-list-item',
    CreativeListItemsInGroups = '[data-test-id="creative-list-item"]',
    CreativeListItemSizeNames = '.creative-size-name',
    CreativeListGroupHeader = 'creative-list-groupheader',
    CreativeListItemWeightCalculation = '[data-test-id="mv-calculate-weight"]',
    CreativeMenuEditButton = '#menu-edit-design',
    CreateNewCampaignButton = '#interaction-create-new-campaign-button',
    CreativeSetName = '.creativeset-name',
    MVPreview = '#mv-preview',
    FullPreviewCreative = '.full-preview #fullPreviewCreative',
    HamburgerMenu = '#interaction-hamburger-menu',
    HorizontalSizesList = '[data-test-id="size-list-horizontal"]',
    HorizontalSizesListItems = '[data-test-id="size-list-horizontal-item"]',
    ManageTopBarCenter = 'studio-topbar .center',
    Sidebar = '.sidebar',
    SizeSelector = '[data-test-id="size-selector"]',
    PreviewCreative = 'size-add-preview studio-creative',
    TargetUrl = '.target-url',
    TranslatePanelInput = '.textarea',
    VersionDialog = 'version-dialog',
    VersionDialogAdd = '#add-version-button',
    VersionDialogClose = '#interaction-cancel-version-dialog',
    VersionDialogItemSettings = '.settings-icon',
    VersionFlag = 'version-flag',
    VersionManageLanguageCell = '[data-test-id="cell-language"]',
    VersionManageLanguageCellCampaign = '[data-test-id="cell-language-campaign"]',
    VersionSelect = '#select-version',
    VersionPicker = '[data-test-id="version-picker"]',
    VersionPickerAdd = '#interaction-add-version',
    VersionPickerAllVersions = '#interaction-all-versions',
    VersionPickerDropdown = '.version-dropdown',
    VersionPickerItem = '.version-item',
    VersionPickerList = '.version-list',
    VersionPickerSettings = '#interaction-version-settings',
    ViewCampaignsButton = '#interaction-topbar-open-campaigns',
    VirtualScrollVieport = '[data-test-id="virtual-scroll-vieport"]'
}

export enum MediaLibrarySelectors {
    AddButton = 'media-library .add-icon',
    ComponentTreeNodes = 'cdk-tree-node',
    ComponentTrees = 'cdk-tree',
    LibraryBackToFeeds = '#media-library-back-to-feeds',
    LibraryElementAddToCanvas = '.ui-dropdown-item[data-test-id="add-to-canvas"]',
    LibraryElementControlKebab = '.library-element-row__control[icon="kebab"]',
    LibraryElementControls = '.library-element-row__control',
    LibraryElementEdit = '.ui-dropdown-item[data-test-id="edit"]',
    LibraryElementDelete = '.ui-dropdown-item[data-test-id="delete"]',
    LibraryElementUpdateWidget = '.ui-dropdown-item[data-test-id="update-widget"]',
    LibraryElementInput = 'library-element ui-input input',
    LibraryElementName = '.library-element-row__name',
    LibraryElements = 'library-element',
    LibraryElementRow = '.library-element-row',
    LibraryEffectElementImportOverlay = '.library-element__import-overlay',
    LibraryEmpty = '.asset-list-empty',
    LibraryFeedField = 'feed-field',
    LibraryFolderNameInput = 'library-folder ui-input',
    LibraryFolderOpen = '#media-library-folder-open-parent',
    LibraryFolders = 'library-folder',
    MediaLibrary = 'media-library',
    SearchInput = '[data-cy="brandlibrary-search"] input',
    ReplaceMediaAsset = '[data-test-id="replace-asset"]'
}

export enum NotificationsSelectors {
    NotificationsWrapper = 'ui-notification',
    NotificationsClose = 'ui-notification ui-svg-icon .icon-close'
}

export enum PopoverSelectors {
    FontPickerFontManager = 'toolbar-button[icon="settings"]',
    Menu = 'ui-popover-master',
    MenuFeedFieldSelect = 'ui-popover-master [data-test-id="feed-field"]',
    MenuFeedStepSizeInput = 'ui-popover-master [data-test-id="feed-step-size"] input',
    MenuFeedStepStartInput = 'ui-popover-master [data-test-id="feed-step-start"] input',
    MenuItemFlipHorizontally = 'ui-popover-master ui-dropdown-item ui-svg-icon[icon=flip-h]',
    MenuItemFlipVertically = 'ui-popover-master ui-dropdown-item ui-svg-icon[icon=flip-v]',
    MenuItemGroupToggle = '#menu-group-toggle',
    MenuItemUngroupToggle = '#menu-ungroup-toggle',
    MenuItems = 'ui-popover-master ui-dropdown-item',
    FontValidationMenuItem = 'ui-popover-master [data-test-id="toggle-font-validation"]',
    VideoMenuItem = 'ui-popover-master #clear-video-validation',
    MenuSelectOption = 'ui-popover-master ui-option',
    SelectAll = 'ui-popover-master [data-cy="select-all"]',
    DeleteSelection = 'ui-popover-master [data-cy="delete-element"]',
    CopySelection = 'ui-popover-master [data-cy="copy-selection"]',
    PasteSelection = 'ui-popover-master [data-cy="paste-selection"]',
    ArrangeOptions = 'ui-popover-master [data-cy="arrange-options"]',
    ArrangeForward = 'ui-popover-master [data-cy="arrange-forward"]',
    ArrangeFront = 'ui-popover-master [data-cy="arrange-front"]',
    ArrangeBackward = 'ui-popover-master [data-cy="arrange-backward"]',
    ArrangeBack = 'ui-popover-master [data-cy="arrange-back"]',
    MoveToGroup = 'ui-popover-master [data-cy="move-to-group"]',
    MoveToGroupTarget = 'ui-popover-master [data-cy="move-to-group-target"]',
    BrandLibraryOptions = 'ui-popover-master [data-cy="brand-library-options"]',
    ToggleElementVisibility = 'ui-popover-master [data-test-id="toggle-element-visibility"]',
    AddMasking = 'ui-popover-master [data-test-id="add-masking"]',
    RemoveMasking = 'ui-popover-master [data-test-id="remove-masking"]'
}

export enum PropertiesPanelSelectors {
    AnimationType = '#animation-type',
    AnimationEasing = '#animation-easing',
    AnimationDurationInput = '[data-test-id="animation-duration-input"] input',
    AnimationPlaceholder = '#animation-placeholder',
    AnimationUnitDegreeInput = '#animation-unit-degree-input',
    AnimationAngleLeft = '#angle-left',
    AnimationAngleRight = '#angle-right',
    AnimationAngleTop = '#angle-up',
    AnimationAngleDown = '#angle-down',
    AnimationUnitPxInput = '#animation-unit-px-input',
    AnimationInTypeSelect = 'animation-properties[type="in"] #animation-type',
    AnimationOutTypeSelect = 'animation-properties[type="out"] #animation-type',
    AnimationInRemoveButton = 'animation-properties[type="in"] #in-animation-remove-btn',
    AnimationOutRemoveButton = 'animation-properties[type="out"] #out-animation-remove-btn',
    AnimationPropertyHeightPercent = '.property-input:contains(%H) input',
    AnimationPropertyWidthPercent = '.property-input:contains(%W) input',
    AssetProperty = 'asset-property',
    AssetPicker = 'asset-picker',
    AssetPickerImageWrapper = 'asset-picker .image-wrapper',
    AssetPickerReplaceButton = 'asset-picker .ui-button',
    AssetPickerReplaceImageButton = 'asset-picker [data-test-id="replace-image-button"]',
    AssetPickerDropdown = 'ui-dropdown-item[data-test-id="add-from-brand-library"]',
    FilterSettingInput = '#default-property-filter .setting-row #filter-input-input',
    FontpickerDropdowns = 'font-picker .text-wrapper',
    FontFamilyDropdown = '#font-family',
    FontStyleDropdown = '#font-style',
    FontSizeInput = '#text-font-size-input-input',
    FeedFieldTextarea = '[data-test-id="Feed field"] textarea',
    HeavyVideoIcon = 'state-tabs ui-svg-icon .icon-video-heavy',
    HeightInput = '#height-input-input',
    ImageOptimizationToggle = '#interaction-image-optimization-toggle',
    ImageOptimizationInput = '[data-test-id="image-optimization-input"] input',
    MixedMultipleValuesPlaceholder = '.placeholder-text',
    OpacityInput = '#opacity-input',
    OpacitySlider = '#opacity-slider input[type="range"]',
    PositionXInput = '#x-input-input',
    PositionYInput = '#y-input-input',
    PropertyBackground = '#creative-property-fill',
    PropertyBorder = '#default-property-border',
    PropertyBorderColorButton = '#default-property-border color-button',
    PropertyBorderType = '#default-property-border #border-type',
    PropertyBorderWidth = '#default-property-border #borderThickness input',
    PropertyBoxShadow = '#default-property-shadow',
    PropertyBoxShadowRow = '#default-property-shadow .section-body > .setting-value',
    PropertyBoxShadowRemove = '#remove-shadow-button',
    PropertyBoxShadowColorButton = '#default-property-shadow color-button',
    PropertyCharacterSpacing = '#text-character-spacing-input-input',
    PropertyColorButtonMixed = 'color-button [icon="progress"]',
    PropertyFill = '#default-property-fill',
    PropertyFillAdd = '#add-fill-button',
    PropertyFillRemove = '#remove-fill-button',
    PropertyFillColorButton = '#default-property-fill color-button',
    PropertyRadius = '[data-test-id="layout-property-radius"]',
    PropertyFilterRemove = '#remove-filter-button',
    PropertyTextAlignHorizontalLeft = '#align-horizontal-left-button',
    PropertyTextAlignHorizontalCenter = '#align-horizontal-center-button',
    PropertyTextAlignHorizontalRight = '#align-horizontal-right-button',
    PropertyTextAlignVerticalTop = '#align-vertical-top-button',
    PropertyTextAlignVerticalMiddle = '#align-vertical-middle-button',
    PropertyTextAlignVerticalBottom = '#align-vertical-bottom-button',
    PropertyTextColor = '#text-property-color',
    PropertyTextLineHeight = '#text-line-height-input-input',
    PropertyTextShadow = '#text-property-shadow',
    PropertyTextShadowColorButton = '#text-property-shadow color-button',
    PropertyTextSection = 'text-properties > studio-ui-section',
    PropertyTextStrikethrough = '#strikethrough-button',
    PropertyTextUnderline = '#underline-button',
    PropertyTextUppercase = '#uppercase-button',
    RadiusInput = '#radius-input input',
    RadiusTypeSeparate = '#separate',
    RadiusTypeJoint = '#joint',
    RadiusInputTopLeft = '#radius-input-top-left input',
    RadiusInputTopRight = '#radius-input-top-right input',
    RadiusInputBottomRight = '#radius-input-bottom-right input',
    RadiusInputBottomLeft = '#radius-input-bottom-left input',
    RatioLock = '.ratio-lock',
    RotationPitchInput = '#rotationY-input-input',
    RotationRollInput = '#rotationX-input-input',
    RotationYawInput = '#rotationZ-input-input',
    SizeModeFill = 'ui-option#size-mode-fill',
    SelectActionType = '[data-test-id="action-select-action"]',
    SelectActionTarget = '[data-test-id="action-select-target"]',
    SizeModeDropdown = 'size-mode-options .size-mode-options',
    SizeModeFit = 'ui-option#size-mode-fit',
    SizeModeStretch = 'ui-option#size-mode-stretch',
    ScaleXInput = '#scale-x-input input',
    ScaleYInput = '#scale-y-input input',
    StateDelete = '[data-test-id="state-delete"]',
    TextMaxRowsInput = '#text-max-rows-input-input',
    TargetWidthInput = '#target-width-input input',
    TargetHeightInput = '#target-height-input input',
    UsePixelsToggle = '#interaction-use-pixels-toggle .switch',
    VideoSizeModeDropdown = 'video-properties .video-size-mode-options',
    VideoStartTimeInput = '[data-test-id="start-time"] input',
    VideoEndTimeInput = '[data-test-id="end-time"] input',
    WidgetProperties = 'widget-properties > #widget-properties',
    SelectWidgetInput = '[data-test-id="Test Select"]',
    ColorWidgetInput = '[data-test-id="Test Color"] .button__color',
    WidthInput = '#width-input-input'
}

export enum WidgetSelectors {
    Action = '#widget-properties .action',
    AddProperty = '.add-property .icon',
    BooleanIframeProperty = '.widget-test-boolean',
    ColorPalette = 'color-palette .color',
    DropdownItem = 'ui-dropdown-item',
    mediaLibraryDialogLibraryElement = 'media-library.inDialog library-element',
    mediaLibraryDialogButton = 'media-library.inDialog ui-button.primary',
    ElementFeaturesIconFeed = '.element-features [icon="feed"]',
    FeedBrowserListCell = 'feed-browser ui-list-cell',
    FeedBrowserButton = 'feed-browser ui-button.primary',
    InteractionBlocker = '.widget-interaction-blocker',
    KindWidget = '.kind-widget',
    NewWidget = '#new-widget',
    Name = '#widget-name',
    NumberIframeProperty = '.widget-test-number',
    Opacity = '[data-test-id="opacity"]',
    Option = 'ui-option',
    PropertySelect = '.property.type ui-select',
    Properties = '.widget-properties',
    PopoverOption = '.ui-popover ui-option',
    Radius = '[data-test-id="radius"]',
    SaveButton = '#widget-save-btn',
    TextIframeProperty = '.widget-test-text',
    TypeWidget = '.toolbar-item.type-widget',
    WidgetEditor = 'widget-editor'
}

export enum WidgetPropertiesSelectors {
    BooleanPropertyToggle = '[data-test-id="booleanProperty"] ui-toggle-switch',
    ColorProperty = '[data-test-id="colorProperty"]',
    FeedPropertyPicker = '[data-test-id="feedProperty"] feed-picker .feed-name',
    FontPropertySelect = '[data-test-id="fontProperty"] ui-select',
    ImageProperty = '[data-test-id="imageProperty"]',
    NumberPropertyInput = '[data-test-id="numberProperty"] input',
    ListPropertySelect = '[data-test-id="selectProperty"] ui-select',
    TextPropertyTextArea = '[data-test-id="textProperty"] textarea'
}

export enum PSDImportSelectors {
    Dropzone = 'ui-file-dropzone',
    FileInput = 'psd-file-input',
    GroupLayer = '.group-layer',
    ImportButton = '[data-test-id="import-psd"]',
    ImportTab = '[data-test-id="size-add-importpsd-tab-label"]',
    Layer = '.any-layer',
    LayerList = 'psd-list',
    Preview = 'psd-import-preview',
    SaveButton = 'save-psd-creative',
    Thumbnail = '[data-test-id="psd-thumbnail"]',
    VisibilityIcon = '[data-test-id="psd-visibility-icon"]',
    WarningIcon = '[data-test-id="psd-warning-icon"]'
}

export enum PSDImportFontFixSelectors {
    ClosePopover = '[data-test-id="close-font-fix-popover"]',
    PopoverWrapper = '[data-test-id="font-fix-popover"]',
    SearchFont = '[data-test-id="search-font"]',
    UseAlternative = '[data-test-id="use-alternative-font"]'
}

export enum ShadowSelectors {
    BlurInput = '[data-test-id="shadow-blur"] input',
    OffsetXInput = '[data-test-id="shadow-offsetX"] input',
    OffsetYInput = '[data-test-id="shadow-offsetY"] input',
    SettingBody = '[data-test-id="shadow-setting-body"]',
    SpreadInput = '[data-test-id="shadow-spread"] input'
}

export enum ShareCreativeset {
    SizesSelect = 'ui-select.sizes',
    SizesSelectOption = '.size-option'
}

export enum SizeFormatSelectors {
    AddDesignButton = '#add-design-button',
    AddSizeDialog = '[data-test-id="size-add-dialog"]',
    AddSizesButton = '#add-sizes-button',
    SizeInteractionList = '#interaction-sizes',
    SizeInteractionListHeader = '#interaction-sizes .header',
    SizeListResetBtn = '[data-test-id="size-filter-reset"]',
    SizeInteractionListItems = '#interaction-sizes [data-test-id="size-interaction-item"]',
    SizeList = '[data-test-id="size-list"]',
    SizeAddCollectionOverview = '[data-test-id="size-add-collection-overview"]',
    SizeListOverviewAction = '.overview__action',
    SizeListOverviewHeader = '.overview__header',
    SizeOption = '[data-test-id="size-add-dialog"] .size',
    SizeSectionBrand = '#size-list .is-brand',
    SizeSectionCustom = '#size-list .is-custom',
    SizeSectionSocial = '#size-list .is-social',
    SizeSelectionPreview = '[data-test-id="size-add-dialog"] .creative-preview',
    SizeSelectionPreviewChildren = '[data-test-id="size-add-dialog"] .creative-preview .creative-wrapper',
    SizeSelector = '[data-test-id="size-selector"]'
}

export enum SocialGuide {
    Section = '[data-test-id="social-guide-section"]',
    NetworkSelection = '[data-test-id="social-guide-network-selection"]',
    PlacementSelection = '[data-test-id="social-guide-placement-selection"]',
    ShowOverlay = '[data-test-id="social-guide-show-overlay"]',
    ShowGuidelines = '[data-test-id="social-guide-show-guidelines"]'
}

export enum StateSelectors {
    AddHoverState = '#add-hover-state',
    AddCustomState = '#add-custom-state',
    AddPressedState = '#add-pressed-state',
    AddState = 'state-tabs #add-state',
    DefaultTab = '#state-select-default',
    HoverTab = '#state-select-Hover',
    CustomTab = '[id^="state-select-custom-"]',
    StateTabs = 'state-tabs'
}

export enum TextSelectors {
    TextArea = '[data-test-id="rich-text-textarea"]',
    TextLine = '[data-test-id="rich-text-text-line"]',
    TextSpan = '[data-test-id="rich-text-span-text"]'
}

export enum TimelineSelectors {
    AnimationSuffix = '.animations-expanded .animate timeline-animation',
    AnimationToggleButton = '[data-test-id="toggle-animations-btn"]',
    DurationInput = '.duration-input #duration',
    Elements = 'studio-timeline timeline-element',
    ElementsLeftPanel = 'studio-timeline timeline-element .left-panel',
    ElementsNames = 'studio-timeline timeline-element input',
    GroupToggle = '[data-test-id="toggle-group"]',
    Groups = 'studio-timeline .group-node',
    ElementVisibilityToggle = '[data-test-id="element-toggle-visibility"]',
    Timeline = 'studio-timeline',
    Playhead = '[data-test-id="playhead"]',
    PlayPauseButton = '[data-test-id="play-pause-btn"]',
    PreloadFrame = '[data-test-id="preload-image-frame"]',
    RecordButton = '#record-btn',
    SeekInput = 'studio-timeline .seek-input input',
    Slider = 'studio-ui-slider',
    StepBackButton = '[data-test-id="step-back-btn"]',
    StepForwardButton = '[data-test-id="step-forward-btn"]',
    TimelineResizeHandle = 'studio-timeline .resize-handle',
    TimeRuler = 'studio-timeline time-ruler',
    ZoomSliderHandle = 'studio-timeline .zoom studio-ui-slider .bar .handle'
}

export enum ToolbarSelectors {
    Toolbar = 'studio-toolbar',
    BrandLibrary = '#library',
    FeedTool = '#feeds-tool',
    TextTool = '#text-tool',
    ButtonTool = '#button-tool',
    RectangleTool = '#rectangle-tool',
    EllipseTool = '#ellipse-tool',
    WidgetTool = '#widget-tool',
    ImageTool = '#image-tool',
    VideoTool = '#video-tool',
    EffectTool = '#effects-tool'
}

export enum TransitionAnimationMenuSelectors {
    None = '[data-test-id="timeline-animation-none"]',
    Ascend = '[data-test-id="timeline-animation-Ascend"]',
    Descend = '[data-test-id="timeline-animation-Descend"]',
    Fade = '[data-test-id="timeline-animation-Fade"]',
    Flip = '[data-test-id="timeline-animation-Flip"]',
    Slide = '[data-test-id="timeline-animation-Slide"]',
    Scale = '[data-test-id="timeline-animation-Scale"]',
    Blur = '[data-test-id="timeline-animation-Blur"]'
}

export enum TranslationPageSelectors {
    PreviousElementButton = 'previous-element-button',
    NextElementButton = 'next-element-button',
    ToggleAllElements = 'toggle-all-elements'
}

export enum TranslationPanelSelectors {
    CharacterStyling = '.character-styling',
    FeedSelector = 'translation-panel feed-picker',
    Header = 'translation-panel div.title',
    RichTextInput = 'rich-text-input',
    RichTextPreview = 'rich-text-preview',
    SaveButton = '#save-versioned-element-properties',
    TranslationPanel = 'translation-panel'
}

export enum UiSelectors {
    Body = 'ui-body',
    Button = 'ui-button',
    Header = 'ui-header',
    ListCell = 'ui-list-cell',
    Loader = 'ui-loader'
}
