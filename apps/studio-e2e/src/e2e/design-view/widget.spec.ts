import { PropertiesPanelSelectors, PopoverSelectors, WidgetSelectors } from '../../support/selectors';
import { DesignViewParameters } from '../../support/types/commands';

const designViewParameters: DesignViewParameters = {
    creativesetId: '2',
    creativeId: '12',
    designId: '20',
    sizeId: '5',
    versionId: '4'
};

describe('Widget', () => {
    beforeEach(() => {
        setupWidgetUpdateFixtures();
        cy.visitDesignView(designViewParameters);
    });

    context('Properties', () => {
        it('update select property', () => {
            updateWidgetSelectProperty();
        });

        it('update color property from recent colors', () => {
            updateWidgetColorPropertyFromRecentColors();
        });
    });
});

/**
 * Widget element custom properties
 */
function updateWidgetSelectProperty(): void {
    cy.getBySelName('Widget update').click();
    cy.get(PropertiesPanelSelectors.WidgetProperties).should('be.visible');
    cy.get(PropertiesPanelSelectors.SelectWidgetInput).click();
    cy.get(PopoverSelectors.MenuSelectOption).eq(1).click();
    cy.get(PropertiesPanelSelectors.SelectWidgetInput).should('include.text', '2');
}

function updateWidgetColorPropertyFromRecentColors(): void {
    const TARGET_COLOR = 'rgb(0, 0, 255)';
    cy.getBySelName('Widget update').click();
    cy.get(PropertiesPanelSelectors.WidgetProperties).should('be.visible');
    cy.get(PropertiesPanelSelectors.ColorWidgetInput).click();
    cy.get(WidgetSelectors.ColorPalette).first().click();
    cy.get(PropertiesPanelSelectors.ColorWidgetInput).should(
        'have.css',
        'background-color',
        TARGET_COLOR
    );
}
function setupWidgetUpdateFixtures(): void {
    cy.mockAPI();
    cy.fixture('update-widget-in-creativeset/creative-set').then(json => {
        cy.mockOperation('GetCreativeset', { creativeset: json });
    });
}
