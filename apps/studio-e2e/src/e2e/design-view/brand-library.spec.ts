import { getSearchPlaceholderAsString } from '@creative/nodes/helpers';
import { UpdateDesignsAndVersionsDto, UpdateElementDto } from '@domain/api/generated/sapi';
import { AssetReference } from '@domain/creativeset/element-asset';
import { LibraryKind } from '@domain/media-library';
import { getIframeBody } from '../../support/commands';
import { Hotkeys } from '../../support/hotkeys.enum';
import {
    CanvasSelectors,
    DialogSelectors,
    EditElementSelectors,
    ElementPopoverMenuSelectors,
    MediaLibrarySelectors,
    NotificationsSelectors,
    PopoverSelectors,
    PropertiesPanelSelectors,
    ToolbarSelectors
} from '../../support/selectors';
import { DesignViewParameters } from '../../support/types/commands';

enum UpdateElementNames {
    First = 'Rect',
    Second = 'NewName'
}

const designViewParameters: DesignViewParameters = {
    creativesetId: '2',
    creativeId: '12',
    designId: '20',
    sizeId: '5',
    versionId: '4'
};

describe('BrandLibrary', () => {
    beforeEach(() => {
        cy.mockAPI();
    });

    before(() => {
        cy.mockAPI();
        cy.visitDesignView();
    });

    describe('Shortcuts', () => {
        it('should open the BL with the correct filterings', () => {
            cy.hotkey('L'); // Library
            checkIfLibraryOpen();
            cy.hotkey('D'); // Dynamic
            checkIfLibraryOpen(LibraryKind.Feeds);
            cy.hotkey('W'); // Widget
            checkIfLibraryOpen(LibraryKind.Widget);
            cy.hotkey('E'); // Ellipse
            checkIfLibraryOpen(LibraryKind.Ellipse);
            cy.hotkey('R'); // Rectangle
            checkIfLibraryOpen(LibraryKind.Rectangle);
            cy.hotkey('T'); // Text
            checkIfLibraryOpen(LibraryKind.Text);
            cy.hotkey('B'); // Button
            checkIfLibraryOpen(LibraryKind.Button);
            cy.hotkey('I'); // Images
            checkIfLibraryOpen(LibraryKind.Image);
            cy.hotkey('C'); // Videos
            checkIfLibraryOpen(LibraryKind.Video);
            cy.hotkey('F'); // Effects
            checkIfLibraryOpen(LibraryKind.Effects);

            cy.get(ToolbarSelectors.BrandLibrary).click();
            checkIfLibraryOpen();
            cy.get(ToolbarSelectors.FeedTool).click();
            checkIfLibraryOpen(LibraryKind.Feeds);
            cy.get(ToolbarSelectors.WidgetTool).click();
            checkIfLibraryOpen(LibraryKind.Widget);
            cy.get(ToolbarSelectors.EllipseTool).click();
            checkIfLibraryOpen(LibraryKind.Ellipse);
            cy.get(ToolbarSelectors.RectangleTool).click();
            checkIfLibraryOpen(LibraryKind.Rectangle);
            cy.get(ToolbarSelectors.TextTool).click();
            checkIfLibraryOpen(LibraryKind.Text);
            cy.get(ToolbarSelectors.ButtonTool).click();
            checkIfLibraryOpen(LibraryKind.Button);
            cy.get(ToolbarSelectors.ImageTool).click();
            checkIfLibraryOpen(LibraryKind.Image);
            cy.get(ToolbarSelectors.VideoTool).click();
            checkIfLibraryOpen(LibraryKind.Video);
            cy.get(ToolbarSelectors.EffectTool).click();
            checkIfLibraryOpen(LibraryKind.Effects);
        });
    });

    it('Rename', () => {
        cy.fixture('brand-library/update').then(json => {
            cy.mockOperation('UpdateElementInBrandLibrary', json);
        });
        cy.fixture('brand-library/query').then(json => {
            cy.mockOperation('BrandLibraryQuery', json);
        });

        editNameFromDropdown();
        editNameFromHeader();
    });

    describe('Canvas and Library integration', () => {
        before(() => {
            setupWidgetUpdateFixtures();
            cy.visitDesignView(designViewParameters);
        });

        it('should find element in brand library via context menu', () => {
            cy.getBySelName('Widget update').rightclick();
            cy.get(PopoverSelectors.BrandLibraryOptions).click({ force: true });
            cy.get(ElementPopoverMenuSelectors.FindElementInBrandLibrary).click({ force: true });

            cy.get(MediaLibrarySelectors.SearchInput).should('include.value', 'Widget update');
            cy.get(MediaLibrarySelectors.LibraryElementName).should('include.text', 'Widget update');
        });
    });

    describe('Update widget', () => {
        beforeEach(() => {
            setupWidgetUpdateFixtures();
            cy.visitDesignView(designViewParameters);
        });

        it('single', () => {
            updateSingleWidget();
        });

        it('bulk', () => {
            updateMultipleWidgets();
        });
    });

    context.skip('Feeds', () => {
        it('should drag and drop a feeded item on canvas', () => {
            cy.fixture('feed-items').then(json => {
                cy.intercept({ url: /devstoreaccount1\/sfeeds/ }, json);
            });

            cy.hotkey('D');
            cy.get(MediaLibrarySelectors.LibraryElementRow).first().click();
            cy.get(MediaLibrarySelectors.LibraryFeedField).first().click();
            cy.get(MediaLibrarySelectors.LibraryFeedField).first().as('feedField');
            cy.mouseDragAndDrop('@feedField', CanvasSelectors.Canvas);
            cy.getBySelName('Link').should('be.visible');
        });
    });

    describe('Effects', () => {
        before(() => {
            cy.mockAPI();
            cy.visitDesignView();
        });

        it('should successfully import and add effect to canvas from brand library', () => {
            cy.fixture('brand-library/bf-library-widgets').then(json => {
                cy.intercept(/api\/widgets/, json).as('getBfLibraryWidgets');
            });
            cy.fixture('brand-library/create-element-effect').then(json => {
                cy.mockOperation('CreateElementInBrandLibrary', json, {
                    alias: 'createEffectElementMutation'
                });
            });

            assertNonImportedEffects();
            importEffect();

            cy.log('Add effect to canvas');
            cy.get(MediaLibrarySelectors.LibraryElementAddToCanvas).click();
            cy.getBySelName('Test effect 1').should('be.visible');
        });
    });

    describe('Update widgets in creative set', () => {
        beforeEach(() => {
            setupWidgetUpdateFixtures();
            cy.visitDesignView(designViewParameters);
        });

        it('Unsaved widget element', () => {
            updateWidgetsInCreativeSetForUnsavedWidget();
        });
    });
});

function assertNonImportedEffects(): void {
    cy.log('Assert effects library containing non-imported effects');
    cy.hotkey('F');
    cy.wait('@getBfLibraryWidgets');

    cy.get(MediaLibrarySelectors.LibraryElements).should('have.length', 2);
    cy.get(MediaLibrarySelectors.LibraryElements).first().as('effectElement');

    cy.get(MediaLibrarySelectors.LibraryEffectElementImportOverlay).should('exist');
    cy.get('@effectElement').find(MediaLibrarySelectors.LibraryElementControlKebab).should('not.exist');
}

function importEffect(): void {
    cy.log('Import an effect');

    cy.get('@effectElement').click();
    cy.wait('@createEffectElementMutation');

    cy.get(MediaLibrarySelectors.LibraryElements).should('have.length', 2);
    cy.get('@effectElement').should('be.visible');
    cy.get('@effectElement')
        .find(MediaLibrarySelectors.LibraryEffectElementImportOverlay)
        .should('not.exist');

    cy.get('@effectElement')
        .find(MediaLibrarySelectors.LibraryElementControlKebab)
        .invoke('show')
        .click({ force: true });

    cy.get(MediaLibrarySelectors.LibraryElementDelete).should('not.exist');
}

function editElementPropertyLabel(propertyLabel: string, value: string): void {
    cy.get(EditElementSelectors.PropertySection)
        .contains(propertyLabel)
        .should('be.visible')
        .as('propertySection');

    cy.get('@propertySection').click();
    cy.get('.property input').first().as('propertyInput');
    cy.enterInput('@propertyInput', value);
    cy.get('@propertySection').click();
}

function getAllContentBlobReferencesOfWidgetElements(elements: UpdateElementDto[]): string[] {
    return elements
        .filter(({ type }) => type === 'widget')
        .map(
            ({ properties }) =>
                properties.find(({ name }) => name === AssetReference.WidgetContentUrl)?.value ?? ''
        )
        .filter(Boolean);
}

function updateWidgetInLibrary(newLabel: string): void {
    cy.fixture('update-widget-in-creativeset/update-element').then(json => {
        cy.mockOperation('UpdateElementInBrandLibrary', json);
    });

    cy.get(ToolbarSelectors.WidgetTool).should('exist').click();
    cy.get(MediaLibrarySelectors.LibraryElementControls).first().invoke('show').click({ force: true });
    cy.get(MediaLibrarySelectors.LibraryElementEdit).click({ force: true });
    editElementPropertyLabel('TextLabel', newLabel);
    cy.get('.monaco-editor .line-numbers.lh-even').should('be.visible', { timeout: 20000 });
    cy.get(EditElementSelectors.SaveButton).click();
}

function setupWidgetUpdateFixtures(): void {
    cy.mockAPI();
    cy.fixture('update-widget-in-creativeset/creative-set').then(json => {
        cy.mockOperation('GetCreativeset', { creativeset: json });
    });
    cy.fixture('update-widget-in-creativeset/brand-library').then(json => {
        cy.mockOperation('BrandLibraryQuery', json);
    });
}

function editNameFromDropdown(): void {
    cy.log('should edit name - dropdown');
    cy.get(ToolbarSelectors.RectangleTool).click(); // Open BL

    // wait for library tool to load
    cy.getBySel('bl-rectangle').should('be.visible');

    cy.get(MediaLibrarySelectors.LibraryElementControls).invoke('show').click({ force: true });
    cy.get(PopoverSelectors.MenuItems).contains('Rename').click();
    cy.enterInput(MediaLibrarySelectors.LibraryElementInput, UpdateElementNames.First);

    // check if we actually send the correct update
    cy.wait('@UpdateElementInBrandLibrary')
        .its('request.body.variables.element.name')
        .should('eq', UpdateElementNames.First);
}

function editNameFromHeader(): void {
    cy.log('should edit name - header');

    // wait for library tool to load
    cy.getBySel('bl-rectangle').should('be.visible');

    // rename via click on header
    cy.get(MediaLibrarySelectors.LibraryElementName).contains('Rect').should('be.visible');
    cy.get('library-element').click();
    cy.get('library-element').should('have.class', 'selected');
    cy.get(MediaLibrarySelectors.LibraryElementName).click();
    cy.get('.library-element__input input').clear();
    cy.enterInput(MediaLibrarySelectors.LibraryElementInput, UpdateElementNames.Second);
    cy.wait('@UpdateElementInBrandLibrary')
        .its('request.body.variables.element.name')
        .should('eq', UpdateElementNames.Second);
}

function updateSingleWidget(): void {
    const NEW_TEXT_LABEL = 'newTextLabel';
    const NEW_HTML_TEXT = 'new HTML';

    cy.log('should update single widget from library and persist it in the same z-index');

    // Copy (edge case)
    cy.getBySelName('Widget update').click();
    cy.hotkey(Hotkeys.Copy);
    cy.hotkey(Hotkeys.Paste);
    cy.getTimelineElementByIndex(1).click({ force: true });
    cy.hotkey(Hotkeys.Delete);

    // Group (edge case)
    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Group);
    cy.deselect();

    // Update widget
    updateWidgetInLibrary(NEW_TEXT_LABEL);

    cy.wait('@UpdateElementInBrandLibrary')
        .its('request.body.variables.element.properties')
        .then(properties => {
            const updatedProperties = properties.filter(prop => prop.label === NEW_TEXT_LABEL);
            expect(updatedProperties).length(1);
        });

    // Assert
    cy.getDesignViewComponent().then(designViewComponent => {
        cy.spy(designViewComponent.editorStateService.renderer.WidgetRenderer!, 'createWidget').as(
            'widgetsLoaded'
        );
    });
    cy.getBySelName('Widget update (1)').rightclick({ force: true });
    cy.get(PopoverSelectors.BrandLibraryOptions).click({ force: true });
    cy.get(ElementPopoverMenuSelectors.UpdateFromWidgetLibrary).click({ force: true });
    cy.wait('@widgetContentBlob-FOOBAR456');

    cy.get('@widgetsLoaded').should('be.called');

    getIframeBody().should('include.text', NEW_HTML_TEXT);

    cy.getTimelineElementLabelByIndex(0).should('have.value', 'Group');
    cy.getTimelineElementByIndex(1).should('have.class', 'in-group');

    cy.get(PropertiesPanelSelectors.WidgetProperties).should('include.text', NEW_TEXT_LABEL);
}

function updateWidgetsInCreativeSetForUnsavedWidget(): void {
    cy.log('should update unsaved widget in creativeset when dialog is confirmed');

    cy.fixture('update-widget-in-creativeset/put-designs-and-versions').then(fixture => {
        cy.intercept(
            { url: /designs-and-versions/, method: 'PUT' },
            fixture.putDesignsAndVersionsResponse
        ).as('PutDesignsAndVersions');
    });
    cy.get(ToolbarSelectors.WidgetTool).should('exist').click();

    cy.get(MediaLibrarySelectors.LibraryElements).first().dblclick();
    cy.get(MediaLibrarySelectors.LibraryElements)
        .first()
        .find(MediaLibrarySelectors.LibraryElementControls)
        .first()
        .invoke('show')
        .click({ force: true });
    cy.get(MediaLibrarySelectors.LibraryElementUpdateWidget).click({ force: true });
    cy.get(DialogSelectors.Dialog).as('dialog').should('be.visible');
    cy.get(DialogSelectors.ButtonPrimary).click();
    cy.get('@dialog').should('not.exist');
    cy.get(NotificationsSelectors.NotificationsWrapper).should(
        'have.text',
        'The widget has now been updated in all creatives in this creative set.'
    );
}

function updateMultipleWidgets(): void {
    cy.log('should update widget in creativeset when dialog is confirmed');

    cy.fixture('update-widget-in-creativeset/put-designs-and-versions').then(fixture => {
        cy.intercept(
            { url: /designs-and-versions/, method: 'PUT' },
            fixture.putDesignsAndVersionsResponse
        ).as('PutDesignsAndVersions');
    });

    const NEW_HTML_CONTENT = 'new HTML';
    const NEW_TEXT_LABEL = 'newTextLabel';
    const UPDATED_CONTENT_BLOB_REFERENCE =
        'http://storage-emulator:10000/devstoreaccount1/widgets/FOOBAR456.json';

    updateWidgetInLibrary(NEW_TEXT_LABEL);
    cy.wait('@UpdateElementInBrandLibrary', { timeout: 10000 })
        .its('request.body.variables.element.properties')
        .then(properties => {
            const updatedProperties = properties.filter(prop => prop.label === NEW_TEXT_LABEL);
            expect(updatedProperties).length(1);
        });

    // Update widget in creativeset
    cy.get(ToolbarSelectors.WidgetTool).should('exist').click();
    cy.get(MediaLibrarySelectors.LibraryElements)
        .first()
        .find(MediaLibrarySelectors.LibraryElementControls)
        .first()
        .invoke('show')
        .click({ force: true });
    cy.get(MediaLibrarySelectors.LibraryElementUpdateWidget).click({ force: true });
    cy.get(DialogSelectors.Dialog).as('dialog').should('be.visible');
    cy.get(DialogSelectors.ButtonPrimary).click();
    cy.get('@dialog').should('not.exist');

    // Assert
    cy.wait('@PutDesignsAndVersions').then(xhr => {
        const body: UpdateDesignsAndVersionsDto = xhr.request.body;
        expect(body.designs).to.have.length(2);

        // verify that widget code properties are updated on save
        const contentBlobReferences = getAllContentBlobReferencesOfWidgetElements(body.elements);
        const isContentBlobReferencesUpdated = contentBlobReferences.every(
            value => value === UPDATED_CONTENT_BLOB_REFERENCE
        );
        expect(isContentBlobReferencesUpdated).to.equal(true);
    });

    cy.get(NotificationsSelectors.NotificationsWrapper).should(
        'have.text',
        'The widget has now been updated in all creatives in this creative set.'
    );

    getIframeBody().should('include.text', NEW_HTML_CONTENT);

    cy.getBySelName('Widget update').click();

    cy.get(PropertiesPanelSelectors.WidgetProperties).should('include.text', NEW_TEXT_LABEL);
}

function checkIfLibraryOpen(kind?: LibraryKind): void {
    cy.get(MediaLibrarySelectors.SearchInput).should(
        'have.attr',
        'placeholder',
        getSearchPlaceholderAsString(kind)
    );
}
