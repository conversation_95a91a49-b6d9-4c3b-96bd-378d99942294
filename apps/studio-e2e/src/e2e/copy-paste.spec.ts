import { setupImageUploadFixture } from '../fixtures/image-upload.fixture';
import { getIframeBody } from '../support/commands';
import { Hotkeys } from '../support/hotkeys.enum';
import {
    CanvasSelectors,
    ManageViewSelectors,
    MediaLibrarySelectors,
    PopoverSelectors,
    TimelineSelectors,
    ToolbarSelectors
} from '../support/selectors';

describe('CopyPaste', () => {
    before(() => {
        cy.mockAPI();
        cy.clearLocalStorage();
        cy.visitDesignView();
        cy.addTextElement();
    });

    beforeEach(() => {
        cy.mockAPI();
    });

    context('Elements', () => {
        it.skip('should cut and paste', () => {
            cutAndPasteTextElementInNonDefaultVersion();
        });

        it('should copy and paste with menu', () => {
            shouldCopyAndPasteWithMenu();
        });

        it('should copy and paste between sizes', () => {
            shouldCopyBetweenSizes();
        });

        it('should copy and paste between sets', () => {
            shouldCopyAndPasteBetweenSets();
        });

        it('should save copy and paste', () => {
            copyPasteBetweenSizesAfterSave();
        });
    });

    context('Groups', () => {
        it('should test copy groups', () => {
            shouldCopyHiddenGroup();
            shouldKeepOrderOfElementsNested();
        });
    });

    context('Timeline', () => {
        it('should copy/paste and keep order', () => {
            shouldCopyAndPasteAndKeepOrder();
        });
    });
});

function shouldCopyAndPasteWithMenu(): void {
    cy.log('should copy and paste with menu');

    cy.get(CanvasSelectors.Canvas).rightclick();
    cy.get(PopoverSelectors.CopySelection).click();
    cy.get(CanvasSelectors.Canvas).rightclick('topLeft');

    cy.get(':nth-child(1) > .content > .custom-item').as('customItem');
    cy.get('@customItem').click();

    cy.addRectangleElement();
    cy.getBySelName('Rectangle').rightclick();
    cy.get(PopoverSelectors.CopySelection).click();
    cy.get(CanvasSelectors.Canvas).rightclick('topRight');
    cy.get('@customItem').click();

    cy.get(ToolbarSelectors.WidgetTool).click();
    cy.get(MediaLibrarySelectors.LibraryElements).first().should('be.visible').dblclick();
    cy.getBySelName('Big Props2').rightclick();
    cy.get(PopoverSelectors.CopySelection).click();
    cy.get(CanvasSelectors.Canvas).rightclick('bottomLeft');
    cy.get('@customItem').click();

    setupImageUploadFixture();

    cy.dropFile(CanvasSelectors.Canvas, 'assets/logo.png', 'image/png');
    cy.wait('@imageUpload');

    cy.get(CanvasSelectors.Canvas).rightclick();
    cy.get(PopoverSelectors.CopySelection).click();
    cy.get(CanvasSelectors.Canvas).rightclick('bottomRight');
    cy.get('@customItem').click();

    assertElements();
}

function cutAndPasteTextElementInNonDefaultVersion(): void {
    cy.log('Cut and paste text element in non-default version');
    cy.deselect();

    cy.get(ManageViewSelectors.VersionSelect).click({ force: true });
    cy.get('#version-2').click({ force: true });

    cy.getBySelName('Text').click();
    cy.hotkey(Hotkeys.Cut);
    cy.getBySelName('Text').should('not.exist');

    cy.hotkey(Hotkeys.Paste);
    cy.getBySelName('Text').should('be.visible');

    // Go back to default version
    cy.get(ManageViewSelectors.VersionSelect).click({ force: true });
    cy.get('#version-1').click({ force: true });
}

function shouldCopyBetweenSizes(): void {
    cy.log('should copy and paste between sizes');
    cy.selectMultipleElements('Text (1)', 'Rectangle (1)', 'Big Props2 (1)', 'assets/logo (1)');
    cy.hotkey(Hotkeys.Delete);

    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Copy);
    cy.visitDesignView({ creativeId: '2', sizeId: '2', designId: undefined });
    pasteElements();
    cy.setZoom(155);

    assertElements();
}

function shouldCopyAndPasteBetweenSets(): void {
    cy.log('should copy and paste between sets');

    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Copy);

    cy.visitDesignView();
    pasteElements();
    cy.getBySelName('Text').should('exist');
    cy.getBySelName('Rectangle').should('exist');
    cy.getBySelName('Big Props2').should('exist');
    cy.getBySelName('assets/logo').should('exist');
}

function shouldCopyAndPasteAndKeepOrder(): void {
    cy.log('should copy and paste and keep order');
    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Delete);
    cy.addTextElement({ x: 50, y: 20 });
    cy.deselect();
    cy.addTextElement({ x: 50, y: 50 });
    cy.deselect();
    cy.addTextElement({ x: 50, y: 100 });
    cy.deselect();

    cy.get(TimelineSelectors.ElementsNames).eq(2).should('have.value', 'Text');
    cy.get(TimelineSelectors.ElementsNames).eq(1).should('have.value', 'Text (1)');
    cy.get(TimelineSelectors.ElementsNames).eq(0).should('have.value', 'Text (2)');
    cy.getTimelineElementBySelName('Text (2)').click();
    cy.getTimelineElementBySelName('Text (1)').click({ ctrlKey: true });
    cy.getTimelineElementBySelName('Text').click({ ctrlKey: true });

    cy.get(TimelineSelectors.ElementsLeftPanel).eq(0).rightclick('center', { force: true });
    cy.get(PopoverSelectors.CopySelection).click();

    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Delete);

    cy.hotkey(Hotkeys.Paste);
    cy.get(TimelineSelectors.ElementsNames).eq(2).should('have.value', 'Text');
    cy.get(TimelineSelectors.ElementsNames).eq(1).should('have.value', 'Text (1)');
    cy.get(TimelineSelectors.ElementsNames).eq(0).should('have.value', 'Text (2)');
}

function copyPasteBetweenSizesAfterSave(): void {
    cy.log('should save and copy and paste between sizes');
    cy.fixture('post-designs-and-versions').then(fixture => {
        cy.intercept({ url: 'designs-and-versions', method: 'POST' }, fixture).as(
            'PostDesignsAndVersions'
        );
    });

    cy.hotkey(Hotkeys.Save);

    cy.wait('@PostDesignsAndVersions').then(() => {
        cy.hotkey(Hotkeys.SelectAll);
        cy.hotkey(Hotkeys.Copy);
        cy.visitDesignView({ creativeId: '2', sizeId: '2' });
        pasteElements();
        cy.setZoom(155);

        assertElements();
    });
}

function shouldCopyHiddenGroup(): void {
    it('should copy paste hidden grouped element', () => {
        cy.addRectangleElement();
        cy.hotkey(Hotkeys.Group);
        cy.deselect();

        cy.getTimelineElementLabelByIndex(1).as('elementLabelInput');
        cy.enterInput('@elementLabelInput', 'New name');

        cy.getTimelineElementByIndex(0).as('timelineRectangle').should('be.visible');

        cy.get('@timelineRectangle').find(TimelineSelectors.ElementVisibilityToggle).click();

        cy.hotkey(Hotkeys.Copy);
        pasteElements();

        cy.get('@timelineRectangle').click();
        cy.get('@timelineRectangle').should('have.class', 'hidden').and('have.class', 'selected');
        cy.get('@timelineRectangle').find('.name input');
        cy.getTimelineElementLabelByIndex(0).should('have.value', 'New name (1)');
    });
}

function shouldKeepOrderOfElementsNested(): void {
    cy.log('should keep order of elements nested in groups');
    cy.addEllipseElement();
    cy.addRectangleElement();
    cy.hotkey(Hotkeys.Group);

    cy.hotkey(Hotkeys.SelectAll);
    cy.hotkey(Hotkeys.Group);
    cy.hotkey(Hotkeys.Copy);
    cy.hotkey(Hotkeys.Paste);

    cy.get(TimelineSelectors.ElementsNames).eq(1).should('have.value', 'Group (2)');
    cy.get(TimelineSelectors.ElementsNames).eq(3).should('have.value', 'Ellipse (1)');
}

function pasteElements(): void {
    cy.getDesignViewComponent().then(designViewComponent => {
        cy.wrap(designViewComponent['brandLibraryDataService']).its('brandLibrary').should('exist');
    });
    cy.hotkey(Hotkeys.Paste);
}

function assertElements(): void {
    cy.getBySelName('Rectangle').should('have.css', 'color', 'rgb(54, 54, 54)');
    cy.getBySelName('assets/logo').find('image').should('have.attr', 'src');

    cy.getBySelName('Text').should('include.text', 'Type').and('include.text', 'something');

    getIframeBody().should('include.text', 'HTML');
}
